package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.dialog.DlgConfirmacaoObject;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.msdropdown.MsDropDown;
import br.com.celk.component.msdropdown.MsItem;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.temp.interfaces.TempHelper;
import br.com.celk.component.temp.v2.TempHelperV2;
import br.com.celk.component.temp.v2.behavior.TempBehaviorV2;
import br.com.celk.component.temp.v2.behavior.TempFormBehaviorV2;
import br.com.celk.component.temp.v2.store.interfaces.impl.DefaultTempStoreStrategyV2;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.resources.Resources;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.unidadesaude.CiapHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgRemoverCondicaoSaude;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.panel.utils.ScoreNewsCalculator;
import br.com.celk.view.prontuario.basico.ciap.autocomplete.AutoCompleteConsultaCiap;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoPrimarioDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AvaliacaoIntegracaoPcacrDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Doenca;
import br.com.ksisolucoes.vo.basico.IndiceImc;
import br.com.ksisolucoes.vo.basico.RelacaoCinturaQuadril;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDoenca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.prontuario.avaliacao.SisvanAlimentacao;
import br.com.ksisolucoes.vo.prontuario.avaliacao.SisvanDoenca;
import br.com.ksisolucoes.vo.prontuario.avaliacao.SisvanIntercorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxEventBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.DropDownChoice;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.SharedResourceReference;
import org.hibernate.criterion.Restrictions;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.math.MathContext;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class AvaliacaoUnidadePanel extends ProntuarioCadastroPanel {

    private Form<AtendimentoPrimarioDTO> form;
    private List<AtendimentoPrimario> historico;
    private List<UsuarioCadsusDoenca> usuarioCadsusDoencaList;
    private Table tblHistoricoAvaliacoes;
    private Table tblCondicoes;
    private InputField txtDataAvaliacao;
    private AttributeModifier modifierVermelho = new AttributeModifier("style", "color: rgb(255, 102, 102);");
    private WebMarkupContainer dadosAntropometricos;
    private WebMarkupContainer dadosSisvan;
    private WebMarkupContainer sinaisVitais;
    private WebMarkupContainer glicemia;
    private WebMarkupContainer glicemiaForaFaixa;
    private WebMarkupContainer condicoes;
    private RadioButtonGroup radioGroup;
    private WebMarkupContainer containerClassificacaoRisco;
    private String textoImc;
    private String textoSituacaoImc;
    private Label lblSituacaoImc;
    private Label lblImc;
    private Label lblResultadoSinaisVitais;
    private Component txtAltura;
    private Component txtPeso;
    private MsDropDown<ClassificacaoRisco> cbxClassificaoRisco;
    private Label lblAvaliacaoGlicemia;
    private Component txtGlicemia;
    private DropDown<Long> cbxTipoGlicemia;
    private DropDown<Long> cbxTipoGlicemiaFaixa;
    private DropDown<Doenca> dropDownDoenca;
    private DropDown<SisvanAlimentacao> dropDownSisvanAlimentacao;
    private DropDown<SisvanDoenca> dropDownSisvanDoencas;
    private DropDown<SisvanIntercorrencia> dropDownSisvanIntercorrencias;
    private LongField txtPesoNascer;
    private String vacina;
    private InputField txtVacina;
    private DlgRemoverCondicaoSaude dlgRemoverCondicaoSaude;
    private DlgConfirmacaoObject<UsuarioCadsusDoenca> dlgConfirmacao;
    private WebMarkupContainer observacao;
    private WebMarkupContainer registroDiarreia;
    private InputArea txaObservacao;
    private DropDown<String> cbxAlergico;
    private DropDown<Long> dropDownFlagDiarreia;
    private DropDown<Long> dropDownFlagDiarreiaSangue;
    private DropDown<String> dropDownFlagPlanoTratamentoDiarreia;
    private DateChooserAjax dchDataPrimeirosSintomasDiarreia;
    private InputArea txaDescricaoAlergia;
    private InputField txtResultadoExameLaboratorial;
    private Component txtCircunferenciaQuadril;
    private Component txtCircunferenciaAbdominal;
    private Component txtPAS;
    private Component txtPAD;
    private Component txtFreqCardiaca;
    private Component txtFreqRespiratoria;
    private Component txtTemperatura;
    private Component txtSaturacaoOxigenio;
    private Label lblSituacaoCalculoRelacaoCinturaQuadril;
    private Label lblCalculoRelacaoCinturaQuadril;
    private String textoSituacaoCalculoRelacaoCinturaQuadril;
    private String textoCalculoRelacaoCinturaQuadril;
    private AutoCompleteConsultaCiap autoCompleteConsultaCiap;
    private DropDown dropDownSubRisco;
    private DateChooserAjax dchDum;
    private InputField<String> txtPCACR;
    private AbstractAjaxButton btnConsultarPCACR;
    private AbstractAjaxButton btnLimparPCACR;
    private AvaliacaoIntegracaoPcacrDTO pcacrDTO;
    private WebMarkupContainer containerPCACR;



    public AvaliacaoUnidadePanel(String id) {
        super(id, BundleManager.getString("avaliacao"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        getVacinaEmDiaPaciente();
        form = new Form("form", new CompoundPropertyModel(carregarAtendimentoPrimario())) {
            @Override
            protected void onSubmit() {
                try {
                    Date dum = form.getModelObject().getAtendimentoPrimario().getDum();
                    if (dum != null && dum.after(DataUtil.getDataAtual())) {
                        MessageUtil.error(getRequestCycle().find(AjaxRequestTarget.class), this, bundle("dataDumNaoPodeSerMaiorQueAtual"));
                        return;
                    }
                    UsuarioCadsusDado usuarioCadsusDado = getFichaUsuarioCadsusDado();
                    if (usuarioCadsusDado != null) {
                        usuarioCadsusDado.setDum(dum);
                        BOFactoryWicket.save(usuarioCadsusDado);
                    }
                    super.onSubmit();
                } catch (Exception e) {
                    Loggable.log.error(e.getMessage(), e);
                    MessageUtil.error(getRequestCycle().find(AjaxRequestTarget.class), this, e.getMessage());
                }
            }
        };

        AtendimentoPrimarioDTO proxy = on(AtendimentoPrimarioDTO.class);

        form.add(autoCompleteConsultaCiap = new AutoCompleteConsultaCiap(path(proxy.getAtendimentoPrimario().getCiap())));
        autoCompleteConsultaCiap.add(new TempBehaviorV2());
        autoCompleteConsultaCiap.add(new AjaxEventBehavior("onchange"){
            @Override
            protected void onEvent(AjaxRequestTarget target) {
                Ciap ciap = (Ciap) autoCompleteConsultaCiap.getModelObject();
                validarCiapSexoPaciente(target, ciap);
            }
        });

        form.add(containerPCACR = new WebMarkupContainer("containerPCACR"));
        containerPCACR.setOutputMarkupPlaceholderTag(true);

        txtPCACR = new InputField<>("pcacr");
        containerPCACR.add(txtPCACR);

        containerPCACR.add(btnConsultarPCACR = new AbstractAjaxButton("atendimentoPrimario.btnConsultarPCACR") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {

                if (txtPCACR.getComponentValue() == null) {
                    throw new ValidacaoException(bundle("msgPCACRObrigatorio"));
                }

                pcacrDTO =  BOFactory.getBO(AtendimentoFacade.class).getPCACRClassificacao(txtPCACR.getComponentValue());
                processarDadosPCACR( pcacrDTO, target,false);

            }
        });

        containerPCACR.add(btnLimparPCACR = new AbstractAjaxButton("atendimentoPrimario.btnLimparPCACR") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                processarDadosPCACR( null, target,  true);
            }
        });


        boolean mostrarCamposPCACR = habilitarPCACR();
        containerPCACR.setVisible(mostrarCamposPCACR);


        // TODO verificar classificacao
//        autoCompleteConsultaCiap.setRequired(isClassificacaoAtendimentoTipoOutros());
        form.add(txtDataAvaliacao = new InputField(path(proxy.getAtendimentoPrimario().getDataAvaliacao())));
        txtDataAvaliacao.add(new TempBehaviorV2());
        form.add(dchDum = new DateChooserAjax(path(proxy.getAtendimentoPrimario().getDum())));
        dchDum.add(new TempBehaviorV2());
        dchDum.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        if(!getAtendimento().getUsuarioCadsus().isFeminino()){
            dchDum.setEnabled(false);
        }
        WebMarkupContainer helpDum = new WebMarkupContainer("helpDum");
        helpDum.add(new AttributeModifier("title", bundle("msgAjudaDum")));
        helpDum.add(new AttributeModifier("class", "icon info"));
        form.add(helpDum);
        form.add(txtVacina = new InputField<String>("vacinaEmDia", new PropertyModel(this, "vacina")));
        txtVacina.setEnabled(false);

        form.add(dadosAntropometricos = new WebMarkupContainer("dadosAntropometricos"));
        dadosAntropometricos.add(txtPeso = new DoubleField(path(proxy.getAtendimentoPrimario().getPeso())).setMDec(3).setVMax(300D).add(new TempBehaviorV2()));
        dadosAntropometricos.add(txtAltura = new DoubleField(path(proxy.getAtendimentoPrimario().getAltura())).setMDec(1).add(new TempBehaviorV2()));
        txtPeso.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                eventoImc(art);
            }
        });

        txtAltura.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                eventoImc(art);
            }
        });

        dadosAntropometricos.add(new DoubleField(path(proxy.getAtendimentoPrimario().getPerimetroCefalico())).add(new TempBehaviorV2()));
        dadosAntropometricos.add(new DoubleField(path(proxy.getAtendimentoPrimario().getPerimetroPanturrilha())).setMDec(1).add(new TempBehaviorV2()));
        dadosAntropometricos.add(lblImc = new Label("textoImc", new PropertyModel<String>(this, "textoImc")));
        dadosAntropometricos.add(lblSituacaoImc = new Label("textoSituacaoImc", new PropertyModel<String>(this, "textoSituacaoImc")));
        lblImc.setOutputMarkupId(true);
        lblSituacaoImc.setOutputMarkupId(true);



        dadosAntropometricos.add(txtCircunferenciaQuadril = new DoubleField(path(proxy.getAtendimentoPrimario().getCircunferenciaQuadril())).add(new TempBehaviorV2()));
        dadosAntropometricos.add(txtCircunferenciaAbdominal = new DoubleField(path(proxy.getAtendimentoPrimario().getCircunferenciaAbdomen())).add(new TempBehaviorV2()));
        txtCircunferenciaQuadril.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                eventoRelacaoCinturaQuadril(art);
            }
        });

        txtCircunferenciaAbdominal.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                eventoRelacaoCinturaQuadril(art);
            }
        });

        dadosAntropometricos.add(lblCalculoRelacaoCinturaQuadril = new Label("textoCalculoRelacaoCinturaQuadril", new PropertyModel<String>(this, "textoCalculoRelacaoCinturaQuadril")));
        dadosAntropometricos.add(lblSituacaoCalculoRelacaoCinturaQuadril = new Label("textoSituacaoCalculoRelacaoCinturaQuadril", new PropertyModel<String>(this, "textoSituacaoCalculoRelacaoCinturaQuadril")));
        lblCalculoRelacaoCinturaQuadril.setOutputMarkupId(true);
        lblSituacaoCalculoRelacaoCinturaQuadril.setOutputMarkupId(true);

        UsuarioCadsusDado usuarioCadsusDado = getFichaUsuarioCadsusDado();

        try {
            form.add(getDadosSisvan(proxy, usuarioCadsusDado));
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        form.add(sinaisVitais = new WebMarkupContainer("sinaisVitais"));
        sinaisVitais.add(txtPAS = new LongField(path(proxy.getAtendimentoPrimario().getPressaoArterialSistolica())).setLabel(new Model(bundle("pas"))).add(new TempBehaviorV2()));
        sinaisVitais.add(txtPAD = new LongField(path(proxy.getAtendimentoPrimario().getPressaoArterialDiastolica())).setLabel(new Model(bundle("pad"))).add(new TempBehaviorV2()));
        sinaisVitais.add(txtFreqCardiaca = new LongField(path(proxy.getAtendimentoPrimario().getFrequenciaCardiaca())).add(new TempBehaviorV2()));
        sinaisVitais.add(txtFreqRespiratoria = new LongField(path(proxy.getAtendimentoPrimario().getFrequenciaRespiratoria())).add(new TempBehaviorV2()));
        sinaisVitais.add(txtTemperatura = new DoubleField(path(proxy.getAtendimentoPrimario().getTemperatura())).add(new TempBehaviorV2()));
        sinaisVitais.add(txtSaturacaoOxigenio = new LongField(path(proxy.getAtendimentoPrimario().getSaturacaoOxigenio())).add(new TempBehaviorV2()));

        txtPAS.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        txtPAD.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        txtFreqCardiaca.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        txtFreqRespiratoria.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        txtTemperatura.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        txtSaturacaoOxigenio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });

        sinaisVitais.add(lblResultadoSinaisVitais = new Label("lblResultadoSinaisVitais"));
        lblResultadoSinaisVitais.setOutputMarkupId(true);
        lblResultadoSinaisVitais.setDefaultModel(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return "";
            }
        });

        DropDownChoice<AtendimentoPrimario.NivelConsciencia> dropDownNivelConsciencia = DropDownUtil.getIEnumDropDown(path(proxy.getAtendimentoPrimario().getNivelConscienciaAvpu()), AtendimentoPrimario.NivelConsciencia.values(), true, false);
        dropDownNivelConsciencia.add(new TempBehaviorV2());
        dropDownNivelConsciencia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });
        sinaisVitais.add(dropDownNivelConsciencia);

        DropDownChoice<AtendimentoPrimario.UsoOxigenacaoSuplementar> dropDownOxigenacao = DropDownUtil.getIEnumDropDown(path(proxy.getAtendimentoPrimario().getUsoOxigenacaoSuplementar()), AtendimentoPrimario.UsoOxigenacaoSuplementar.values(), true, false);
        dropDownOxigenacao.add(new TempBehaviorV2());
        dropDownOxigenacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                calcularNEWS(target);
            }
        });
        sinaisVitais.add(dropDownOxigenacao);

        form.add(glicemia = new WebMarkupContainer("glicemia"));
        glicemia.add(txtGlicemia = new LongField(path(proxy.getAtendimentoPrimario().getGlicemia())).add(new TempBehaviorV2()));
        txtGlicemia.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                avaliarGlicemia(art);
            }
        });
        cbxTipoGlicemia = getCbxTipoGlicemia(path(proxy.getAtendimentoPrimario().getGlicemiaTipo()), false);
        glicemia.add(cbxTipoGlicemia.add(new TempBehaviorV2()));
        glicemia.add(lblAvaliacaoGlicemia = new Label("lblAvaliacaoGlicemia"));
        lblAvaliacaoGlicemia.setOutputMarkupId(true);

        form.add(glicemiaForaFaixa = new WebMarkupContainer("glicemiaForaFaixa"));
        glicemiaForaFaixa.add(getCbxGlicemiaForaFaixa(path(proxy.getAtendimentoPrimario().getGlicemiaForaFaixa())).add(new TempBehaviorV2()));
        glicemiaForaFaixa.add(getCbxTipoGlicemia(path(proxy.getAtendimentoPrimario().getGlicemiaForaFaixaTipo()), true).add(new TempBehaviorV2()).add(new Tooltip().setText("msgGlicemiaForaFaixa")));

        form.add(condicoes = new WebMarkupContainer("condicoes"));
        condicoes.add(getDropDownCondicoes());
        condicoes.add(new AbstractAjaxButton("btnAdicionarComorbidade") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarCondicao(target);
            }
        });
        condicoes.add(tblCondicoes = new Table("tblCondicoes", getColumnsCondicoes(), getCollectionProviderCondicoes()));
        tblCondicoes.populate();

        form.add(tblHistoricoAvaliacoes = new Table("tblHistoricoAvaliacoes", getColumnsHistoricoAvaliacoes(), getCollectionProviderHistoricoAvaliacoes()));
        tblHistoricoAvaliacoes.populate();
        tblHistoricoAvaliacoes.setScrollY("272px");
        tblHistoricoAvaliacoes.setScrollX("768px");

        form.add(containerClassificacaoRisco = new WebMarkupContainer("containerClassificacaoRisco"));
        containerClassificacaoRisco.add(getCbxClassificacaoRisco(path(proxy.getAtendimentoPrimario().getAtendimento().getClassificacaoRisco())).add(new TempBehaviorV2()));
        containerClassificacaoRisco.add(getDropDownSubRisco(path(proxy.getSubClassificacaoRisco())).add(new TempBehaviorV2()));

        form.add(observacao = new WebMarkupContainer("observacao"));
        observacao.add(txaObservacao = new InputArea(path(proxy.getAtendimentoPrimario().getObservacao())));
        txaObservacao.add(new TempBehaviorV2());

        form.add(registroDiarreia = new WebMarkupContainer("registroDiarreia"));
        registroDiarreia.add(dropDownFlagDiarreia = (DropDown<Long>) DropDownUtil.getNaoSimLongDropDown(path(proxy.getDiarreia())).add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableDiarreia(target);
                new TempHelper().save(target, txaDescricaoAlergia);
            }
        }).add(new TempBehaviorV2()));
        registroDiarreia.add(dropDownFlagDiarreiaSangue = (DropDown<Long>) DropDownUtil.getNaoSimLongDropDown(path(proxy.getAtendimentoMDDA().getComSangue()), true).add(new TempBehaviorV2()));
        registroDiarreia.add(dropDownFlagPlanoTratamentoDiarreia = (DropDown<String>) DropDownUtil.getEnumDropDown(path(proxy.getAtendimentoMDDA().getPlanoTratamento()), AtendimentoMDDA.PlanoTratamento.values(), "").add(new TempBehaviorV2()));
        registroDiarreia.add(dchDataPrimeirosSintomasDiarreia = (DateChooserAjax) new DateChooserAjax(path(proxy.getAtendimentoMDDA().getDataPrimeirosSintomas())).add(new TempBehaviorV2()));
        dchDataPrimeirosSintomasDiarreia.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        registroDiarreia.add(txtResultadoExameLaboratorial = (InputField) new InputField(path(proxy.getAtendimentoMDDA().getResultadoExameLaboratorial())).add(new TempBehaviorV2()));
        add(form);

        carregarAtendimentoMDDA();

        form.add(cbxAlergico = (DropDown<String>) DropDownUtil.getNaoSimDropDown(path(proxy.getAtendimentoPrimario().getFlagAlergico())).add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                avaliarAlergia(target);
                new TempHelper().save(target, txaDescricaoAlergia);
            }
        }).add(new TempBehaviorV2()));
        form.add(txaDescricaoAlergia = new InputArea(path(proxy.getAtendimentoPrimario().getDescricaoAlergia())));
        txaDescricaoAlergia.add(new TempBehaviorV2());

        if (usuarioCadsusDado != null && StringUtils.trimToNull(usuarioCadsusDado.getDescricaoAlergico()) != null) {
            cbxAlergico.setModelObject(RepositoryComponentDefault.SIM);
            txaDescricaoAlergia.setComponentValue(usuarioCadsusDado.getDescricaoAlergico());
            new TempHelper().save(cbxAlergico, RepositoryComponentDefault.SIM);
        }

        if (form.getModelObject().getAtendimentoPrimario().getFlagAlergico() == null) {
            cbxAlergico.setModelObject(RepositoryComponentDefault.NAO);
            new TempHelper().save(cbxAlergico, RepositoryComponentDefault.NAO);
        }

        form.add(new TempFormBehaviorV2(new DefaultTempStoreStrategyV2(getAtendimento(), getIdentificador().toString(), AtendimentoPrimarioDTO.class)));

        if (form.getModelObject().getAtendimentoPrimario().getDataAvaliacao() == null) {
            Date dataAtual = DataUtil.getDataAtual();
            txtDataAvaliacao.setModelObject(dataAtual);
            new TempHelperV2().save(form);
        }

        eventoImc(null);
        avaliarGlicemia(null);
        carregaDropDown(null);
        eventoRelacaoCinturaQuadril(null);
        if (form.getModelObject().getAtendimentoPrimario().getGlicemiaTipo() == null) {
            form.getModelObject().getAtendimentoPrimario().setGlicemiaTipo(AtendimentoPrimario.TIPO_GLICEMIA_JEJUM);
        }

        if (form.getModel().getObject().getAtendimentoPrimario().getPesoNascer() == null && usuarioCadsusDado != null) {
            txtPesoNascer.setModelObject(usuarioCadsusDado.getPesoNascer());
            new TempHelperV2().save(form);
        }

        avaliarAlergia(null);
        enableDiarreia(null);
        if(mostrarCamposPCACR){
            enabledPCACR(txtPCACR.getComponentValue() == null);
        }

    }

    private void getVacinaEmDiaPaciente() {
        boolean isVacinaEmDia = false;
        try {
            isVacinaEmDia = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).isVacinaEmDia(getAtendimento().getUsuarioCadsus());
            if (isVacinaEmDia) {
                vacina = Bundle.getStringApplication("rotulo_sim");
            } else {
                vacina = Bundle.getStringApplication("rotulo_nao");
            }
        } catch (DAOException ex) {
            Logger.getLogger(AvaliacaoPanel.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(AvaliacaoPanel.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void adicionarCondicao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        validarCondicaoSaude();

        UsuarioCadsusDoenca ucd = new UsuarioCadsusDoenca();
        ucd.setDoenca(dropDownDoenca.getComponentValue());
        ucd.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());

        //Foi removido a tabela de condições de saúde do temposrário para não sobrepor a doença gestação quando finaliza o pre-natal
        //dessa forma as doenças devem ser salvas no momento da iserção na tabela.
        ucd = BOFactoryWicket.save(ucd);
        if (usuarioCadsusDoencaList == null) {
            usuarioCadsusDoencaList = new ArrayList<>();
        }
        usuarioCadsusDoencaList.add(ucd);

        if (Doenca.CondicaoEsus.GESTANTE.value().equals(ucd.getDoenca().getCondicaoEsus()))
            habilitaSisvan(true, target);

        limparCondicoesSaude(target);
    }

    private DropDown<Long> getCbxTipoGlicemia(String proxy, boolean foraFaixa) {
        DropDown<Long> cbxTipo= new DropDown<>(proxy);
        cbxTipo.addChoice(null, "");
        cbxTipo.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_JEJUM, bundle("emJejum"));
        cbxTipo.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_POS_PRANDIAL, bundle("posPrandial"));
        cbxTipo.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_PRE_PRANDIAL, bundle("prePrandial"));
        cbxTipo.addChoice(AtendimentoPrimario.TIPO_GLICEMIA_NAO_ESPECIFICADO, bundle("naoEspecificado"));

        cbxTipo.add(new TempBehaviorV2());
        if(!foraFaixa) {
            cbxTipo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    avaliarGlicemia(target);
                }
            });
        }
        return cbxTipo;
    }

    private DropDown<String> getCbxGlicemiaForaFaixa(String proxy) {
        DropDown<String> cbx= new DropDown<>(proxy);
        cbx.addChoice(null, "");
        cbx.addChoice(bundle("lo"),bundle("lo"));
        cbx.addChoice(bundle("hi"),bundle("hi"));
        return cbx;
    }

    private UsuarioCadsusDado getFichaUsuarioCadsusDado() {
        UsuarioCadsusDado usuarioCadsusDado = null;
        try {
            usuarioCadsusDado = (UsuarioCadsusDado) HibernateSessionFactory.getSession().createCriteria(UsuarioCadsusDado.class)
                    .add(Restrictions.eq(VOUtils.montarPath(UsuarioCadsusDado.PROP_CODIGO), getAtendimento().getUsuarioCadsus().getCodigo()))
                    .uniqueResult();

            if (usuarioCadsusDado == null) {
                usuarioCadsusDado = new UsuarioCadsusDado();
                usuarioCadsusDado.setCodigo(getAtendimento().getUsuarioCadsus().getCodigo());;
                BOFactoryWicket.save(usuarioCadsusDado);
            }
        } catch (DAOException | ValidacaoException e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        return usuarioCadsusDado;
    }

    private void validarCondicaoSaude() throws ValidacaoException, DAOException {
        Doenca doenca = dropDownDoenca.getComponentValue();
        if (doenca == null) {
            throw new ValidacaoException(BundleManager.getString("selecioneCondicaoSituacoesSaude"));
        }

        if (CollectionUtils.isNotNullEmpty(usuarioCadsusDoencaList)) {
            for (UsuarioCadsusDoenca item : usuarioCadsusDoencaList) {
                if (item.getDoenca().getCodigo().equals(doenca.getCodigo())) {
                    throw new ValidacaoException(BundleManager.getString("condicaoSituacoesSaudeJaAdicionada"));
                }
            }
        }

        boolean gestante = Doenca.CondicaoEsus.GESTANTE.value().equals(doenca.getCondicaoEsus());
        if (gestante) {
            if (RepositoryComponentDefault.Sexo.MASCULINO.value().equals(getAtendimento().getUsuarioCadsus().getSexo())) {
                throw new ValidacaoException(BundleManager.getString("condicaoSaudeNaoPermitidaSexoPaciente"));
            }
            Long idadeMinimaGestante = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("idadeMinimaGestante");
            if (Data.getIdade(getAtendimento().getUsuarioCadsus().getDataNascimento()) < Coalesce.asLong(idadeMinimaGestante)) {
                throw new ValidacaoException(BundleManager.getString("condicaoSaudeNaoPermitidaIdadePaciente"));
            }
        }
    }

    private void habilitaSisvan(boolean isGestante, AjaxRequestTarget target) {
        txtPesoNascer.setEnabled(isGestante);
        dadosSisvan.setEnabled(isGestante);
        dropDownSisvanDoencas.setEnabled(isGestante);
        dropDownSisvanIntercorrencias.setEnabled(isGestante);

        if (target != null) {
            txtPesoNascer.limpar(target);
            dadosSisvan.setEnabled(isGestante);
            dropDownSisvanDoencas.limpar(target);
            dropDownSisvanIntercorrencias.limpar(target);

            target.add(txtPesoNascer);
            target.add(dadosSisvan);
            target.add(dropDownSisvanDoencas);
            target.add(dropDownSisvanIntercorrencias);
        }
    }

    private void limparCondicoesSaude(AjaxRequestTarget target) {
        dropDownDoenca.limpar(target);
        tblCondicoes.update(target);
    }

    private DropDown getDropDownCondicoes() {
        if (dropDownDoenca == null) {
            dropDownDoenca = new DropDown<Doenca>("condicoes", new Model());
            List<Doenca> lstComorbidade = LoadManager.getInstance(Doenca.class)
                    .addSorter(new QueryCustomSorter(Doenca.PROP_DESCRICAO, QueryCustom.QueryCustomSorter.CRESCENTE))
                    .start().getList();

            dropDownDoenca.addChoice(null, bundle("selecioneCondicaoSituacoesSaude"));

            for (Doenca doenca : lstComorbidade) {
                dropDownDoenca.addChoice(doenca, doenca.getDescricao());
            }
        }
        return dropDownDoenca;
    }

    private List<IColumn> getColumnsCondicoes() {
        List<IColumn> columns = new ArrayList<IColumn>();

        UsuarioCadsusDoenca proxy = on(UsuarioCadsusDoenca.class);

        columns.add(getCustomColumnCondicoes());
        columns.add(createColumn(bundle("condicaoSituacoesSaude"), proxy.getDoenca().getDescricao()));

        return columns;
    }

    private IColumn getCustomColumnCondicoes() {
        return new MultipleActionCustomColumn<UsuarioCadsusDoenca>() {

            @Override
            public void customizeColumn(UsuarioCadsusDoenca rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<UsuarioCadsusDoenca>() {
                    @Override
                    public void action(AjaxRequestTarget target, UsuarioCadsusDoenca modelObject) throws ValidacaoException, DAOException {
                        Doenca doencaRemover = modelObject.getDoenca();
                        List<UsuarioCadsusDoenca> doencasRelacionadas = new ArrayList<UsuarioCadsusDoenca>();

                        for (UsuarioCadsusDoenca item : usuarioCadsusDoencaList) {
                            Doenca doenca = item.getDoenca();
                            item.setUsuarioCadsus(getAtendimento().getUsuarioCadsus());

                            if (!doenca.getCodigo().equals(doencaRemover.getCodigo())) {
                                if (doenca.getDoencaPrincipal() != null && doencaRemover.getCodigo().equals(doenca.getDoencaPrincipal().getCodigo())) {
                                    doencasRelacionadas.add(item);
                                }
                            }
                        }

                        if (CollectionUtils.isNotNullEmpty(doencasRelacionadas)) {
                            removerCondicaoRelacionada(target, modelObject, doencasRelacionadas);
                        } else {
                            initDlgConfirmacaoRemoverCondicao(target, modelObject);
                        }
                    }
                }).setQuestionDialogBundleKey(null);
            }
        };
    }

    private void removerCondicaoRelacionada(AjaxRequestTarget target, UsuarioCadsusDoenca modelObject, List<UsuarioCadsusDoenca> doencasRelacionadas) throws ValidacaoException, DAOException {
        if (dlgRemoverCondicaoSaude == null) {
            getProntuarioController().addWindow(target, dlgRemoverCondicaoSaude = new DlgRemoverCondicaoSaude(getProntuarioController().newWindowId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, UsuarioCadsusDoenca modelObject, List<UsuarioCadsusDoenca> doencasRelacionadas) throws ValidacaoException, DAOException {
                    removerCondicao(target, modelObject);

                    for (UsuarioCadsusDoenca item : doencasRelacionadas) {
                        removerCondicao(target, item);
                    }
                }
            });
        }
        dlgRemoverCondicaoSaude.show(target, modelObject, doencasRelacionadas);
    }

    private void initDlgConfirmacaoRemoverCondicao(AjaxRequestTarget target, UsuarioCadsusDoenca modelObject) {
        if (dlgConfirmacao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacaoObject<UsuarioCadsusDoenca>(WindowUtil.newModalId(this), BundleManager.getString("desejaRealmenteRemover")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, UsuarioCadsusDoenca modelObject) throws ValidacaoException, DAOException {
                    removerCondicao(target, modelObject);
                }
            });
        }
        dlgConfirmacao.show(target, modelObject);
    }

    private void removerCondicao(AjaxRequestTarget target, UsuarioCadsusDoenca modelObject) throws ValidacaoException, DAOException {
        CrudUtils.removerItem(target, tblCondicoes, usuarioCadsusDoencaList, modelObject);
        BOFactoryWicket.delete(modelObject);

        if (Doenca.CondicaoEsus.GESTANTE.value().equals(modelObject.getDoenca().getCondicaoEsus()))
            habilitaSisvan(false, target);
    }

    private ICollectionProvider getCollectionProviderCondicoes() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return usuarioCadsusDoencaList;
            }
        };
    }

    private List<IColumn> getColumnsHistoricoAvaliacoes() {
        List<IColumn> columns = new ArrayList<IColumn>();

        AtendimentoPrimario proxy = on(AtendimentoPrimario.class);

        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getAtendimento().getDataAtendimento())));
        columns.add(new DoubleColumn(bundle("pesoKg"), path(proxy.getPeso())).setCasasDecimais(3));
        columns.add(createColumn(bundle("temperatura"), proxy.getTemperatura()));
        columns.add(createColumn(bundle("pas"), proxy.getPressaoArterialSistolica()));
        columns.add(createColumn(bundle("pad"), proxy.getPressaoArterialDiastolica()));
        columns.add(new DoubleColumn(bundle("alturaCm"), path(proxy.getAltura())).setCasasDecimais(1));
        columns.add(createColumn(bundle("glicemia"), proxy.getGlicemiaFormatada()));
        columns.add(createColumn(bundle("glicemiaForaFaixa"), proxy.getGlicemiaForaFaixaFormatada()));
        columns.add(createColumn(bundle("imc"), proxy.getImc()));
        columns.add(createColumn(bundle("circunferenciaQuadril"), proxy.getCircunferenciaQuadril()));
        columns.add(createColumn(bundle("circunferenciaAbdominal"), proxy.getCircunferenciaAbdomen()));
        if(getAtendimento().getUsuarioCadsus().isFeminino()) {
            columns.add(new DateTimeColumn(bundle("dum"), path(proxy.getDum())).setPattern("dd/MM/yyyy"));
        }
        if(habilitarPCACR()){
            columns.add(createColumn(bundle("pcacr"), proxy.getPropIntegracaoPcacrFormatado()));
        }


        return columns;
    }

    private ICollectionProvider getCollectionProviderHistoricoAvaliacoes() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return historico;
            }
        };
    }

    private AtendimentoPrimarioDTO carregarAtendimentoPrimario() {
        AtendimentoPrimarioDTO dto = new AtendimentoPrimarioDTO();
        try {
            dto = BOFactoryWicket.getBO(AtendimentoFacade.class).carregarDadosAtendimentoPrimario(getAtendimento().getUsuarioCadsus().getCodigo(), getAtendimento());
            historico = dto.getAtendimentoPrimarioList();
            usuarioCadsusDoencaList = dto.getUsuarioCadsusDoencaList();
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        if (dto.getAtendimentoPrimario() == null) {
            dto.setAtendimentoPrimario(new AtendimentoPrimario());
            dto.getAtendimentoPrimario().setAtendimento(getAtendimento());
        }

        return dto;
    }

    private void eventoImc(AjaxRequestTarget target) {
        textoImc = "IMC: ";
        textoSituacaoImc = "";

        Long menorIdade = LoadManager.getInstance(IndiceImc.class)
                .addGroup(new QueryCustom.QueryCustomGroup(IndiceImc.PROP_IDADE_INICIAL, BuilderQueryCustom.QueryGroup.MIN))
                .start().getVO();
        if (getAtendimento().getUsuarioCadsus().getIdadeEmMeses() >= menorIdade) {
            Double imc = null;
            IndiceImc indiceImc = null;
            double altura = Coalesce.asDouble(form.getModelObject().getAtendimentoPrimario().getAltura(), 0D);
            if (altura != 0D && form.getModelObject().getAtendimentoPrimario().getPeso() != null) {
                imc = CalculosUtil.calculoImc((altura / 100), form.getModelObject().getAtendimentoPrimario().getPeso());
                textoImc = textoImc + Valor.adicionarFormatacaoMonetaria(imc);
                boolean gestante = UsuarioCadsusHelper.isGestante(getAtendimento().getUsuarioCadsus().getCodigo());
                Long idadeGestacional = null;
                if (gestante) {
                    PreNatal preNatal = form.getModel().getObject().getPreNatal();
                    if (preNatal != null && preNatal.getDataUltimaMenstruacao() != null) {
                        idadeGestacional = Data.calcularIdadeGestacional(preNatal.getDataUltimaMenstruacao(), getAtendimento().getDataChegada());
                    }
                }

                LoadManager load = LoadManager.getInstance(IndiceImc.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_FAIXA_INICIAL, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, imc))
                        .addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_FAIXA_FINAL, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, imc))
                        .addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_SEXO, BuilderQueryCustom.QueryParameter.IGUAL, getAtendimento().getUsuarioCadsus().getSexo(), HQLHelper.RESOLVE_CHAR_TYPE, getAtendimento().getUsuarioCadsus().getSexo()));
                if (gestante) {
                    load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_GESTANTE, gestante ? RepositoryComponentDefault.SIM_LONG : RepositoryComponentDefault.NAO_LONG));
                    load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_IDADE_GESTACIONAL, BuilderQueryCustom.QueryParameter.IGUAL, idadeGestacional));
                } else {
                    load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_IDADE_INICIAL, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, getAtendimento().getUsuarioCadsus().getIdadeEmMeses()));
                    load.addParameter(new QueryCustom.QueryCustomParameter(IndiceImc.PROP_IDADE_FINAL, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, getAtendimento().getUsuarioCadsus().getIdadeEmMeses()));
                }

                List<IndiceImc> lstIndiceImc = load.start().getList();

                if (lstIndiceImc.size() < 1 || lstIndiceImc.isEmpty()) {
                    textoSituacaoImc = BundleManager.getString("desconhecido");
                } else {
                    textoSituacaoImc = lstIndiceImc.get(0).getSituacao();
                    indiceImc = lstIndiceImc.get(0);
                }
            }
            form.getModelObject().getAtendimentoPrimario().setImc(imc);
            form.getModelObject().getAtendimentoPrimario().setIndiceImc(indiceImc);
            new TempHelperV2().save(form);
        }

        if (target != null) {
            target.add(lblSituacaoImc);
            target.add(lblImc);
        }
    }

    private void eventoRelacaoCinturaQuadril(AjaxRequestTarget target) {
        textoCalculoRelacaoCinturaQuadril = "Relação: ";
        textoSituacaoCalculoRelacaoCinturaQuadril = "";

        Long menorIdade = LoadManager.getInstance(RelacaoCinturaQuadril.class)
                .addGroup(new QueryCustom.QueryCustomGroup(RelacaoCinturaQuadril.PROP_IDADE_INICIAL, BuilderQueryCustom.QueryGroup.MIN))
                .start().getVO();
        if (getAtendimento().getUsuarioCadsus().getIdadeEmMeses() >= menorIdade && getAtendimento().getUsuarioCadsus().getSexo() != null) {
            Double calculoRelacaoCinturaQuadril = null;
            RelacaoCinturaQuadril relacaoCinturaQuadril = null;
            Double quadril = Coalesce.asDouble(form.getModelObject().getAtendimentoPrimario().getCircunferenciaQuadril(), 0D).doubleValue();
            Double abdomen = Coalesce.asDouble(form.getModelObject().getAtendimentoPrimario().getCircunferenciaAbdomen(), 0D).doubleValue();
            if (quadril != 0D && abdomen != 0D) {
                calculoRelacaoCinturaQuadril = new Dinheiro(abdomen, MathContext.DECIMAL128).dividir(quadril).doubleValue();
                textoCalculoRelacaoCinturaQuadril = textoCalculoRelacaoCinturaQuadril + Valor.adicionarFormatacaoMonetaria(calculoRelacaoCinturaQuadril);
                List<RelacaoCinturaQuadril> lstRelacaoCinturaQuadril = LoadManager.getInstance(RelacaoCinturaQuadril.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(RelacaoCinturaQuadril.PROP_FAIXA_INICIAL, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, calculoRelacaoCinturaQuadril))
                        .addParameter(new QueryCustom.QueryCustomParameter(RelacaoCinturaQuadril.PROP_FAIXA_FINAL, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, calculoRelacaoCinturaQuadril))
                        .addParameter(new QueryCustom.QueryCustomParameter(RelacaoCinturaQuadril.PROP_SEXO, BuilderQueryCustom.QueryParameter.IGUAL, getAtendimento().getUsuarioCadsus().getSexo()))
                        .addParameter(new QueryCustom.QueryCustomParameter(RelacaoCinturaQuadril.PROP_IDADE_INICIAL, BuilderQueryCustom.QueryParameter.MENOR_IGUAL, getAtendimento().getUsuarioCadsus().getIdadeEmMeses()))
                        .addParameter(new QueryCustom.QueryCustomParameter(RelacaoCinturaQuadril.PROP_IDADE_FINAL, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, getAtendimento().getUsuarioCadsus().getIdadeEmMeses()))
                        .start().getList();

                if (lstRelacaoCinturaQuadril.size() > 1 || lstRelacaoCinturaQuadril.isEmpty()) {
                    textoSituacaoCalculoRelacaoCinturaQuadril = BundleManager.getString("desconhecido");
                } else {
                    textoSituacaoCalculoRelacaoCinturaQuadril = lstRelacaoCinturaQuadril.get(0).getSituacao();
                    relacaoCinturaQuadril = lstRelacaoCinturaQuadril.get(0);
                }
            }
            form.getModelObject().getAtendimentoPrimario().setCalculoRelacaoCinturaQuadril(calculoRelacaoCinturaQuadril);
            form.getModelObject().getAtendimentoPrimario().setRelacaoCinturaQuadril(relacaoCinturaQuadril);
            new TempHelperV2().save(form);
        }

        if (target != null) {
            target.add(lblSituacaoCalculoRelacaoCinturaQuadril);
            target.add(lblCalculoRelacaoCinturaQuadril);
        }
    }

    private MsDropDown<ClassificacaoRisco> getCbxClassificacaoRisco(String id) {
        if (cbxClassificaoRisco == null) {
            cbxClassificaoRisco = new MsDropDown(id);

            List<ClassificacaoRisco> lstClassificacao = LoadManager.getInstance(ClassificacaoRisco.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ClassificacaoRisco.PROP_ATIVO), RepositoryComponentDefault.ATIVO))
                    .addSorter(new QueryCustom.QueryCustomSorter(ClassificacaoRisco.PROP_NIVEL_GRAVIDADE, QueryCustom.QueryCustomSorter.DECRESCENTE))
                    .start().getList();

            for (ClassificacaoRisco classificacaoRisco : lstClassificacao) {
                cbxClassificaoRisco.addChoice(new MsItem(classificacaoRisco, classificacaoRisco.getDescricao(), urlFor(new SharedResourceReference(Resources.class, classificacaoRisco.getCaminhoImagem()), null).toString()));
            }

            cbxClassificaoRisco.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubRisco.removeAllChoices();
                    if  (target != null) {
                        carregaDropDown(target);
                    }
                }
            });
        }
        return cbxClassificaoRisco;
    }

    private void carregaDropDown(AjaxRequestTarget target){
        List<SubClassificacaoRisco> subClassificacaoRiscoList = LoadManager.getInstance(SubClassificacaoRisco.class)
                .addProperties(new HQLProperties(SubClassificacaoRisco.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(SubClassificacaoRisco.PROP_CLASSIFICACAO_RISCO, cbxClassificaoRisco.getComponentValue()))
                .addSorter(new QueryCustomSorter(SubClassificacaoRisco.PROP_VALOR, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(subClassificacaoRiscoList)) {
            dropDownSubRisco.addChoice(null,"");
            for (SubClassificacaoRisco subClassificacaoRisco : subClassificacaoRiscoList) {
                dropDownSubRisco.addChoice(subClassificacaoRisco, subClassificacaoRisco.getDescricao());
            }
        }
        if (target != null) {
            target.add(dropDownSubRisco);
        }

        new TempHelperV2().save(form);
    }

    private DropDown<SubClassificacaoRisco> getDropDownSubRisco(String id){
        dropDownSubRisco = new DropDown(id);
        dropDownSubRisco.addChoice(null,"");
        return dropDownSubRisco;
    }

    private void avaliarGlicemia(AjaxRequestTarget target){
        try{
            IParameterModuleContainer ipmc = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE);
            Long tipoGlicemia = form.getModelObject().getAtendimentoPrimario().getGlicemiaTipo();
            Long glicemiaInformada = form.getModelObject().getAtendimentoPrimario().getGlicemia();

            if (AtendimentoPrimario.TIPO_GLICEMIA_JEJUM.equals(tipoGlicemia)) {
                Long glicemiaPosJejumInicial = ipmc.getParametro("GlicemiaPosJejumInicial");
                Long glicemiaPosJejumFinal = ipmc.getParametro("GlicemiaPosJejumFinal");
                atualizaLabelGlicemia(glicemiaPosJejumInicial, glicemiaPosJejumFinal, glicemiaInformada);

            } else if (AtendimentoPrimario.TIPO_GLICEMIA_PRE_PRANDIAL.equals(tipoGlicemia)) {
                Long glicemiaPosPrandialInicial = ipmc.getParametro("GlicemiaPrePrandialInicial");
                Long glicemiaPosPrandialFinal = ipmc.getParametro("GlicemiaPrePrandialFinal");
                atualizaLabelGlicemia(glicemiaPosPrandialInicial, glicemiaPosPrandialFinal, glicemiaInformada);

            } else if (AtendimentoPrimario.TIPO_GLICEMIA_POS_PRANDIAL.equals(tipoGlicemia)) {
                Long glicemiaPosPrandialInicial = ipmc.getParametro("GlicemiaPosPrandialInicial");
                Long glicemiaPosPrandialFinal = ipmc.getParametro("GlicemiaPosPrandialFinal");
                atualizaLabelGlicemia(glicemiaPosPrandialInicial, glicemiaPosPrandialFinal, glicemiaInformada);

            } else if (AtendimentoPrimario.TIPO_GLICEMIA_NAO_ESPECIFICADO.equals(tipoGlicemia)) {
                atualizaLabelGlicemia(null, null, glicemiaInformada);

            } else {
                atualizaLabelGlicemia(null, null, null);
            }

            if (target != null) {
                target.add(lblAvaliacaoGlicemia);
            }
        } catch (DAOException ex){
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void atualizaLabelGlicemia(Long glicemiaInicial, Long glicemiaFinal, Long glicemiaInformada) {
        if (Coalesce.asLong(glicemiaInformada) == 0L) {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return "";
                }
            });
            setLabelNormal();
        } else if (glicemiaInicial == null || glicemiaFinal == null) {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return bundle("desconhecido");
                }
            });
            setLabelNormal();
        } else if (glicemiaInformada >= glicemiaInicial && glicemiaInformada <= glicemiaFinal) {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return bundle("normal");
                }
            });
            setLabelNormal();
        } else {
            lblAvaliacaoGlicemia.setDefaultModel(new LoadableDetachableModel<String>() {

                @Override
                protected String load() {
                    return bundle("foraNormalidade");
                }
            });
            setLabelVermelho();
        }
    }

    private void setLabelNormal() {
        if (lblAvaliacaoGlicemia.getBehaviors().contains(modifierVermelho)) {
            lblAvaliacaoGlicemia.remove(modifierVermelho);
        }
    }

    private void setLabelVermelho() {
        if (!lblAvaliacaoGlicemia.getBehaviors().contains(modifierVermelho)) {
            lblAvaliacaoGlicemia.add(modifierVermelho);
        }
    }

    private WebMarkupContainer getDadosSisvan(AtendimentoPrimarioDTO proxy, UsuarioCadsusDado usuarioCadsusDado) throws ValidacaoException {
        Boolean enableSisvan = Boolean.FALSE;
        Boolean enableBolsaFamilia = Boolean.FALSE;
        Integer idadeParaAcompanhamentoAnual = null;
        Integer idadeParaValidarAlimentacao = null;
        Long idadeEmMeses = null;

        try {
            idadeEmMeses = Data.getIdadeEmMeses(getAtendimento().getUsuarioCadsus().getDataNascimento());
            idadeParaAcompanhamentoAnual = (Integer) BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("idadeParaAcompanhamentoAnual");
            idadeParaValidarAlimentacao = (Integer) BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("idadeParaValidarAlimentacao");
            enableSisvan = usuarioCadsusDado != null && RepositoryComponentDefault.SIM_LONG.equals(usuarioCadsusDado.getGestante())
                    || idadeEmMeses < idadeParaAcompanhamentoAnual;
            if (getAtendimento().getEnderecoDomicilio() != null) {
                EnderecoDomicilio enderecoDomicilio = LoadManager.getInstance(EnderecoDomicilio.class)
                        .addProperty(EnderecoDomicilio.PROP_FLAG_BOLSA_FAMILIA)
                        .setId(getAtendimento().getEnderecoDomicilio().getCodigo())
                        .start().getVO();

                enableBolsaFamilia = RepositoryComponentDefault.SIM.equals(enderecoDomicilio.getFlagBolsaFamilia())
                        && idadeEmMeses < idadeParaValidarAlimentacao;
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        dadosSisvan = new WebMarkupContainer("dadosSisvan");
        dadosSisvan.setOutputMarkupId(true);
        dadosSisvan.add(getDropDownSisvanAlimentacao(path(proxy.getAtendimentoPrimario().getSisvanAlimentacao()), idadeParaValidarAlimentacao, idadeEmMeses, enableBolsaFamilia).add(new TempBehaviorV2()));
        dadosSisvan.add(txtPesoNascer = new LongField(path(proxy.getAtendimentoPrimario().getPesoNascer())));
        txtPesoNascer.setEnabled(enableSisvan);
        txtPesoNascer.add(new TempBehaviorV2());

        dadosSisvan.add(getDropDownSisvanDoencas(path(proxy.getAtendimentoPrimario().getSisvanDoenca()), enableSisvan).add(new TempBehaviorV2()));
        dadosSisvan.add(getDropDownSisvanIntecorrencias(path(proxy.getAtendimentoPrimario().getSisvanIntercorrencia()), enableSisvan).add(new TempBehaviorV2()));

        return dadosSisvan;
    }

    private DropDown<SisvanAlimentacao> getDropDownSisvanAlimentacao(String id, Integer idadeParaValidarAlimentacao, Long idadeEmMeses, boolean enableBolsaFamilia) {
        if (dropDownSisvanAlimentacao == null) {
            dropDownSisvanAlimentacao = new DropDown<SisvanAlimentacao>(id);
            dropDownSisvanAlimentacao.addChoice(null, "");

            if (idadeEmMeses >= idadeParaValidarAlimentacao && !enableBolsaFamilia) {
                dropDownSisvanAlimentacao.setEnabled(false);
                return dropDownSisvanAlimentacao;
            }

            List<SisvanAlimentacao> list = LoadManager.getInstance(SisvanAlimentacao.class)
                    .addProperties(new HQLProperties(SisvanAlimentacao.class).getProperties())
                    .addSorter(new QueryCustomSorter(SisvanAlimentacao.PROP_CODIGO_SISVAN, QueryCustomSorter.CRESCENTE))
                    .start().getList();

            for (SisvanAlimentacao sisvanAlimentacao : list) {
                dropDownSisvanAlimentacao.addChoice(sisvanAlimentacao, sisvanAlimentacao.getDescricaoAlimentacao());
            }
        }
        return dropDownSisvanAlimentacao;
    }

    private DropDown<SisvanDoenca> getDropDownSisvanDoencas(String id, Boolean enableSisvan) {
        if (dropDownSisvanDoencas == null) {
            dropDownSisvanDoencas = new DropDown<SisvanDoenca>(id);
            dropDownSisvanDoencas.addChoice(null, "");

            if (!enableSisvan) {
                dropDownSisvanDoencas.setEnabled(false);
            }

            List<SisvanDoenca> list = LoadManager.getInstance(SisvanDoenca.class)
                    .addProperties(new HQLProperties(SisvanDoenca.class).getProperties())
                    .addSorter(new QueryCustomSorter(SisvanDoenca.PROP_CODIGO_SISVAN, QueryCustomSorter.CRESCENTE))
                    .start().getList();

            for (SisvanDoenca sisvanDoenca : list) {
                dropDownSisvanDoencas.addChoice(sisvanDoenca, sisvanDoenca.getDescricaoDoenca());
            }
        }
        return dropDownSisvanDoencas;
    }

    private DropDown<SisvanIntercorrencia> getDropDownSisvanIntecorrencias(String id, Boolean enableSisvan) {
        if (dropDownSisvanIntercorrencias == null) {
            dropDownSisvanIntercorrencias = new DropDown<SisvanIntercorrencia>(id);
            dropDownSisvanIntercorrencias.addChoice(null, "");

            if (!enableSisvan) {
                dropDownSisvanIntercorrencias.setEnabled(false);
            }

            List<SisvanIntercorrencia> list = LoadManager.getInstance(SisvanIntercorrencia.class)
                    .addProperties(new HQLProperties(SisvanIntercorrencia.class).getProperties())
                    .addSorter(new QueryCustomSorter(SisvanIntercorrencia.PROP_CODIGO_SISVAN, QueryCustomSorter.CRESCENTE))
                    .start().getList();

            for (SisvanIntercorrencia sisvanIntercorrencia : list) {
                dropDownSisvanIntercorrencias.addChoice(sisvanIntercorrencia, sisvanIntercorrencia.getDescricaoIntercorrencia());
            }
        }
        return dropDownSisvanIntercorrencias;
    }

    private void avaliarAlergia(AjaxRequestTarget target) {
        boolean enable = RepositoryComponentDefault.SIM.equals(form.getModelObject().getAtendimentoPrimario().getFlagAlergico());
        txaDescricaoAlergia.setEnabled(enable);
        if (target != null) {
            if (!enable) {
                txaDescricaoAlergia.limpar(target);
            } else {
                target.add(txaDescricaoAlergia);
            }
        }
    }

    private void enableDiarreia(AjaxRequestTarget target) {
        boolean enable = RepositoryComponentDefault.SIM_LONG.equals(form.getModelObject().getDiarreia());
        dropDownFlagDiarreiaSangue.setEnabled(enable);
        dropDownFlagPlanoTratamentoDiarreia.setEnabled(enable);
        dchDataPrimeirosSintomasDiarreia.setEnabled(enable);
        txtResultadoExameLaboratorial.setEnabled(enable);
        if (target != null) {
            if (!enable) {
                dropDownFlagDiarreia.limpar(target);
                dropDownFlagDiarreiaSangue.limpar(target);
                dchDataPrimeirosSintomasDiarreia.limpar(target);
                txtResultadoExameLaboratorial.limpar(target);
                dropDownFlagPlanoTratamentoDiarreia.limpar(target);
            } else {
                target.add(dropDownFlagDiarreiaSangue);
                target.add(dropDownFlagPlanoTratamentoDiarreia);
                target.add(dchDataPrimeirosSintomasDiarreia);
                target.add(txtResultadoExameLaboratorial);
            }
        }
    }

    private void carregarAtendimentoMDDA() {
        AtendimentoMDDA proxy = on(AtendimentoMDDA.class);
        List<AtendimentoMDDA> atendimentoMDDAList = LoadManager.getInstance(AtendimentoMDDA.class)
                .addProperties(new HQLProperties(AtendimentoMDDA.class).getProperties())
                .addParameter(new QueryCustomParameter(path(proxy.getAtendimento().getAtendimentoPrincipal()), getAtendimento().getAtendimentoPrincipal()))
                .addSorter(new QueryCustomSorter(path(proxy.getCodigo()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(atendimentoMDDAList)) {
            form.getModelObject().setAtendimentoMDDA(atendimentoMDDAList.get(0));
            form.getModelObject().setDiarreia(RepositoryComponentDefault.SIM_LONG);
            new TempHelperV2().save(form);
        }
    }

    public void validarCiapSexoPaciente(AjaxRequestTarget target, Ciap ciap) {
        boolean ciapInvalido = CiapHelper.isInvalidoCiapSexoPaciente(ciap, getAtendimento());
        if (ciapInvalido) {
            String msg = Bundle.getStringApplication("ciap_nao_valido_sexo_paciente");
            MessageUtil.warn(target, this, msg);
        }
    }

    private void calcularNEWS(AjaxRequestTarget target) {
        AtendimentoPrimarioDTO proxy = form.getModelObject();
        Long score = ScoreNewsCalculator.calcularNEWS(proxy);
        proxy.getAtendimentoPrimario().setScoreNewsSinaisVitais(score);

        mostrarResultado(score, target);
    }

    private void mostrarResultado(Long score, AjaxRequestTarget target) {
        String situacaoNews = bundle(ScoreNewsCalculator.determinarRisco(score));

        lblResultadoSinaisVitais.setDefaultModelObject(situacaoNews);
        if (target != null) {
            target.add(lblResultadoSinaisVitais);
        }
    }

    private boolean habilitarPCACR(){
      try {
            String permiteInformarCodigoPcacr = (String) BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("permiteInformarCodigoPcacr");
            return RepositoryComponentDefault.SIM.equals(permiteInformarCodigoPcacr);
      } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
            return false;
      }
    }

    private void preencherDadosVitais(AvaliacaoIntegracaoPcacrDTO pcacrDTO) {
        if(pcacrDTO != null){

            form.getModelObject().getAtendimentoPrimario().setFrequenciaRespiratoria(pcacrDTO.getFrAsLong());
            form.getModelObject().getAtendimentoPrimario().setFrequenciaCardiaca(pcacrDTO.getFcAsLong());
            form.getModelObject().getAtendimentoPrimario().setPressaoArterialSistolica(pcacrDTO.getPasAsLong());
            form.getModelObject().getAtendimentoPrimario().setPressaoArterialDiastolica(pcacrDTO.getPadAsLong());
            form.getModelObject().getAtendimentoPrimario().setSaturacaoOxigenio(pcacrDTO.getSaturacaoOxigenioAsLong());
            form.getModelObject().getAtendimentoPrimario().setTemperatura(pcacrDTO.getTemperaturaAsDouble());
            form.getModelObject().getAtendimentoPrimario().setGlicemia(pcacrDTO.getHgtAsLong());
            form.getModelObject().getAtendimentoPrimario().setPeso(pcacrDTO.getPesoAsDouble());
            form.getModelObject().getAtendimentoPrimario().setObservacao(pcacrDTO.getAnamnese());
            form.getModelObject().getAtendimentoPrimario().setPcacr(txtPCACR.getComponentValue());
            new TempHelperV2().save(form);
        }
    }

    private void enabledPCACR(boolean habilitar) {
        txtFreqRespiratoria.setEnabled(habilitar);
        txtFreqCardiaca.setEnabled(habilitar);
        txtGlicemia.setEnabled(habilitar);
        txtPAS.setEnabled(habilitar);
        txtPAD.setEnabled(habilitar);
        txtSaturacaoOxigenio.setEnabled(habilitar);
        txtTemperatura.setEnabled(habilitar);
        txtPeso.setEnabled(habilitar);
        txaObservacao.setEnabled(habilitar);
        txtPCACR.setEnabled(habilitar);
    }


    private void atualizarComponentes(AjaxRequestTarget target) {
        if (target != null) {
            target.add(form);
        }
    }

    private void limpar(AjaxRequestTarget target )  {
        Long vlZero = 0L;

        form.getModelObject().getAtendimentoPrimario().setFrequenciaRespiratoria(vlZero);
        form.getModelObject().getAtendimentoPrimario().setFrequenciaCardiaca(vlZero);
        form.getModelObject().getAtendimentoPrimario().setPressaoArterialSistolica(vlZero);
        form.getModelObject().getAtendimentoPrimario().setPressaoArterialDiastolica(vlZero);
        form.getModelObject().getAtendimentoPrimario().setSaturacaoOxigenio(vlZero);
        form.getModelObject().getAtendimentoPrimario().setTemperatura(0.0D);
        form.getModelObject().getAtendimentoPrimario().setGlicemia(vlZero);
        form.getModelObject().getAtendimentoPrimario().setPeso(0.0D);
        form.getModelObject().getAtendimentoPrimario().setObservacao("");
        form.getModelObject().getAtendimentoPrimario().setPcacr("");
        if(target != null){
          txtPCACR.limpar(target);
        }
        new TempHelperV2().save(form);

    }

    private void processarDadosPCACR(AvaliacaoIntegracaoPcacrDTO pcacrDTO, AjaxRequestTarget target, boolean limparCampos) {
        if(limparCampos){
            limpar(target);
        }else{
            preencherDadosVitais(pcacrDTO);
        }

        enabledPCACR(limparCampos);
        atualizarComponentes(target);
    }


}


