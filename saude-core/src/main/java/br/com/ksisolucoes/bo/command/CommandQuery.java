package br.com.ksisolucoes.bo.command;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.command.CommandCustomSession;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.ThreadHelper;
import br.com.ksisolucoes.util.ProcessTime;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Collection;
import java.util.UUID;

public abstract class CommandQuery<T> extends CommandStart<T> implements CommandCustomSession<T> {

    private static final double SECONDS_MILISECONDS_DECIMALS = 1000.0;
    private static final int MIN_LOGGABLE_RECORDS = 1000;
    private static final int MIN_SECONDS_LOGGABLE_RECORDS = 10;
    protected transient Session session;
    protected transient HQLHelper hql;
    private transient Query query;
    private boolean writeCallerClass = false;
    private String callerClassName;
    private transient Long maxResults;

    public CommandQuery() {
    }

    public CommandQuery<T> leitura() {
        this.session = HibernateSessionFactory.getSessionLeitura();
        return this;
    }

    @Override
    public T start(Session session) throws DAOException, ValidacaoException {
        this.session = session;
        start();
        return (T) this;
    }

    @Override
    public T start(Session session, Long maxResults) throws DAOException, ValidacaoException {
        this.session = session;
        this.maxResults = maxResults;
        start();
        return (T) this;
    }

    @Override
    public T start(Long maxResults) throws DAOException, ValidacaoException {
        this.maxResults = maxResults;
        start();
        return (T) this;
    }

    @Override
    public T start() throws DAOException, ValidacaoException {
        /*
         * PEGA A SESSION
         * --------------
         *---------------------------------------------------------------------*/
        try {
            this.session = getSession();
            /*--------------------------------------------------------------------*/

            /*
             * INSTANCIA O HQLHELPER
             * ---------------------
             *---------------------------------------------------------------------*/
            this.hql = new HQLHelper();
            /*--------------------------------------------------------------------*/

            this.createQuery(hql);

            if (!Coalesce.asString(hql.getQuery()).equals("")) {
                String comment = "";
                if (this.getCallerClassName() != null) {
                    comment = this.getCallerClassName();
                } else {
                    comment = this.getClass().getCanonicalName();
                }

                if (this.hql.isUseSQL()) {
                    this.query = this.session.createSQLQuery(this.customHQLHelperBeforeExecute(this.hql).getQuery());
                } else {
                    this.query = this.session.createQuery(this.customHQLHelperBeforeExecute(this.hql).getQuery());
                }

                if (this.maxResults != null) {
                    query.setMaxResults(this.maxResults.intValue());
                }

                this.setParameters(hql, query);

                String[] commentArray = Coalesce.asString(comment).split("\\.");
                comment = commentArray[commentArray.length - 1];
                query.setComment(comment);

                hql.applyRestrictions(query);

                this.customQuery(this.query);

                ProcessTime timeQuery = new ProcessTime();
                String logId = UUID.randomUUID().toString();

                Object object = this.executeQuery(query);

                logTime(logId, timeQuery.stop().getTempo().getTime(), object);
                logRecords(logId, object, timeQuery.stop().getTempo().getTime());

                if (this.maxResults != null) {
                    hql.getBeanList(new ArrayList<>());
                }
                this.result(object);

                this.result(this.hql, object);
            }

            ProcessTime processTime = new ProcessTime();
            this.customProcess(this.session);
            if (processTime.stop().getTempo().getTime() > 0) {
                Loggable.log.debug("customProcess " + processTime.toString());
            }
        } catch (ValidacaoException | DAOException e) {
            Loggable.log.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            Loggable.log.error(e.getMessage(), e);
            throw new DAOException(e);
        }

        return (T) this;
    }

    private void logRecords(String logId, Object object, long time) {
        int sizeObject = getSizeObject(object);
        if (sizeObject >= MIN_LOGGABLE_RECORDS) {
            StringBuilder sb = new StringBuilder("\n");
            sb.append("- ID: ").append(logId).append("\n");
            sb.append("- Registros retornados: ").append(sizeObject).append("\n");
            sb.append("- Tempo total: ").append(time).append("\n");
            sb.append("- Query executada: ").append(query.getQueryString()).append("\n");
            sb.append(getStackTrace()).append("\n");
            Loggable.log.warn(sb.toString());
        }
    }

    private void logTime(String logId, long time, Object object) {
        if (time >= MIN_SECONDS_LOGGABLE_RECORDS * SECONDS_MILISECONDS_DECIMALS) {
            int sizeObject = getSizeObject(object);
            StringBuilder sb = new StringBuilder("\n");
            sb.append("- ID: ").append(logId).append("\n");
            sb.append("- Tempo total: ").append(time).append("\n");
            if (sizeObject > 0) {
                sb.append("- Registros retornados: ").append(sizeObject).append("\n");
            }
            sb.append("- Query executada: ").append(query.getQueryString()).append("\n");
            sb.append(getStackTrace()).append("\n");
            Loggable.log.warn(sb.toString());
        }
    }

    private int getSizeObject(Object object) {
        if (object instanceof Collection) {
            Collection<?> collection = (Collection<?>) object;
            return collection.size();
        }
        return 0;
    }

    private String getStackTrace() {
        if (this.getCallerClassName() != null) {
            return "- StackTrace: ".concat(ThreadHelper.buildThreadTrace(this.getCallerClassName()));
        }
        return "- StackTrace: ".concat(ThreadHelper.buildThreadTrace());
    }

    protected void customProcess(Session session) throws ValidacaoException, DAOException {
    }

    /**
     * Deve ser implementado retornando a query
     *
     * @return
     */
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
    }

    /**
     * Deve ser implementado retornando a query
     *
     * @return
     */
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
    }

    /**
     * Metodo para customizao da query aplicados no hibernate
     *
     * @param query Query
     */
    protected void customQuery(Query query) {
    }

    /**
     * Execucao da query. Pode ser sobrescrito para alterar para uniqueResult
     *
     * @param query
     * @return
     */
    protected Object executeQuery(Query query) {
        return query.list();
    }

    /**
     * Resultado da query.
     *
     * @param result R (Generic)
     */
    protected void result(Object result) throws ValidacaoException, DAOException {
    }

    /**
     * Resultado da query.
     *
     * @param result R (Generic)
     */
    protected void result(HQLHelper hql, Object result) {
    }

    protected Session getSession() throws DAOException {
        if (this.session == null) {
            this.session = HibernateSessionFactory.getSession();
        }
        return this.session;
    }

    protected HQLHelper customHQLHelperBeforeExecute(HQLHelper hql) {
        return hql;
    }

    public Collection getResult() {
        throw new UnsupportedOperationException("E necessario implemnentar este metodo");
    }

    public String getCallerClassName() {
        return callerClassName;
    }

    public void setCallerClassName(String callerClassName) {
        this.callerClassName = callerClassName;
    }

    public void setWriteCallerClass(boolean writeCallerClass) {
        this.writeCallerClass = writeCallerClass;
    }

    public boolean isWriteCallerClass() {
        return writeCallerClass;
    }

    public HQLHelper getHQL() {
        return hql;
    }

    public void setHql(HQLHelper hql) {
        this.hql = hql;
    }
}
