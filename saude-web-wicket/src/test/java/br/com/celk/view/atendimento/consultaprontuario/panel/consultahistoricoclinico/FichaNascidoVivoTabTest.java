package br.com.celk.view.atendimento.consultaprontuario.panel.consultahistoricoclinico;


import br.com.celk.system.Application;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import org.apache.wicket.Localizer;
import org.apache.wicket.ThreadContext;
import org.apache.wicket.application.ComponentInitializationListenerCollection;
import org.apache.wicket.application.ComponentInstantiationListenerCollection;
import org.apache.wicket.settings.IDebugSettings;
import org.apache.wicket.settings.IResourceSettings;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertNull;
import static org.powermock.api.mockito.PowerMockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        Application.class,
        ThreadContext.class,
        IDebugSettings.class,
})
public class FichaNascidoVivoTabTest {

    private final Application application = mock(Application.class);
    private final org.apache.wicket.Application applicationWicket = mock(Application.class);
    private final ComponentInstantiationListenerCollection componentInstantiationListenerCollection = mock(ComponentInstantiationListenerCollection.class);
    private final ComponentInitializationListenerCollection componentInitializationListeners = mock(ComponentInitializationListenerCollection.class);
    private final IDebugSettings iDebugSettings = mock(IDebugSettings.class);
    private final IResourceSettings resourceSettings = mock(IResourceSettings.class);
    private final Localizer localizer = mock(Localizer.class);

    @Before
    public  void setUp(){
        mockStatic(Application.class);
        mockStatic(ThreadContext.class);

        when(ThreadContext.getApplication()).thenReturn(applicationWicket);
        when(applicationWicket.getComponentInitializationListeners()).thenReturn(componentInitializationListeners);
        when(applicationWicket.getComponentInstantiationListeners()).thenReturn(componentInstantiationListenerCollection);
        when(applicationWicket.getDebugSettings()).thenReturn(iDebugSettings);
        when(applicationWicket.getResourceSettings()).thenReturn(resourceSettings);
        when(resourceSettings.getLocalizer()).thenReturn(localizer);
    }


    @Test
    public void test() {
        NoHistoricoClinicoDTO dto = new NoHistoricoClinicoDTO();
        FichaNascidoVivoTab tab = new FichaNascidoVivoTab("id", dto);
        assertNull(tab.getDefaultModelObject());
    }
}