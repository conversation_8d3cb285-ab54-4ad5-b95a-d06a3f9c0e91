package br.com.celk.view.unidadesaude.processos.regulacoesdevolvidas;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.base.BaseEmpresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.base.BaseUsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.NAO;
import static br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO;
import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseSolicitacaoAgendamento.*;
import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseTipoProcedimento.PROP_FLAG_TFD;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class BuildParametrosFiltroRegulacoesDevolvidasTest {

    private BuildParametrosFiltroRegulacoesDevolvidas buildParametros;

    @Before
    public void setup() {
        buildParametros = new BuildParametrosFiltroRegulacoesDevolvidas();
    }

    @Test
    public void deveAdicionarTipoProcedimento() {
        TipoProcedimento tipoProcedimento = new TipoProcedimento(1L);
        List<BuilderQueryCustom.QueryParameter> parametros = buildParametros.addTipoProcedimento(tipoProcedimento)
                                                                            .build();

        assertNotNull("Parâmetro não pode ser null", parametros);
        assertEquals(VOUtils.montarPath(PROP_TIPO_PROCEDIMENTO, PROP_FLAG_TFD), parametros.get(0).getProp());
        assertEquals(NAO, parametros.get(0).getValue());
        assertEquals(PROP_TIPO_PROCEDIMENTO, parametros.get(1).getProp());
        assertEquals(Long.valueOf(1), ((List<TipoProcedimento>) parametros.get(1).getValue()).get(0).getCodigo());
    }

    @Test
    public void deveAdicionarCodigoPaciente() {
        List<BuilderQueryCustom.QueryParameter> parametros = buildParametros.addCodigoPaciente(1L)
                                                                            .build();

        assertNotNull("Parâmetro não pode ser null", parametros);
        assertEquals(VOUtils.montarPath(PROP_USUARIO_CADSUS, BaseUsuarioCadsus.PROP_CODIGO), parametros.get(0).getProp());
        assertEquals(1L, parametros.get(0).getValue());
    }

    @Test
    public void deveAdicionarPeriodo() {
        Date dataAtual = DataUtil.getDataAtual();
        DatePeriod periodo = new DatePeriod(dataAtual, dataAtual);

        List<BuilderQueryCustom.QueryParameter> parametros = buildParametros.addPeriodo(periodo)
                                                                            .build();

        assertNotNull("Parâmetro não pode ser null", parametros);
        assertEquals(PROP_DATA_AUTORIZADOR, parametros.get(0).getProp());
        assertEquals(periodo.getDataInicial(), parametros.get(0).getValue());
        assertEquals(PROP_DATA_AUTORIZADOR, parametros.get(1).getProp());
        assertEquals(periodo.getDataFinal(), parametros.get(1).getValue());
    }

    @Test
    public void deveAdicionarEmpresas() {
        List<Empresa> empresas = Collections.singletonList(new Empresa(1L));
        OperadorValor<List<Empresa>> operadorEmpresa = new OperadorValor<>();
        operadorEmpresa.setValue(empresas);
        List<BuilderQueryCustom.QueryParameter> parametros = buildParametros.addEmpresas(operadorEmpresa)
                                                                            .build();

        assertNotNull("Parâmetro não pode ser null", parametros);
        assertEquals(VOUtils.montarPath(PROP_EMPRESA, BaseEmpresa.PROP_CODIGO), parametros.get(0).getProp());
        assertEquals(Collections.singletonList(1L), parametros.get(0).getValue());
    }

    @Test
    public void deveAdicionarProfissional() {
        List<BuilderQueryCustom.QueryParameter> parametros = buildParametros.addProfissional(new Profissional(1L))
                                                                            .build();

        assertNotNull("Parâmetro não pode ser null", parametros);
        assertEquals(PROP_PROFISSIONAL, parametros.get(0).getProp());
        assertEquals(Long.valueOf(1), ((Profissional) parametros.get(0).getValue()).getCodigo());
    }

    @Test
    public void deveAdicionarNumeroSolicitacao() {
        List<BuilderQueryCustom.QueryParameter> parametros = buildParametros.addNumeroSolicitacao(1L)
                                                                            .build();

        assertNotNull("Parâmetro não pode ser null", parametros);
        assertEquals(PROP_CODIGO, parametros.get(0).getProp());
        assertEquals(1L, parametros.get(0).getValue());
    }

    @Test
    public void deveAdicionarStatusSolicitacao() {
        List<BuilderQueryCustom.QueryParameter> parametros = buildParametros.addStatusSolicitacao(Arrays.asList(STATUS_REGULACAO_DEVOLVIDO))
                                                                            .build();

        assertNotNull("Parâmetro não pode ser null", parametros);
        assertEquals(PROP_STATUS, parametros.get(0).getProp());
        assertEquals(Collections.singletonList(STATUS_REGULACAO_DEVOLVIDO), parametros.get(0).getValue());
    }


}