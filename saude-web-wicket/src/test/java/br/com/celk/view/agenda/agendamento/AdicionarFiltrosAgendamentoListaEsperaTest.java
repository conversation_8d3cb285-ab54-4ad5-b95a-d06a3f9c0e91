package br.com.celk.view.agenda.agendamento;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;

import static br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam.*;
import static br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam.PROP_APENAS_BLOQUEADA;
import static org.junit.Assert.*;
import static org.mockito.Mockito.when;


@RunWith(PowerMockRunner.class)
@PrepareForTest({LoadManager.class})
public class AdicionarFiltrosAgendamentoListaEsperaTest {

    private PageParameters pageParameters;

    private AgendamentoListaEsperaDTOParam agendamentoListaEsperaDTOParam;

    @Mock
    private LoadManager loadManagerTipoProcedimento;

    @Mock
    private LoadManager loadManagerExameProcedimento;

    @Mock
    private LoadManager loadManagerEmpresa;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(LoadManager.class);
        agendamentoListaEsperaDTOParam = this.buildAgendamentoListaEsperaDTO();

        pageParameters = new PageParameters();
        pageParameters.add(PROP_CODIGO_TIPO_PROCEDIMENTO, agendamentoListaEsperaDTOParam.getTipoProcedimento().getCodigo());
        pageParameters.add(PROP_CODIGO_EXAME_PROCEDIMENTO, agendamentoListaEsperaDTOParam.getExameProcedimento().getCodigo());
        pageParameters.add(PROP_DATA_NASCIMENTO, agendamentoListaEsperaDTOParam.getDataNascimento());
        pageParameters.add(PROP_APENAS_REAGENDAMENTO, agendamentoListaEsperaDTOParam.getApenasReagendamento());
        pageParameters.add(PROP_CODIGO_EMPRESA, "");
        pageParameters.add(PROP_CODIGO_ORIGEM_SOLICITACAO, agendamentoListaEsperaDTOParam.getOrigemSolicitacao().getCodigo());
        pageParameters.add(PROP_NOME_PACIENTE, agendamentoListaEsperaDTOParam.getPaciente());
        pageParameters.add(PROP_CODIGO_SOLICITACAO, agendamentoListaEsperaDTOParam.getCodigoSolicitacao());
        pageParameters.add(PROP_CODIGO_PACIENTE, agendamentoListaEsperaDTOParam.getCodigoPaciente());
        pageParameters.add(PROP_TIPO_CONSULTA, agendamentoListaEsperaDTOParam.getTipoConsulta());
        pageParameters.add(PROP_NUMERACAO_AUXILIAR, agendamentoListaEsperaDTOParam.getNumeracaoAuxiliar());
        pageParameters.add(PROP_APENAS_BLOQUEADA, agendamentoListaEsperaDTOParam.getApenasBloqueada());
    }

    @Test
    public void deveAdicionarValorFiltroPageParameters() {
        agendamentoListaEsperaDTOParam.setNumeracaoAuxiliar(null);
        PageParameters page = new AdicionarFiltrosAgendamentoListaEspera(pageParameters).deveManterFiltro(true)
                                                                                        .addFiltersPageParameters(agendamentoListaEsperaDTOParam);

        assertNotNull("PageParameters não pode ser null", page);
        assertNotNull("PageParameters allNamed não pode ser null", page.getAllNamed());
        assertEquals("Deve ter 11 parametros", 11, page.getAllNamed().size());
    }

    @Test
    public void deveAdicionarValorFiltroPageParametersCenario2() {
        PageParameters page = new AdicionarFiltrosAgendamentoListaEspera(pageParameters).deveManterFiltro(true)
                                                                                        .addFiltersPageParameters(agendamentoListaEsperaDTOParam);

        assertNotNull("PageParameters não pode ser null", page);
        assertNotNull("PageParameters allNamed não pode ser null", page.getAllNamed());
        assertFalse(page.get(PROP_CODIGO_TIPO_PROCEDIMENTO).isEmpty());
        assertFalse(page.get(PROP_CODIGO_EXAME_PROCEDIMENTO).isEmpty());
        assertFalse(page.get(PROP_DATA_NASCIMENTO).isEmpty());
        assertFalse(page.get(PROP_APENAS_REAGENDAMENTO).isEmpty());
        assertFalse(page.get(PROP_CODIGO_EMPRESA).isEmpty());
        assertFalse(page.get(PROP_CODIGO_ORIGEM_SOLICITACAO).isEmpty());
        assertFalse(page.get(PROP_NOME_PACIENTE).isEmpty());
        assertFalse(page.get(PROP_CODIGO_SOLICITACAO).isEmpty());
        assertFalse(page.get(PROP_CODIGO_PACIENTE).isEmpty());
        assertFalse(page.get(PROP_TIPO_CONSULTA).isEmpty());
        assertFalse(page.get(PROP_NUMERACAO_AUXILIAR).isEmpty());
        assertFalse(page.get(PROP_APENAS_BLOQUEADA).isEmpty());
    }

    @Test
    public void deveRemoverValorFiltroPageParameters() {
        PageParameters page = new AdicionarFiltrosAgendamentoListaEspera(pageParameters).deveManterFiltro(false)
                                                                                        .addFiltersPageParameters(agendamentoListaEsperaDTOParam);

        assertNotNull("PageParameters não pode ser null", page);
        assertNotNull("PageParameters allNamed não pode ser null", page.getAllNamed());
        assertEquals("Não deve possuir parametros", 0, page.getAllNamed().size());
    }

    @Test
    public void naoDeveAdicionarFiltroAgendamentoListaEspera() {
        agendamentoListaEsperaDTOParam = new AdicionarFiltrosAgendamentoListaEspera(new PageParameters()).addFiltersAgendamentoListaEsperaDTOParam(new AgendamentoListaEsperaDTOParam());

        assertNotNull("AgendamentoListaEsperaDTOParam não pode ser null", agendamentoListaEsperaDTOParam);
        assertNull(agendamentoListaEsperaDTOParam.getTipoProcedimento());
        assertNull(agendamentoListaEsperaDTOParam.getExameProcedimento());
        assertNull(agendamentoListaEsperaDTOParam.getDataNascimento());
        assertNull(agendamentoListaEsperaDTOParam.getApenasReagendamento());
        assertNull(agendamentoListaEsperaDTOParam.getEmpresas());
        assertNull(agendamentoListaEsperaDTOParam.getOrigemSolicitacao());
        assertNull(agendamentoListaEsperaDTOParam.getPaciente());
        assertNull(agendamentoListaEsperaDTOParam.getCodigoSolicitacao());
        assertNull(agendamentoListaEsperaDTOParam.getCodigoPaciente());
        assertNull(agendamentoListaEsperaDTOParam.getTipoConsulta());
        assertNull(agendamentoListaEsperaDTOParam.getNumeracaoAuxiliar());
        assertNull(agendamentoListaEsperaDTOParam.getApenasBloqueada());
    }

    @Test
    public void deveAdicionarFiltroAgendamentoListaEspera() {
        loadManagerTipoProcedimento = this.mockLoadManager(TipoProcedimento.class, loadManagerTipoProcedimento);
        when(loadManagerTipoProcedimento.getVO()).thenReturn(agendamentoListaEsperaDTOParam.getTipoProcedimento());

        loadManagerExameProcedimento = this.mockLoadManager(ExameProcedimento.class, loadManagerExameProcedimento);
        when(loadManagerExameProcedimento.getVO()).thenReturn(agendamentoListaEsperaDTOParam.getExameProcedimento());

        loadManagerEmpresa = this.mockLoadManager(Empresa.class, loadManagerEmpresa);
        when(loadManagerEmpresa.getVO()).thenReturn(agendamentoListaEsperaDTOParam.getOrigemSolicitacao());

        AgendamentoListaEsperaDTOParam parametros = new AdicionarFiltrosAgendamentoListaEspera(pageParameters).addFiltersAgendamentoListaEsperaDTOParam(new AgendamentoListaEsperaDTOParam());

        assertNotNull("AgendamentoListaEsperaDTOParam não pode ser null", parametros);
        assertNotNull(parametros.getTipoProcedimento());
        assertNotNull(parametros.getExameProcedimento());
        assertNotNull(parametros.getDataNascimento());
        assertNotNull(parametros.getApenasReagendamento());
        assertNotNull(parametros.getOrigemSolicitacao());
        assertNotNull(parametros.getPaciente());
        assertNotNull(parametros.getCodigoSolicitacao());
        assertNotNull(parametros.getCodigoPaciente());
        assertNotNull(parametros.getTipoConsulta());
        assertNotNull(parametros.getNumeracaoAuxiliar());
        assertNotNull(parametros.getApenasBloqueada());

        assertNull(parametros.getEmpresas());
    }


    public LoadManager mockLoadManager(Class clazz, LoadManager loadManager) {
        when(LoadManager.getInstance(clazz)).thenReturn(loadManager);
        when(loadManager.setId(1L)).thenReturn(loadManager);
        when(loadManager.start()).thenReturn(loadManager);
        return loadManager;
    }


    private AgendamentoListaEsperaDTOParam buildAgendamentoListaEsperaDTO() {
        AgendamentoListaEsperaDTOParam param = new AgendamentoListaEsperaDTOParam();
        param.setTipoProcedimento(new TipoProcedimento(1L));
        param.setExameProcedimento(new ExameProcedimento(1L));
        param.setDataNascimento(DataUtil.getDataAtual());
        param.setApenasReagendamento(RepositoryComponentDefault.SIM_LONG);
        param.setEmpresas(Collections.singletonList(new Empresa(1L)));
        param.setOrigemSolicitacao(new Empresa(1L));
        param.setPaciente("Eduardo");
        param.setCodigoSolicitacao("1L");
        param.setCodigoPaciente(1L);
        param.setTipoConsulta(SolicitacaoAgendamento.TIPO_CONSULTA_NORMAL);
        param.setNumeracaoAuxiliar("123456");
        param.setApenasBloqueada(RepositoryComponentDefault.SIM_LONG);
        return param;
    }

}