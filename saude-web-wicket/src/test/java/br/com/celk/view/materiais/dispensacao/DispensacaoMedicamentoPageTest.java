package br.com.celk.view.materiais.dispensacao;

import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalField;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DispensacaoMedicamentoPageTest {

    private DispensacaoMedicamentoItem dmi;

    private Produto produto;

    private List<DispensacaoMedicamentoItem> dispensacoes;

    @Before
    public void setUp(){
        dmi = new DispensacaoMedicamentoItem();

    }

    @Test
    public void deveObterOValorTotalDaPrescicaoMesmoTendoJaAlgumaQuantidadeDispensada() throws ValidacaoException {
        Calendar data = Calendar.getInstance();
        data.set(2021,8,15);
        dmi.setQuantidadePrescrita(90D);
        dmi.setDataProximaDispensacao(data.getTime());// "2021-05-19"
        boolean resultado = verificarQuantidadeDispensadaMaiorQuePrescrita(dmi,50D,90D);
        Assert.assertEquals(true,resultado);
    }
    @Test
    public void naoDeveObterOValorTotalDaPrescicaoMesmoTendoJaAlgumaQuantidadeDispensada() throws ValidacaoException {
        LocalDate hoje = LocalDate.now().plusDays(5l);
        Date dat = Date.from(hoje.atStartOfDay(ZoneId.systemDefault()).toInstant());
        dmi.setQuantidadePrescrita(90D);
        dmi.setDataProximaDispensacao(dat);// "2021-09-19"
        boolean resultado = verificarQuantidadeDispensadaMaiorQuePrescrita(dmi,50D,90D);
        Assert.assertEquals(false,resultado);
    }

    @Test
    public void devePassarPegandoInformacaoDaDataNaConsultaDaDispensacaoAtravesDoReceituarioItem() throws ValidacaoException {
        Calendar data = Calendar.getInstance();
        data.set(2021,5,19);
        produto = new Produto();
        produto.setCodigo("2196");
        DispensacaoMedicamentoItem dispensaItem = new DispensacaoMedicamentoItem();
        dispensaItem.setDataProximaDispensacao(data.getTime());
        dmi.setQuantidadePrescrita(90D);
        dmi.setDataProximaDispensacao(null);// "2021-09-19"
        dmi.setProduto(produto);
        dispensacoes = montarListaDeDispensacao(1);
        ReceituarioItem receituarioItem = new ReceituarioItem();
        receituarioItem.setDispensacaoMedicamentoItem(dispensaItem);
        dmi.setReceituarioItem(receituarioItem);
        boolean resultado = verificarQuantidadeDispensadaMaiorQuePrescrita(dmi,50D,90D);
        Assert.assertEquals(true,resultado);
    }
    @Test
    public void deveSerVerdadeiroQuandoCompararComOProdutoDoPacientePrescrito() throws ValidacaoException {
        produto = new Produto();
        produto.setCodigo("2196");
        dmi.setProduto(produto);
        montarListaDeDispensacao(2);
        boolean resultado = verificarQuantidadeDispensadaMaiorQuePrescrita(dmi,50D,90D);
        Assert.assertEquals(true,resultado);
    }

    private List<DispensacaoMedicamentoItem> montarListaDeDispensacao(int quantidadeItem){
        dispensacoes = new ArrayList<>();

        Calendar data = Calendar.getInstance();
        data.set(2021,5,19);

        if(quantidadeItem == 1){
            DispensacaoMedicamentoItem novoDMI = new DispensacaoMedicamentoItem();
            Produto novoProduto = new Produto();
            novoProduto.setCodigo("2196");
            novoDMI.setProduto(novoProduto);
            novoDMI.setDataProximaDispensacao(data.getTime());
            dispensacoes.add(novoDMI);
        }else {
            DispensacaoMedicamentoItem novoDMI = new DispensacaoMedicamentoItem();
            novoDMI.setDataProximaDispensacao(data.getTime());
            Produto novoProduto = new Produto();
            novoProduto.setCodigo("2196");
            novoDMI.setProduto(novoProduto);
            dispensacoes.add(novoDMI);
            novoDMI = new DispensacaoMedicamentoItem();
            novoDMI.setDataProximaDispensacao(data.getTime());
            novoProduto = new Produto();
            novoProduto.setCodigo("2296");
            novoDMI.setProduto(novoProduto);
            dispensacoes.add(novoDMI);
        }
        return dispensacoes;
    }

    private boolean verificarQuantidadeDispensadaMaiorQuePrescrita(DispensacaoMedicamentoItem item,
                                                                   Double totalDispensado,
                                                                   Double quantidadeParaDispensar) throws ValidacaoException {
        LocalDate hoje = LocalDate.now();
        long diasToleranciaParaDispensacao = 5l;

        LocalDate dataProximaDispensacao = null;
        if(!(item.getDataProximaDispensacao() == null)) {
            dataProximaDispensacao = item.getDataProximaDispensacao().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        }else {
            for (DispensacaoMedicamentoItem dmiRetorno : dispensacoes) {
                if (dmiRetorno.getProduto().equals(item.getProduto())) {
                    if (dmiRetorno.getDataProximaDispensacao() == null) {
                        dataProximaDispensacao = item.getReceituarioItem().getDispensacaoMedicamentoItem().getDataProximaDispensacao().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    } else {
                        dataProximaDispensacao = dmiRetorno.getDataProximaDispensacao().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    }
                }
            }
        }
        LocalDate dataToleranca = dataProximaDispensacao.minusDays(diasToleranciaParaDispensacao);
        if (hoje.isBefore(dataToleranca) || hoje.equals(dataToleranca)) {
            if ((totalDispensado + quantidadeParaDispensar) > item.getQuantidadePrescrita()) {
                return false;
            }else{
                return true;
            }
        }else{
            return true;
        }
    }
}