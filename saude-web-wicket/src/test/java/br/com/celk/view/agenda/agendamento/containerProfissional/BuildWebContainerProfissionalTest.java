package br.com.celk.view.agenda.agendamento.containerProfissional;

import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.util.tester.WicketTester;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento.TIPO_CONSULTA_NORMAL;
import static br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento.TIPO_CONSULTA_RETORNO;
import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({BOFactoryWicket.class})
public class BuildWebContainerProfissionalTest {

    @Mock
    private AgendamentoFacade agendamentoBO;
    private WicketTester wicketTester;

    @Before
    public void setUp(){
        PowerMockito.mockStatic(BOFactoryWicket.class);
        when(BOFactoryWicket.getBO(AgendamentoFacade.class)).thenReturn(agendamentoBO);
        wicketTester = new WicketTester();
    }

    @Test
    public void deveExibirContainerProfissionalCenario1() {
        when(agendamentoBO.permiteInformarUnidadeExecutante()).thenReturn(true);
        SolicitacaoAgendamento solicitacaoAgendamento = new SolicitacaoAgendamento();
        solicitacaoAgendamento.setTipoConsulta(TIPO_CONSULTA_NORMAL);

        WebMarkupContainer containerProfissional = new BuildWebContainerProfissional().ocultarContainer(solicitacaoAgendamento)
                                                                                      .build();

        assertNotNull(containerProfissional);
        assertTrue(containerProfissional.isVisible());
    }

    @Test
    public void deveExibirContainerProfissionalCenario2() {
        when(agendamentoBO.permiteInformarUnidadeExecutante()).thenReturn(true);
        SolicitacaoAgendamento solicitacaoAgendamento = new SolicitacaoAgendamento();
        solicitacaoAgendamento.setTipoConsulta(TIPO_CONSULTA_RETORNO);

        WebMarkupContainer containerProfissional = new BuildWebContainerProfissional().ocultarContainer(solicitacaoAgendamento)
                                                                                      .build();

        assertNotNull(containerProfissional);
        assertTrue(containerProfissional.isVisible());
    }

    @Test
    public void deveExibirContainerProfissionalCenario3() {
        when(agendamentoBO.permiteInformarUnidadeExecutante()).thenReturn(false);
        SolicitacaoAgendamento solicitacaoAgendamento = new SolicitacaoAgendamento();
        solicitacaoAgendamento.setTipoConsulta(TIPO_CONSULTA_RETORNO);

        WebMarkupContainer containerProfissional = new BuildWebContainerProfissional().ocultarContainer(solicitacaoAgendamento)
                                                                                      .build();

        assertNotNull(containerProfissional);
        assertTrue(containerProfissional.isVisible());
    }

    @Test
    public void naoDeveExibirContainerProfissionalCenario2() {
        when(agendamentoBO.permiteInformarUnidadeExecutante()).thenReturn(false);
        SolicitacaoAgendamento solicitacaoAgendamento = new SolicitacaoAgendamento();
        solicitacaoAgendamento.setTipoConsulta(TIPO_CONSULTA_NORMAL);

        WebMarkupContainer containerProfissional = new BuildWebContainerProfissional().ocultarContainer(solicitacaoAgendamento)
                                                                                      .build();

        assertNotNull(containerProfissional);
        assertFalse(containerProfissional.isVisible());
    }
}