package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoCovid19DTO;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.InvestigacaoAgravo;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.prontuario.basico.TipoTesteRapido;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoCovid19;
import br.com.ksisolucoes.vo.vigilancia.investigacao.dto.ExameDto;
import junit.framework.Assert;
import org.joda.time.LocalDate;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import static org.junit.Assert.*;

public class RegrasFichaInvestigacaoAgravoCovid19Test {
    private RegrasFichaInvestigacaoAgravoCovid19 regrasFichaCovid19;
    private FichaInvestigacaoAgravoCovid19DTO fichaCovidDTO;
    private InvestigacaoAgravoCovid19 investigacaoAgravo;

    @Before
    public void setUp() throws Exception {
        fichaCovidDTO = new FichaInvestigacaoAgravoCovid19DTO();
        fichaCovidDTO.setInvestigacaoAgravoCovid19(new InvestigacaoAgravoCovid19());
        fichaCovidDTO.setRegistroAgravo(new RegistroAgravo());
        investigacaoAgravo = new InvestigacaoAgravoCovid19();
        regrasFichaCovid19 = new RegrasFichaInvestigacaoAgravoCovid19(investigacaoAgravo, fichaCovidDTO);
        fichaCovidDTO.setInvestigacaoAgravoCovid19Old(investigacaoAgravo);
    }

    @Test
    public void verificaExameNaInvestigacao_SemResultadoDeExamesTest() {
        ExameDto exameDtoTesteRapido = new ExameDto();
        exameDtoTesteRapido.setTipoTeste((Long) TipoTesteRapido.TipoTeste.HEPATITE_C.value());
        ExameDto exameDtoExameExterno = new ExameDto();
        fichaCovidDTO.getInvestigacaoAgravoCovid19().setFlagAlteracaoResultadoRealizada(
                InvestigacaoAgravoCovid19.FlagAlteracaoResultadoRealizada.RESULTADO_NAO_ALTERADO.getValue());

        exameDtoExameExterno.setDataColeta(LocalDate.now().toDate());
        RegistroAgravo registroAgravo = new RegistroAgravo();
        registroAgravo.setDataPrimeirosSintomas(LocalDate.now().toDate());
        investigacaoAgravo.setRegistroAgravo(registroAgravo);

        regrasFichaCovid19.verificaExameNaInvestigacao(exameDtoTesteRapido, exameDtoExameExterno);

        assertNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getTipoTeste());
        Assert.assertFalse(exameDtoTesteRapido.getTipoTeste().equals(
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getTipoTeste()));

        assertNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getResultadoTeste());
        Assert.assertNotSame(InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO,
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getResultadoTeste());

        Assert.assertNotSame(InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO,
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getResultadoTeste());

        Assert.assertEquals(fichaCovidDTO.getInvestigacaoAgravoCovid19().getConcluido(),
                RepositoryComponentDefault.SIM_LONG);
        Assert.assertEquals(fichaCovidDTO.getInvestigacaoAgravoCovid19().getColetado(),
                RepositoryComponentDefault.NAO_LONG);
        Assert.assertEquals(fichaCovidDTO.getInvestigacaoAgravoCovid19().getSolicitado(),
                RepositoryComponentDefault.NAO_LONG);
    }

    @Test
    public void verificaExameNaInvestigacao_ExamePositivoTest() {
        ExameDto exameDtoTesteRapido = new ExameDto();
        exameDtoTesteRapido.setTipoTeste((Long) TipoTesteRapido.TipoTeste.HEPATITE_C.value());

        ExameDto exameDtoExameExterno = new ExameDto();
        exameDtoExameExterno.setResultadoTeste(InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO);
        Calendar dataColeta = Calendar.getInstance();
        dataColeta.set(2020, Calendar.JULY, 25);
        exameDtoExameExterno.setDataColeta(dataColeta.getTime());

        RegistroAgravo registroAgravo = new RegistroAgravo();
        registroAgravo.setDataPrimeirosSintomas(dataColeta.getTime());
        investigacaoAgravo.setRegistroAgravo(registroAgravo);

        fichaCovidDTO.getInvestigacaoAgravoCovid19().setFlagAlteracaoResultadoRealizada(
                InvestigacaoAgravoCovid19.FlagAlteracaoResultadoRealizada.RESULTADO_NAO_ALTERADO.getValue());

        regrasFichaCovid19.verificaExameNaInvestigacao(exameDtoTesteRapido, exameDtoExameExterno);

        Assert.assertNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getTipoTeste());
        Assert.assertFalse(exameDtoTesteRapido.getTipoTeste().equals(
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getTipoTeste()));
        Assert.assertNotNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getResultadoTeste());
        Assert.assertEquals(InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO.value(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getResultadoTeste());
        Assert.assertNotNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getDataColetaTeste());
        Assert.assertEquals(fichaCovidDTO.getInvestigacaoAgravoCovid19().getDataColetaTeste(),
                dataColeta.getTime());
    }

    @Test
    public void verificaExameNaInvestigacao_xxxExamePositivoTest() {
        ExameDto exameDtoTesteRapido = new ExameDto();
        exameDtoTesteRapido.setTipoTeste((Long) TipoTesteRapido.TipoTeste.HEPATITE_C.value());

        ExameDto exameDtoExameExterno = new ExameDto();
        exameDtoExameExterno.setResultadoTeste(InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO);
        Calendar dataColeta = Calendar.getInstance();
        dataColeta.set(2020, Calendar.JULY, 25);
        exameDtoExameExterno.setDataColeta(dataColeta.getTime());

        RegistroAgravo registroAgravo = new RegistroAgravo();
        registroAgravo.setDataPrimeirosSintomas(dataColeta.getTime());
        investigacaoAgravo.setRegistroAgravo(registroAgravo);

        fichaCovidDTO.getInvestigacaoAgravoCovid19().setFlagAlteracaoResultadoRealizada(
                InvestigacaoAgravoCovid19.FlagAlteracaoResultadoRealizada.RESULTADO_NAO_ALTERADO.getValue());

        regrasFichaCovid19.verificaExameNaInvestigacao(exameDtoTesteRapido, exameDtoExameExterno);

        Assert.assertNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getTipoTeste());
        Assert.assertFalse(exameDtoTesteRapido.getTipoTeste().equals(
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getTipoTeste()));
        Assert.assertNotNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getResultadoTeste());
        Assert.assertEquals(InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO.value(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getResultadoTeste());
        Assert.assertNotNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getDataColetaTeste());
        Assert.assertEquals(fichaCovidDTO.getInvestigacaoAgravoCovid19().getDataColetaTeste(),
                dataColeta.getTime());
    }

    @Test
    public void testIsDataSintomasDataResultadoExameInvalido_deveRetornarCorretamente() {
        RegistroAgravo registroAgravo = new RegistroAgravo();
        registroAgravo.setDataPrimeirosSintomas(LocalDate.now().toDate());

        assertTrue(regrasFichaCovid19.isDataPrimeirosSintomasAnteriorDataColeta(registroAgravo, LocalDate.now().toDate()));
        assertFalse(regrasFichaCovid19.isDataPrimeirosSintomasAnteriorDataColeta(registroAgravo, Data.removeDias(LocalDate.now().toDate(), 1)));

        registroAgravo.setDataPrimeirosSintomas(Data.removeDias(LocalDate.now().toDate(), 1));
        assertTrue(regrasFichaCovid19.isDataPrimeirosSintomasAnteriorDataColeta(registroAgravo, LocalDate.now().toDate()));
    }

    @Test
    public void verificaExameNaInvestigacao_ExamePositivoComExameTesteRapidoNuloTest() {
        ExameDto exameDtoTesteRapido = new ExameDto();
        exameDtoTesteRapido.setTipoTeste((Long) TipoTesteRapido.TipoTeste.HEPATITE_C.value());
        exameDtoTesteRapido.setResultadoTeste(InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO);
        Calendar dataColeta = Calendar.getInstance();
        dataColeta.set(2020, Calendar.JULY, 25);
        exameDtoTesteRapido.setDataColeta(dataColeta.getTime());
        ExameDto exameDtoExameExterno = null;

        RegistroAgravo registroAgravo = new RegistroAgravo();
        registroAgravo.setDataPrimeirosSintomas(dataColeta.getTime());
        investigacaoAgravo.setRegistroAgravo(registroAgravo);

        fichaCovidDTO.getInvestigacaoAgravoCovid19().setFlagAlteracaoResultadoRealizada(
                InvestigacaoAgravoCovid19.FlagAlteracaoResultadoRealizada.RESULTADO_NAO_ALTERADO.getValue());

        regrasFichaCovid19.verificaExameNaInvestigacao(exameDtoTesteRapido, exameDtoExameExterno);

        Assert.assertEquals(TipoTesteRapido.TipoTeste.HEPATITE_C.value(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getTipoTeste());
        Assert.assertSame(TipoTesteRapido.TipoTeste.HEPATITE_C.value(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getTipoTeste());
        Assert.assertNotNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getResultadoTeste());
        Assert.assertEquals(InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO.value(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getResultadoTeste());
        Assert.assertNotNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getDataColetaTeste());
        Assert.assertEquals(fichaCovidDTO.getInvestigacaoAgravoCovid19().getDataColetaTeste(),
                dataColeta.getTime());
    }

    @Test
    public void carregaCondicoes_DeveInicializarValorParaCadaCondicaoTest() {
        fichaCovidDTO.setTriagem(new FormularioTriagemCovid19());
        FormularioTriagemMorbidadeCovid19 condicao = new FormularioTriagemMorbidadeCovid19();
        condicao.setMorbidadeCovid19(new MorbidadePreviaCovid19());
        List<FormularioTriagemMorbidadeCovid19> listaCondicoes = new ArrayList<>();
        listaCondicoes.add(condicao);

        condicao.getMorbidadeCovid19().setCodigo(10L);
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        Assert.assertEquals(fichaCovidDTO.getDoencaRespDescompensada(), condicao.getMorbidadeCovid19());

        condicao.getMorbidadeCovid19().setCodigo(11L);
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        Assert.assertEquals(fichaCovidDTO.getDoencaCardiacaCronica(), condicao.getMorbidadeCovid19());

        condicao.getMorbidadeCovid19().setCodigo(2L);
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        Assert.assertEquals(fichaCovidDTO.getDiabetes(), condicao.getMorbidadeCovid19());

        condicao.getMorbidadeCovid19().setCodigo(12L);
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        Assert.assertEquals(fichaCovidDTO.getDoencasRenaisAvancado(), condicao.getMorbidadeCovid19());

        condicao.getMorbidadeCovid19().setCodigo(13L);
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        Assert.assertEquals(fichaCovidDTO.getImunossupressao(), condicao.getMorbidadeCovid19());

        condicao.getMorbidadeCovid19().setCodigo(14L);
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        Assert.assertEquals(fichaCovidDTO.getGestanteAltoRisco(), condicao.getMorbidadeCovid19());

        condicao.getMorbidadeCovid19().setCodigo(15L);
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        Assert.assertEquals(fichaCovidDTO.getPortadorDoencaCromossomica(), condicao.getMorbidadeCovid19());

        configuraCondicoes_test(listaCondicoes);
    }

    private void configuraCondicoes_test(List<FormularioTriagemMorbidadeCovid19> listaCondicoes) {
        fichaCovidDTO.setInvestigacaoAgravoCovid19(investigacaoAgravo);

        investigacaoAgravo.setPortadorDoencaCromossomica(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        Assert.assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getPortadorDoencaCromossomica());

        investigacaoAgravo.setCancelado(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getCancelado());

        investigacaoAgravo.setIgnorado(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getIgnorado());

        investigacaoAgravo.setObito(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getObito());

        investigacaoAgravo.setCura(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getCura());

        investigacaoAgravo.setInternado(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getInternado());

        investigacaoAgravo.setInternadoUti(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getInternadoUti());

        investigacaoAgravo.setTratamentoDomiciliar(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getTratamentoDomiciliar());

        investigacaoAgravo.setSolicitado(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getSolicitado());

        investigacaoAgravo.setColetado(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getColetado());

        investigacaoAgravo.setConcluido(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getConcluido());
    }

    @Test
    public void carregaCondicoes_NaoDeveInicializarValorParaCadaCondicaoTest() {
        fichaCovidDTO.setTriagem(new FormularioTriagemCovid19());
        FormularioTriagemMorbidadeCovid19 condicao = new FormularioTriagemMorbidadeCovid19();
        condicao.setMorbidadeCovid19(new MorbidadePreviaCovid19());
        List<FormularioTriagemMorbidadeCovid19> listaCondicoes = new ArrayList<>();
        listaCondicoes.add(condicao);

        condicao.getMorbidadeCovid19().setCodigo(9999999L);
        regrasFichaCovid19.carregaCondicoes(listaCondicoes);
        Assert.assertNotSame(fichaCovidDTO.getDoencaRespDescompensada(), condicao.getMorbidadeCovid19());
        Assert.assertNotSame(fichaCovidDTO.getDoencaCardiacaCronica(), condicao.getMorbidadeCovid19());
        Assert.assertNotSame(fichaCovidDTO.getDiabetes(), condicao.getMorbidadeCovid19());
        Assert.assertNotSame(fichaCovidDTO.getDoencasRenaisAvancado(), condicao.getMorbidadeCovid19());
        Assert.assertNotSame(fichaCovidDTO.getImunossupressao(), condicao.getMorbidadeCovid19());
        Assert.assertNotSame(fichaCovidDTO.getGestanteAltoRisco(), condicao.getMorbidadeCovid19());
        Assert.assertNotSame(fichaCovidDTO.getPortadorDoencaCromossomica(), condicao.getMorbidadeCovid19());
    }

    @Test
    public void carregaSintomas_DeveInicializarValorParaCadaSintomaTest() {
        fichaCovidDTO.setTriagem(new FormularioTriagemCovid19());
        FormularioTriagemSintomasCovid19 sintoma = new FormularioTriagemSintomasCovid19();
        sintoma.setSintomaCovid19(new SintomasCovid19());
        List<FormularioTriagemSintomasCovid19> listaSintomas = new ArrayList<>();
        listaSintomas.add(sintoma);

        sintoma.getSintomaCovid19().setCodigo(3L);
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        Assert.assertEquals(fichaCovidDTO.getDorGarganta(), sintoma.getSintomaCovid19());

        sintoma.getSintomaCovid19().setCodigo(23L);
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        Assert.assertEquals(fichaCovidDTO.getDispneia(), sintoma.getSintomaCovid19());

        sintoma.getSintomaCovid19().setCodigo(1L);
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        Assert.assertEquals(fichaCovidDTO.getFebre(), sintoma.getSintomaCovid19());

        sintoma.getSintomaCovid19().setCodigo(2L);
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        Assert.assertEquals(fichaCovidDTO.getTosse(), sintoma.getSintomaCovid19());

        sintoma.getSintomaCovid19().setCodigo(24L);
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        Assert.assertEquals(fichaCovidDTO.getOutros(), sintoma.getSintomaCovid19());

        configuraSintomas_test(listaSintomas);
    }

    private void configuraSintomas_test(List<FormularioTriagemSintomasCovid19> listaSintomas) {
        fichaCovidDTO.setInvestigacaoAgravoCovid19(investigacaoAgravo);

        investigacaoAgravo.setConcluido(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getConcluido());

        investigacaoAgravo.setDorGarganta(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getDorGarganta());

        investigacaoAgravo.setDispneia(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getDispneia());

        investigacaoAgravo.setFebre(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getFebre());

        investigacaoAgravo.setTosse(InvestigacaoAgravoCovid19.SimNao.SIM.getValue());
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        assertEquals(InvestigacaoAgravoCovid19.SimNao.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getTosse());

        String mensagemObservacao = "Teste de mensagem para Observacao";
        investigacaoAgravo.setOutrosObservacao(mensagemObservacao);
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        assertEquals(mensagemObservacao, fichaCovidDTO.getInvestigacaoAgravoCovid19().getOutrosObservacao());

        investigacaoAgravo.setOutrosObservacao(null);
        fichaCovidDTO.setOutrosObservacao("mensagemObservacao");
        fichaCovidDTO.getTriagem().setOutrosSintomas(mensagemObservacao);
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        assertEquals(mensagemObservacao, fichaCovidDTO.getInvestigacaoAgravoCovid19().getOutrosObservacao());

        regrasFichaCovid19 = new RegrasFichaInvestigacaoAgravoCovid19(null, null);
        listaSintomas = new ArrayList<>();
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        assertNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getCodigo());
    }

    @Test
    public void carregaSintomas_NaoDeveInicializarValorParaCadaSintomaTest() {
        fichaCovidDTO.setTriagem(new FormularioTriagemCovid19());
        FormularioTriagemSintomasCovid19 sintoma = new FormularioTriagemSintomasCovid19();
        sintoma.setSintomaCovid19(new SintomasCovid19());
        List<FormularioTriagemSintomasCovid19> listaSintomas = new ArrayList<>();
        listaSintomas.add(sintoma);

        sintoma.getSintomaCovid19().setCodigo(99999999L);
        regrasFichaCovid19.carregaSintomas(listaSintomas);
        Assert.assertNotSame(fichaCovidDTO.getDorGarganta(), sintoma.getSintomaCovid19());
        Assert.assertNotSame(fichaCovidDTO.getDispneia(), sintoma.getSintomaCovid19());
        Assert.assertNotSame(fichaCovidDTO.getFebre(), sintoma.getSintomaCovid19());
        Assert.assertNotSame(fichaCovidDTO.getTosse(), sintoma.getSintomaCovid19());
        Assert.assertNotSame(fichaCovidDTO.getOutros(), sintoma.getSintomaCovid19());
    }

    @Test
    public void houveAlteracaoResultado_DeveEstarAlteradoTest() {
        InvestigacaoAgravoCovid19 old = fichaCovidDTO.getInvestigacaoAgravoCovid19Old();
        assertNotNull(old);
        old.setConcluido(RepositoryComponentDefault.SIM_LONG);
        InvestigacaoAgravoCovid19 investigacaoAgravo = fichaCovidDTO.getInvestigacaoAgravoCovid19();
        assertNotNull(investigacaoAgravo);
        Assert.assertNotSame(old.getConcluido(), investigacaoAgravo.getConcluido());
    }

    @Test
    public void dataObito_DeveEstarHabilitadoTest() {
        boolean isHabilitaDataObito = regrasFichaCovid19.habilitaDataObito(RepositoryComponentDefault.SIM_LONG);
        assertEquals(isHabilitaDataObito, true);
        assertEquals(investigacaoAgravo.getObito(), RepositoryComponentDefault.SIM_LONG);
    }

    @Test
    public void dataObito_DeveEstarHabilitadoQuandoInformadoValorNuloTest() {
        boolean isHabilitaDataObito = regrasFichaCovid19.habilitaDataObito(null);
        assertEquals(isHabilitaDataObito, false);
        assertEquals(investigacaoAgravo.getObito(), RepositoryComponentDefault.NAO_LONG);
    }

    @Test
    public void dataObito_NaoDeveEstarHabilitadoTest() {
        boolean isHabilitaDataObito = regrasFichaCovid19.habilitaDataObito(RepositoryComponentDefault.NAO_LONG);
        assertEquals(isHabilitaDataObito, false);
        assertEquals(investigacaoAgravo.getObito(), RepositoryComponentDefault.NAO_LONG);
    }

    @Test
    public void inicializaStatusRegistroAgravo_DeveTerStatusIgualEmInvestigacaoTest() {
        assertNotNull(fichaCovidDTO.getRegistroAgravo());
        fichaCovidDTO.getRegistroAgravo().setStatus(RegistroAgravo.Status.MONITORAMENTO.value());
        regrasFichaCovid19.inicializaStatusRegistroAgravo();
        assertEquals(fichaCovidDTO.getRegistroAgravo().getStatus(), RegistroAgravo.Status.EM_INVESTIGACAO.value());
    }

    @Test
    public void inicializaProfissionalInvestigacao_InformandoProfissionalTest() {
        investigacaoAgravo.setRegistroAgravo(fichaCovidDTO.getRegistroAgravo());
        Profissional profissional = new Profissional();
        String nomeProfissional = "ALBERTO MENEZES";
        profissional.setNome(nomeProfissional);

        regrasFichaCovid19.inicializaProfissionalInvestigacao(profissional);

        assertNotNull(fichaCovidDTO.getRegistroAgravo());
        assertEquals(nomeProfissional, fichaCovidDTO.getRegistroAgravo().getProfissionalInvestigacao().getNome());
    }

    @Test
    public void inicializaProfissionalInvestigacao_InformandoProfissionalNuloTest() {
        investigacaoAgravo.setRegistroAgravo(fichaCovidDTO.getRegistroAgravo());
        Profissional profissional = new Profissional();
        String nomeProfissional = "MARIA SILVA";
        profissional.setNome(nomeProfissional);
        assertNotNull(investigacaoAgravo.getRegistroAgravo());
        investigacaoAgravo.getRegistroAgravo().setProfissional(profissional);

        regrasFichaCovid19.inicializaProfissionalInvestigacao(null);

        assertEquals(fichaCovidDTO.getRegistroAgravo().getProfissionalInvestigacao().getNome(),
                investigacaoAgravo.getRegistroAgravo().getProfissional().getNome());
        assertEquals(nomeProfissional, fichaCovidDTO.getRegistroAgravo().getProfissionalInvestigacao().getNome());
    }

    @Test
    public void configuraTriagem_DeveTrocarMensagemDoCampoOutrosObservacao() {
        FormularioTriagemCovid19 formularioTriagem = new FormularioTriagemCovid19();
        String mensagem = "MENSAGEM OUTROS SINTOMAS";
        formularioTriagem.setOutrosSintomas(mensagem);
        regrasFichaCovid19.configuraTriagem(formularioTriagem);

        assertNotNull(fichaCovidDTO.getTriagem());
        assertSame(mensagem, fichaCovidDTO.getOutrosObservacao());
        assertSame(mensagem, fichaCovidDTO.getTriagem().getOutrosSintomas());
    }

    @Test
    public void configuraInvestigacaoAgravo() {
        Calendar dataColeta = Calendar.getInstance();
        dataColeta.set(2020, Calendar.AUGUST, 19);
        investigacaoAgravo.setDataColetaTeste(dataColeta.getTime());
        regrasFichaCovid19.configuraInvestigacaoAgravo();
        assertEquals(fichaCovidDTO.getInvestigacaoAgravoCovid19().getDataColetaTeste(), dataColeta.getTime());

        investigacaoAgravo.setTipoTeste((Long) TipoTesteRapido.TipoTeste.COVID_19.value());
        regrasFichaCovid19.configuraInvestigacaoAgravo();
        assertEquals((Long) TipoTesteRapido.TipoTeste.COVID_19.value(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getTipoTeste());

        investigacaoAgravo.setResultadoTeste(InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO.getValue());
        regrasFichaCovid19.configuraInvestigacaoAgravo();
        assertEquals(InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getResultadoTeste());

        investigacaoAgravo.setClassificacaoFinal(InvestigacaoAgravo.ClassificacaoFinal.EA_NAO_GRAVE.getValue());
        regrasFichaCovid19.configuraInvestigacaoAgravo();
        assertEquals(InvestigacaoAgravo.ClassificacaoFinal.EA_NAO_GRAVE.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getClassificacaoFinal());

        String mensagemObservacao = "OBSERVACAO REFERENTE AO PACIENTE";
        investigacaoAgravo.setObservacao(mensagemObservacao);
        regrasFichaCovid19.configuraInvestigacaoAgravo();
        assertEquals(mensagemObservacao, fichaCovidDTO.getInvestigacaoAgravoCovid19().getObservacao());

        investigacaoAgravo.setProfissionalSaude(InvestigacaoAgravoCovid19.ProfissionalSaude.SIM.getValue());
        regrasFichaCovid19.configuraInvestigacaoAgravo();
        assertEquals(InvestigacaoAgravoCovid19.ProfissionalSaude.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getProfissionalSaude());


        investigacaoAgravo.setProfissionalSaude(null);
        fichaCovidDTO.setTriagem(new FormularioTriagemCovid19());
        fichaCovidDTO.getTriagem().setProfissionalSaude(RepositoryComponentDefault.SIM_LONG);
        regrasFichaCovid19.configuraInvestigacaoAgravo();

        assertEquals(InvestigacaoAgravoCovid19.ProfissionalSaude.SIM.getValue(),
                fichaCovidDTO.getInvestigacaoAgravoCovid19().getProfissionalSaude());

        regrasFichaCovid19 = new RegrasFichaInvestigacaoAgravoCovid19(null, null);
        regrasFichaCovid19.configuraInvestigacaoAgravo();
        assertNull(fichaCovidDTO.getInvestigacaoAgravoCovid19().getCodigo());
    }
}