<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type COCT_MT030000UV04.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:include schemaLocation="COCT_MT150000UV02.xsd"/>
   <xs:include schemaLocation="COCT_MT500000UV04.xsd"/>
   <xs:include schemaLocation="COCT_MT030202UV01.xsd"/>
   <xs:include schemaLocation="COCT_MT150002UV01.xsd"/>
   <xs:include schemaLocation="COCT_MT710000UV01.xsd"/>
   <xs:complexType name="COCT_MT030000UV04.BirthPlace">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="birthplace" type="COCT_MT710000UV01.Place" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="optional" default="BIRTHPL"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.Citizen">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="politicalOrganization" type="COCT_MT150000UV02.Organization"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="CIT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.ContactParty">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="contactPerson" type="COCT_MT030202UV01.Person" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="contactOrganization" type="COCT_MT150002UV01.Organization"
                        nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassContact" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.Employment">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="jobCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="jobTitleName" type="SC" minOccurs="0" maxOccurs="1"/>
         <xs:element name="jobClassCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="employerOrganization" type="COCT_MT150000UV02.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassEmployee" use="required"/>
      <xs:attribute name="negationInd" type="bl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.Entity">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="quantity" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="existenceTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassRoot" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.Guarantor">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="guarantorPerson" type="COCT_MT030202UV01.Person" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="guarantorOrganization" type="COCT_MT150002UV01.Organization"
                        nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="GUAR"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.Guardian">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="certificateText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="guardianPerson" type="COCT_MT030202UV01.Person" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="guardianOrganization" type="COCT_MT150002UV01.Organization"
                        nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="optional" default="GUARD"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.LanguageCommunication">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="languageCode" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="modeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="proficiencyLevelCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="preferenceInd" type="BL" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.Member">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="groupEntity" type="COCT_MT030000UV04.Entity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="MBR"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.NonPersonLivingSubject">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="quantity" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="existenceTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="riskCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="handlingCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="administrativeGenderCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="birthTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="deceasedInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="multipleBirthInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="multipleBirthOrderNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="organDonorInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="strainText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="genderStatusCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asMember" type="COCT_MT030000UV04.Member" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asOtherIDs" type="COCT_MT030000UV04.OtherIDs" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asCoveredParty" type="COCT_MT500000UV04.CoveredParty" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="contactParty" type="COCT_MT030000UV04.ContactParty" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="guardian" type="COCT_MT030000UV04.Guardian" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="guarantor" type="COCT_MT030000UV04.Guarantor" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="birthPlace" type="COCT_MT030000UV04.BirthPlace" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassNonPersonLivingSubject" use="required"/>
      <xs:attribute name="determinerCode" type="x_DeterminerInstanceKind" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.OtherIDs">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="scopingOrganization" type="COCT_MT150000UV02.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassRoot" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.Person">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="administrativeGenderCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="birthTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="deceasedInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="deceasedTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="multipleBirthInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="multipleBirthOrderNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="organDonorInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="maritalStatusCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="educationLevelCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="disabilityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="livingArrangementCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="religiousAffiliationCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="raceCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="ethnicGroupCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="asEmployment" type="COCT_MT030000UV04.Employment" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asCitizen" type="COCT_MT030000UV04.Citizen" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asStudent" type="COCT_MT030000UV04.Student" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asMember" type="COCT_MT030000UV04.Member" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asOtherIDs" type="COCT_MT030000UV04.OtherIDs" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asCoveredParty" type="COCT_MT500000UV04.CoveredParty" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="contactParty" type="COCT_MT030000UV04.ContactParty" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="guardian" type="COCT_MT030000UV04.Guardian" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="guarantor" type="COCT_MT030000UV04.Guarantor" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="birthPlace" type="COCT_MT030000UV04.BirthPlace" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="languageCommunication" type="COCT_MT030000UV04.LanguageCommunication"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="PSN"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT030000UV04.Student">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="schoolOrganization" type="COCT_MT150000UV02.Organization" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="STD"/>
   </xs:complexType>
</xs:schema>