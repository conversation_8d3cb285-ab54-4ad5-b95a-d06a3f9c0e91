<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type COCT_MT510000UV06.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:include schemaLocation="COCT_MT050000UV01.xsd"/>
   <xs:include schemaLocation="COCT_MT150000UV02.xsd"/>
   <xs:include schemaLocation="COCT_MT030007UV.xsd"/>
   <xs:include schemaLocation="COCT_MT810000UV.xsd"/>
   <xs:include schemaLocation="COCT_MT280000UV04.xsd"/>
   <xs:include schemaLocation="COCT_MT290000UV06.xsd"/>
   <xs:include schemaLocation="COCT_MT490000UV04.xsd"/>
   <xs:include schemaLocation="COCT_MT300000UV04.xsd"/>
   <xs:include schemaLocation="COCT_MT310000UV04.xsd"/>
   <xs:include schemaLocation="COCT_MT600000UV06.xsd"/>
   <xs:include schemaLocation="COCT_MT740000UV04.xsd"/>
   <xs:include schemaLocation="COCT_MT530000UV.xsd"/>
   <xs:include schemaLocation="COCT_MT060000UV01.xsd"/>
   <xs:complexType name="COCT_MT510000UV06.Author2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="functionCode" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="underwriter" type="COCT_MT510000UV06.Underwriter" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="AUT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Beneficiary">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="memberOrganization1" type="COCT_MT150000UV02.Organization"
                        nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
            <xs:choice>
               <xs:element name="memberPerson" type="COCT_MT030007UV.Person" nillable="true" minOccurs="0"
                           maxOccurs="1"/>
               <xs:element name="memberNonPersonLivingSubject"
                           type="COCT_MT030007UV.NonPersonLivingSubject"
                           nillable="true"
                           minOccurs="0"
                           maxOccurs="1"/>
            </xs:choice>
         </xs:choice>
         <xs:element name="groupOrganization" type="COCT_MT510000UV06.Organization" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="MBR"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Beneficiary2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:choice>
            <xs:element name="patient" type="COCT_MT050000UV01.Patient" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="beneficiary" type="COCT_MT510000UV06.Beneficiary" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="BEN"/>
      <xs:attribute name="negationInd" type="bl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Benefit">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="reasonCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="definition" type="COCT_MT510000UV06.Definition" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="precondition" type="COCT_MT510000UV06.Precondition" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="reference" type="COCT_MT510000UV06.Reference" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="limitation" type="COCT_MT510000UV06.Limitation2" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassCareProvision" use="required"/>
      <xs:attribute name="moodCode" type="ActMoodCompletionTrack" use="required"/>
      <xs:attribute name="negationInd" type="bl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Component">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="priorityNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="policyOrProgram" type="COCT_MT510000UV06.PolicyOrProgram" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipHasComponent" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Coverage2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="benefit" type="COCT_MT510000UV06.Benefit" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="COVBY"/>
      <xs:attribute name="negationInd" type="bl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.CoverageCharge">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="unitQuantity" type="RTO_PQ_PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="unitPriceAmt" type="RTO_MO_PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="netAmt" type="MO" minOccurs="0" maxOccurs="1"/>
         <xs:element name="factorNumber" type="REAL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="reference" type="COCT_MT510000UV06.Reference2" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="INVE"/>
      <xs:attribute name="moodCode" type="ActMoodCriterion" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.CoverageChargePolicy">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="reasonCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="POL"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="DEF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.CoverageDefinition">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="title" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ST" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="COV"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="DEF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.CoverageLimitObservation">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="CE" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="DEF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.CoveragePolicy">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="reasonCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="POL"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="DEF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.CoverageRecord">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="beneficiary" type="COCT_MT510000UV06.Beneficiary2" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="component" type="COCT_MT510000UV06.Component" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="COV"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.CoveredParty">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="coveredOrganization1" type="COCT_MT150000UV02.Organization"
                        nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
            <xs:choice>
               <xs:element name="coveredPerson" type="COCT_MT030007UV.Person" nillable="true"
                           minOccurs="0"
                           maxOccurs="1"/>
               <xs:element name="coveredNonPersonLivingSubject"
                           type="COCT_MT030007UV.NonPersonLivingSubject"
                           nillable="true"
                           minOccurs="0"
                           maxOccurs="1"/>
            </xs:choice>
         </xs:choice>
         <xs:element name="underwritingOrganization" type="COCT_MT150000UV02.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="subjectOf1" type="COCT_MT510000UV06.Subject" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf2" type="COCT_MT510000UV06.Subject3" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="part" type="COCT_MT510000UV06.Part" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="indirectAuthority1" type="COCT_MT510000UV06.IndirectAuthorithyOver"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="indirectAuthority2" type="COCT_MT510000UV06.IndirectAuthorithyOver2"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassCoveredParty" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.CoveredParty2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="coveredParty" type="COCT_MT510000UV06.CoveredParty" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="optional" default="COV"/>
      <xs:attribute name="negationInd" type="bl" use="optional" default="true"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Definition">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:choice>
            <xs:choice>
               <xs:element name="billableClinicalService1"
                           type="COCT_MT290000UV06.BillableClinicalService"
                           nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="billableClinicalProduct1"
                           type="COCT_MT490000UV04.BillableClinicalProduct"
                           nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="supplyEvent1" type="COCT_MT300000UV04.SupplyEvent" nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="accomodationSupplied1" type="COCT_MT310000UV04.AccomodationSupplied"
                           nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="supplyEvent2" type="COCT_MT600000UV06.SupplyEvent" nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="oralHealthService1" type="COCT_MT740000UV04.OralHealthService"
                           nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="crossReference1" type="COCT_MT280000UV04.CrossReference" nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
            </xs:choice>
            <xs:choice>
               <xs:choice>
                  <xs:element name="observation" type="COCT_MT530000UV.Observation" nillable="true"
                              minOccurs="1"
                              maxOccurs="1"/>
                  <xs:element name="substanceAdministration" type="COCT_MT530000UV.SubstanceAdministration"
                              nillable="true"
                              minOccurs="1"
                              maxOccurs="1"/>
                  <xs:element name="supply" type="COCT_MT530000UV.Supply" nillable="true" minOccurs="1"
                              maxOccurs="1"/>
                  <xs:element name="procedure" type="COCT_MT530000UV.Procedure" nillable="true" minOccurs="1"
                              maxOccurs="1"/>
                  <xs:element name="encounter" type="COCT_MT530000UV.Encounter" nillable="true" minOccurs="1"
                              maxOccurs="1"/>
                  <xs:element name="act" type="COCT_MT530000UV.Act" nillable="true" minOccurs="1"
                              maxOccurs="1"/>
                  <xs:element name="organizer" type="COCT_MT530000UV.Organizer" nillable="true" minOccurs="1"
                              maxOccurs="1"/>
               </xs:choice>
               <xs:element name="actReference" type="COCT_MT530000UV.ActReference" nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
            </xs:choice>
            <xs:element name="transportation" type="COCT_MT060000UV01.Transportation" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="serviceDefinition" type="COCT_MT510000UV06.ServiceDefinition"
                        nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="INST"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Definition3">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="coverageDefinition" type="COCT_MT510000UV06.CoverageDefinition"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="INST"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.DirectAuthorityOver">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="underwriter" type="COCT_MT510000UV06.Underwriter" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="RoleLinkType" use="required" fixed="DIRAUTH"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.DirectAuthorityOver2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="payor" type="COCT_MT510000UV06.Payor" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="RoleLinkType" use="required" fixed="DIRAUTH"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.DirectAuthorityOver3">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="underwriter" type="COCT_MT510000UV06.Underwriter" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="RoleLinkType" use="required" fixed="DIRAUTH"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.DirectAuthorityOver4">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="payor" type="COCT_MT510000UV06.Payor" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="RoleLinkType" use="required" fixed="DIRAUTH"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.EligibilityStatusObservation">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="ED" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.FinancialParticipationCharge">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="unitQuantity" type="RTO_PQ_PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="unitPriceAmt" type="RTO_MO_PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="netAmt" type="MO" minOccurs="0" maxOccurs="1"/>
         <xs:element name="factorNumber" type="REAL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="reference" type="COCT_MT510000UV06.Reference2" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="INVE"/>
      <xs:attribute name="moodCode" type="ActMoodCriterion" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Holder">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="policyHolder" type="COCT_MT510000UV06.PolicyHolder" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="HLD"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.IndirectAuthorithyOver">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="responsibleParty" type="COCT_MT510000UV06.ResponsibleParty"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="RoleLinkType" use="required" fixed="INDAUTH"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.IndirectAuthorithyOver2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="policyHolder" type="COCT_MT510000UV06.PolicyHolder" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="RoleLinkType" use="required" fixed="INDAUTH"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Limitation">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="coverageLimitObservation"
                     type="COCT_MT510000UV06.CoverageLimitObservation"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="LIMIT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Limitation2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:choice>
            <xs:element name="coverageCharge" type="COCT_MT510000UV06.CoverageCharge" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="financialParticipationCharge"
                        type="COCT_MT510000UV06.FinancialParticipationCharge"
                        nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="LIMIT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Limitation3">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="policyOrProgramFinancialLimit"
                     type="COCT_MT510000UV06.PolicyOrProgramFinancialLimit"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="LIMIT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Organization">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="ON" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassOrganization" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Part">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="priorityNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="personalRelationship" type="COCT_MT510000UV06.PersonalRelationship"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="RoleLinkType" use="required" fixed="PART"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Payor">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="ON" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="invoicePayorOrganization" type="COCT_MT150000UV02.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="underwritingOrganization" type="COCT_MT150000UV02.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="directAuthorityOver" type="COCT_MT510000UV06.DirectAuthorityOver4"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="PAYOR"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Person">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="PN" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="birthTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="PSN"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.PersonalRelationship">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="relationshipHolder" type="COCT_MT510000UV06.Person" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="personalRelationshipWith" type="COCT_MT510000UV06.Person" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="PRS"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.PolicyHolder">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:choice>
            <xs:element name="policyHolderPerson" type="COCT_MT510000UV06.Person" nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
            <xs:element name="policyHolderOrganization" type="COCT_MT510000UV06.Organization"
                        nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
         </xs:choice>
         <xs:element name="underwritingOrganization" type="COCT_MT150000UV02.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="POLHOLD"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.PolicyOrProgram">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="1" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="coveredParty" type="COCT_MT510000UV06.CoveredParty2" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
         <xs:element name="holder" type="COCT_MT510000UV06.Holder" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="responsibleParty" type="COCT_MT510000UV06.ResponsibleParty2"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="primaryPerformer" type="COCT_MT510000UV06.PrimaryPerformer"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="author" type="COCT_MT510000UV06.Author2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="definition" type="COCT_MT510000UV06.Definition3" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="replacementOf" type="COCT_MT510000UV06.ReplacementOf" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="limitation1" type="COCT_MT510000UV06.Limitation3" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="limitation2" type="COCT_MT510000UV06.Limitation" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="coverageOf" type="COCT_MT510000UV06.Coverage2" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="COV"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.PolicyOrProgramFinancialLimit">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="netAmt" type="MO" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="INVE"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="DEF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Precondition">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:choice>
            <xs:choice>
               <xs:element name="observation" type="COCT_MT530000UV.Observation" nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="substanceAdministration" type="COCT_MT530000UV.SubstanceAdministration"
                           nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="supply" type="COCT_MT530000UV.Supply" nillable="true" minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="procedure" type="COCT_MT530000UV.Procedure" nillable="true" minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="encounter" type="COCT_MT530000UV.Encounter" nillable="true" minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="act" type="COCT_MT530000UV.Act" nillable="true" minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="organizer" type="COCT_MT530000UV.Organizer" nillable="true" minOccurs="1"
                           maxOccurs="1"/>
            </xs:choice>
            <xs:element name="actReference" type="COCT_MT530000UV.ActReference" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="PRCN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.PreviousPolicyOrProgram">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="statusCode" type="CS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="COV"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.PrimaryPerformer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="functionCode" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="payor" type="COCT_MT510000UV06.Payor" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="PPRF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Reference">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="coveragePolicy" type="COCT_MT510000UV06.CoveragePolicy" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="REFR"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Reference2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="coverageChargePolicy" type="COCT_MT510000UV06.CoverageChargePolicy"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="REFR"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.ReplacementOf">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="previousPolicyOrProgram" type="COCT_MT510000UV06.PreviousPolicyOrProgram"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="RPLC"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.ResponsibleParty">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="agentPerson" type="COCT_MT510000UV06.Person" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:choice>
            <xs:element name="representedPerson" type="COCT_MT510000UV06.Person" nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
            <xs:element name="representedOrganization" type="COCT_MT510000UV06.Organization"
                        nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassAgent" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.ResponsibleParty2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="functionCode" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="sponsor" type="COCT_MT510000UV06.Sponsor" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="RESP"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.ServiceDefinition">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassRoot" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="DEF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Sponsor">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="ON" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="sponsorOrganization" type="COCT_MT150000UV02.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="underwritingOrganization" type="COCT_MT150000UV02.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="directAuthorityOver" type="COCT_MT510000UV06.DirectAuthorityOver3"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="SPNSR"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Subject">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="eligibilityStatusObservation"
                     type="COCT_MT510000UV06.EligibilityStatusObservation"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Subject3">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="verification" type="COCT_MT810000UV.Verification" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT510000UV06.Underwriter">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="ON" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="underwritingOrganization" type="COCT_MT150000UV02.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="scoperOrganization" type="COCT_MT150000UV02.Organization" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="directAuthorityOver1" type="COCT_MT510000UV06.DirectAuthorityOver2"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="directAuthorityOver2" type="COCT_MT510000UV06.DirectAuthorityOver"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="UNDWRT"/>
   </xs:complexType>
</xs:schema>