<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:mif="urn:hl7-org:v3/mif"
           xmlns:v3="urn:hl7-org:v3"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified">
   <xs:annotation xmlns="urn:hl7-org:v3">
      <xs:documentation>Source Information
     Rendered by: RoseTree 4.2.7
     Rendered on: 2008-03-22T24:01:25
This model was rendered into XML using software provided to HL7 by Beeler Consulting LLC.
 Transform: $RCSfile: RoseTreeRimToMIFStaticModel.xsl,v $ $Revision: 1.8 $ $Date: 2007/10/19 05:55:13 $
 Generated using schema builder version: 3.1.6
 RIM MIF Infrastructure Root to Schema Transform: $Id: RimInfrastructureRootToXsd.xsl,v 1.4 2007/03/20 02:48:50 wbeeler Exp $
  Static MIF to Schema Transform: $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
  Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="voc.xsd"/>
   <xs:include schemaLocation="datatypes.xsd"/>
   <xs:group name="InfrastructureRootElements">
      <xs:sequence>
         <xs:element xmlns="urn:hl7-org:v3" name="realmCode" type="CS" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element xmlns="urn:hl7-org:v3" name="typeId" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element xmlns="urn:hl7-org:v3" name="templateId" type="II" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
   </xs:group>
   <xs:attributeGroup name="InfrastructureRootAttributes"/>
</xs:schema>