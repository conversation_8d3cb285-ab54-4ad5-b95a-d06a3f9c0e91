<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type COCT_MT500000UV04.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:complexType name="COCT_MT500000UV04.AccommodationCoverage">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CS" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="ACCM"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="DEF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.Author">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="carrierRole" type="COCT_MT500000UV04.CarrierRole" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="AUT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.Beneficiary">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="policyOrAccount" type="COCT_MT500000UV04.PolicyOrAccount" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="BEN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.CarrierOrganization">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="TN" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassOrganization" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.CarrierRole">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="underwritingCarrierOrganization"
                     type="COCT_MT500000UV04.CarrierOrganization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="UNDWRT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.CoveredParty">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="code" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="beneficiaryOf" type="COCT_MT500000UV04.Beneficiary" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassCoveredParty" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.EmployerOrganization">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="ON" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassOrganization" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.Employment">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="employerOrganization" type="COCT_MT500000UV04.EmployerOrganization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassEmployee" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.Holder">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="policyHolder" type="COCT_MT500000UV04.PolicyHolder" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="HLD"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.Limitation">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="accommodationCoverage" type="COCT_MT500000UV04.AccommodationCoverage"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="LIMIT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.PolicyHolder">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="policyHolderPerson" type="COCT_MT500000UV04.PolicyHolderPerson"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="policyHolderOrganization"
                        type="COCT_MT500000UV04.PolicyHolderOrganization"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="POLHOLD"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.PolicyHolderOrganization">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="TN" minOccurs="0" maxOccurs="1"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassOrganization" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.PolicyHolderPerson">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="PN" minOccurs="1" maxOccurs="1"/>
         <xs:element name="birthTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asEmployment" type="COCT_MT500000UV04.Employment" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="PSN"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT500000UV04.PolicyOrAccount">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="holder" type="COCT_MT500000UV04.Holder" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="author" type="COCT_MT500000UV04.Author" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
         <xs:element name="limitation" type="COCT_MT500000UV04.Limitation" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="COV"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
</xs:schema>