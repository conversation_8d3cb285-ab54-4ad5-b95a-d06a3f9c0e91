<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type COCT_MT080000UV.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:include schemaLocation="COCT_MT150000UV02.xsd"/>
   <xs:include schemaLocation="COCT_MT070000UV01.xsd"/>
   <xs:include schemaLocation="COCT_MT090000UV01.xsd"/>
   <xs:complexType name="COCT_MT080000UV.ActRef">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassRoot" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Additive">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="quantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="additive" type="COCT_MT080000UV.AdditiveMaterial" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="ADTV"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Additive2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="quantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="additive" type="COCT_MT080000UV.AdditiveMaterial" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="ADTV"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.AdditiveMaterial">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.AuthorOrPerformer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="noteText" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assignedEntity" type="COCT_MT090000UV01.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="x_ParticipationAuthorPerformer" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="ON"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.AutomationSpecimenObservationEvent">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="text" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="ANY" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="optional" fixed="SPCOBS"/>
      <xs:attribute name="moodCode" type="ActMood" use="optional" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Container">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="riskCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="handlingCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="capacityQuantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="heightQuantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="diameterQuantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="capTypeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="separatorTypeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="barrierDeltaQuantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="bottomDeltaQuantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asIdentifiedContainer" type="COCT_MT080000UV.IdentifiedContainer"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="asContent" type="COCT_MT080000UV.Content3" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="asLocatedEntity" type="COCT_MT070000UV01.LocatedEntity" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="additive" type="COCT_MT080000UV.Additive2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassContainer" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Content1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="container" type="COCT_MT080000UV.Container" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="CONT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Content3">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="positionNumber" type="INT" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="containerHolder" type="COCT_MT080000UV.Holder" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="CONT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Content4">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="positionNumber" type="INT" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="containerHolder" type="COCT_MT080000UV.Holder" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="CONT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Criterion">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="ANY" minOccurs="0" maxOccurs="1"/>
         <xs:element name="interpretationCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN.CRT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Holder">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asIdentifiedHolder" type="COCT_MT080000UV.IdentifiedHolder"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asContent" type="COCT_MT080000UV.Content4" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="asLocatedEntity" type="COCT_MT070000UV01.LocatedEntity" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="HOLD"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.IdentifiedContainer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="IDENT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.IdentifiedHolder">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="optional" default="IDENT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Manufactured">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="quantity" type="PQ" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="riskCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="handlingCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="lotNumberText" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="expirationTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asSpecimenAlternateIdentifier"
                     type="COCT_MT080000UV.SpecimenAlternateIdentifier"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asManufacturedProduct" type="COCT_MT080000UV.ManufacturedProduct"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
         <xs:element name="asSpecimenStub" type="COCT_MT080000UV.SpecimenStub" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asContent" type="COCT_MT080000UV.Content1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="additive" type="COCT_MT080000UV.Additive" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassManufacturedMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.ManufacturedProduct">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="manufacturerOrganization" type="COCT_MT150000UV02.Organization"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassManufacturedProduct" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Natural">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="quantity" type="PQ" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="riskCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="handlingCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asSpecimenAlternateIdentifier"
                     type="COCT_MT080000UV.SpecimenAlternateIdentifier"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asSpecimenStub" type="COCT_MT080000UV.SpecimenStub" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asContent" type="COCT_MT080000UV.Content1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="additive" type="COCT_MT080000UV.Additive" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassRoot" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.NonPersonLivingSubject">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="quantity" type="PQ" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="existenceTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="riskCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="handlingCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="administrativeGenderCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="birthTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="deceasedInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="deceasedTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="multipleBirthInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="multipleBirthOrderNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="organDonorInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="strainText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="genderStatusCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asSpecimenAlternateIdentifier"
                     type="COCT_MT080000UV.SpecimenAlternateIdentifier"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asSpecimenStub" type="COCT_MT080000UV.SpecimenStub" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asContent" type="COCT_MT080000UV.Content1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="additive" type="COCT_MT080000UV.Additive" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassNonPersonLivingSubject" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Performer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="noteText" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assignedEntity" type="COCT_MT090000UV01.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationPhysicalPerformer" use="optional"
                    default="PRF"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="OP"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Person">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="quantity" type="PQ" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="existenceTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="riskCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="handlingCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="administrativeGenderCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="birthTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="deceasedInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="deceasedTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="multipleBirthInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="multipleBirthOrderNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="organDonorInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="disabilityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="raceCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="ethnicGroupCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="asSpecimenAlternateIdentifier"
                     type="COCT_MT080000UV.SpecimenAlternateIdentifier"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asSpecimenStub" type="COCT_MT080000UV.SpecimenStub" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asContent" type="COCT_MT080000UV.Content1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="additive" type="COCT_MT080000UV.Additive" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="PSN"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Precondition">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="priorityNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="conjunctionCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="criterion" type="COCT_MT080000UV.Criterion" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="PRCN"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="AN"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional" default="false"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Process">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="text" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="SXCM_TS" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="approachSiteCode" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="targetSiteCode" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="performer" type="COCT_MT080000UV.Performer" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="precondition" type="COCT_MT080000UV.Precondition" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassProcedure" use="required"/>
      <xs:attribute name="moodCode" type="x_ActMoodIntentEvent" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.ProcessStep">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="subject1" type="COCT_MT080000UV.Subject1" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subject2" type="COCT_MT080000UV.Subject2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="authorOrPerformer" type="COCT_MT080000UV.AuthorOrPerformer"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="x_LabProcessClassCodes" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Product">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="process" type="COCT_MT080000UV.Process" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="actRef" type="COCT_MT080000UV.ActRef" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="PRD"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="ON"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Specimen">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="specimenNatural" type="COCT_MT080000UV.Natural" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="specimenManufactured" type="COCT_MT080000UV.Manufactured" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="specimenNonPersonLivingSubject"
                        type="COCT_MT080000UV.NonPersonLivingSubject"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="specimenPerson" type="COCT_MT080000UV.Person" minOccurs="1" maxOccurs="1"/>
         </xs:choice>
         <xs:choice>
            <xs:element name="sourceNatural" type="COCT_MT080000UV.Natural" nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
            <xs:element name="sourceManufactured" type="COCT_MT080000UV.Manufactured" nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
            <xs:element name="sourceNonPersonLivingSubject"
                        type="COCT_MT080000UV.NonPersonLivingSubject"
                        nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
            <xs:element name="sourcePerson" type="COCT_MT080000UV.Person" nillable="true" minOccurs="0"
                        maxOccurs="1"/>
         </xs:choice>
         <xs:element name="subjectOf1" type="COCT_MT080000UV.Subject4" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf2" type="COCT_MT080000UV.Subject3" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="productOf" type="COCT_MT080000UV.Product" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassSpecimen" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.SpecimenAlternateIdentifier">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assigningOrganization" type="COCT_MT150000UV02.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="IDENT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.SpecimenObservationEvent">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="text" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="ANY" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="optional" fixed="OBS"/>
      <xs:attribute name="moodCode" type="ActMood" use="optional" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.SpecimenStub">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassSpecimen" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Subject1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="identifiedContainer" type="COCT_MT080000UV.IdentifiedContainer"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="ON"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Subject2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="identifiedHolder" type="COCT_MT080000UV.IdentifiedHolder" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="optional" default="SBJ"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="ON"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Subject3">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="processStep" type="COCT_MT080000UV.ProcessStep" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="ON"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT080000UV.Subject4">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="specimenObservationEvent" type="COCT_MT080000UV.SpecimenObservationEvent"
                        nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="automationSpecimenObservationEvent"
                        type="COCT_MT080000UV.AutomationSpecimenObservationEvent"
                        nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="ON"/>
   </xs:complexType>
</xs:schema>