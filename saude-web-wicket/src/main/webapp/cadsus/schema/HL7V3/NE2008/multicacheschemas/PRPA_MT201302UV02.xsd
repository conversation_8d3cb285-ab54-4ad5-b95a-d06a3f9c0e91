<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ex="urn:hl7-org/v3-example" targetNamespace="urn:hl7-org:v3" elementFormDefault="qualified">
	<!--
*****************************************************************************************************************
* XML schema for message type PRPA_MT201302UV02.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  -->
	<xs:annotation>
		<xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
	</xs:annotation>
	<xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
	<xs:include schemaLocation="COCT_MT150007UV.xsd"/>
	<xs:include schemaLocation="COCT_MT820000UV.xsd"/>
	<xs:include schemaLocation="COCT_MT150002UV01.xsd"/>
	<xs:include schemaLocation="COCT_MT030007UV.xsd"/>
	<xs:include schemaLocation="COCT_MT030207UV.xsd"/>
	<xs:include schemaLocation="COCT_MT710007UV.xsd"/>
	<xs:include schemaLocation="COCT_MT960000UV05.xsd"/>
	<xs:include schemaLocation="COCT_MT670000UV04.xsd"/>
	<xs:include schemaLocation="COCT_MT150003UV03.xsd"/>
	<xs:include schemaLocation="COCT_MT510000UV06.xsd"/>
	<xs:complexType name="PRPA_MT201302UV02.AdministrativeObservation.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.AdministrativeObservation.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.AdministrativeObservation.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.AdministrativeObservation">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.AdministrativeObservation.id" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="code" type="CD"/>
			<xs:element name="statusCode" type="CS" minOccurs="0"/>
			<xs:element name="effectiveTime" type="SXCM_TS" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="value" type="ANY"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="ActClassObservation" use="required"/>
		<xs:attribute name="moodCode" type="ActMood" use="optional" fixed="EVN"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.BirthPlace">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="addr" type="AD" minOccurs="0"/>
			<xs:element name="birthplace" type="COCT_MT710007UV.Place" nillable="true" minOccurs="0"/>
			<xs:element name="subjectOf" type="PRPA_MT201302UV02.Subject2" nillable="true" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClass" use="optional" fixed="BIRTHPL"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.CareGiver.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.CareGiver.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.CareGiver.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.CareGiver">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.CareGiver.id" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="code" type="CE" minOccurs="0"/>
			<xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="statusCode" type="CS" minOccurs="0"/>
			<xs:element name="effectiveTime" type="IVL_TS" minOccurs="0"/>
			<xs:element name="careGiverPerson" type="COCT_MT030207UV.Person" nillable="true"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClass" use="optional" fixed="CAREGIVER"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Citizen.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.Citizen.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.Citizen.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.Citizen">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.Citizen.id" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="effectiveTime" type="IVL_TS" minOccurs="0"/>
			<xs:element name="politicalNation" type="PRPA_MT201302UV02.Nation"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClass" use="optional" fixed="CIT"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.ContactParty.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.ContactParty.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.ContactParty.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.ContactParty">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.ContactParty.id" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="code" type="CE" minOccurs="0"/>
			<xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="telecom" type="TEL" maxOccurs="unbounded"/>
			<xs:element name="statusCode" type="CS" minOccurs="0"/>
			<xs:element name="effectiveTime" type="IVL_TS" minOccurs="0"/>
			<xs:choice>
				<xs:element name="contactPerson" type="COCT_MT030207UV.Person" nillable="true"/>
				<xs:element name="contactOrganization" type="COCT_MT150007UV.Organization" nillable="true"/>
			</xs:choice>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClassContact" use="required"/>
		<xs:attribute name="negationInd" type="bl" use="optional"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.CoveredParty">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="sequenceNumber" type="INT" minOccurs="0"/>
			<xs:element name="time" type="IVL_TS" minOccurs="0"/>
			<xs:element name="coverageRecord" type="COCT_MT510000UV06.CoverageRecord" nillable="true"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="COV"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Employee.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.Employee.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.Employee.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.Employee">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.Employee.id" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="statusCode" type="CS" minOccurs="0"/>
			<xs:element name="effectiveTime" type="IVL_TS" minOccurs="0"/>
			<xs:element name="jobTitleName" type="SC" minOccurs="0"/>
			<xs:element name="jobClassCode" type="CE" minOccurs="0"/>
			<xs:element name="occupationCode" type="CE" minOccurs="0"/>
			<xs:element name="employerOrganization" type="COCT_MT150007UV.Organization" nillable="true"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClassEmployee" use="required"/>
		<xs:attribute name="negationInd" type="bl" use="optional"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Group">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="code" type="CE" minOccurs="0"/>
			<xs:element name="name" type="EN" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="desc" type="ED" minOccurs="0"/>
			<xs:element name="statusCode" type="CS" minOccurs="0"/>
			<xs:element name="existenceTime" type="IVL_TS" minOccurs="0"/>
			<xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="addr" type="AD" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="EntityClassOrganization" use="required"/>
		<xs:attribute name="determinerCode" type="EntityDeterminer" use="optional" fixed="INSTANCE"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Guardian.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.Guardian.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.Guardian.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.Guardian">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.Guardian.id" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="statusCode" type="CS" minOccurs="0"/>
			<xs:element name="effectiveTime" type="IVL_TS" minOccurs="0"/>
			<xs:element name="certificateText" type="ED" minOccurs="0"/>
			<xs:choice>
				<xs:element name="guardianPerson" type="COCT_MT030207UV.Person" nillable="true"/>
				<xs:element name="guardianOrganization" type="COCT_MT150007UV.Organization" nillable="true"/>
			</xs:choice>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClass" use="optional" fixed="GUARD"/>
		<xs:attribute name="negationInd" type="bl" use="optional"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.LanguageCommunication">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="languageCode" type="CE"/>
			<xs:element name="modeCode" type="CE" minOccurs="0"/>
			<xs:element name="proficiencyLevelCode" type="CE" minOccurs="0"/>
			<xs:element name="preferenceInd" type="BL" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Member.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.Member.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.Member.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.Member">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.Member.id" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="code" type="CE" minOccurs="0"/>
			<xs:element name="statusCode" type="CS" minOccurs="0"/>
			<xs:element name="effectiveTime" type="IVL_TS" minOccurs="0"/>
			<xs:element name="group" type="PRPA_MT201302UV02.Group" nillable="true"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClass" use="required" fixed="MBR"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Nation">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="code" type="CD"/>
			<xs:element name="name" type="ON" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="EntityClass" use="optional" fixed="NAT"/>
		<xs:attribute name="determinerCode" type="EntityDeterminer" use="optional" fixed="INSTANCE"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.NonPersonLivingSubject.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.NonPersonLivingSubject.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.NonPersonLivingSubject.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.NonPersonLivingSubject">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.NonPersonLivingSubject.id" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="code" type="CE" minOccurs="0"/>
			<xs:element name="name" type="EN" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="desc" type="ED" minOccurs="0"/>
			<xs:element name="existenceTime" type="IVL_TS" minOccurs="0"/>
			<xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="riskCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="handlingCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="administrativeGenderCode" type="CE" minOccurs="0"/>
			<xs:element name="birthTime" type="TS" minOccurs="0"/>
			<xs:element name="deceasedInd" type="BL" minOccurs="0"/>
			<xs:element name="deceasedTime" type="TS" minOccurs="0"/>
			<xs:element name="multipleBirthInd" type="BL" minOccurs="0"/>
			<xs:element name="multipleBirthOrderNumber" type="INT" minOccurs="0"/>
			<xs:element name="organDonorInd" type="BL" minOccurs="0"/>
			<xs:element name="strainText" type="ED" minOccurs="0"/>
			<xs:element name="genderStatusCode" type="CE" minOccurs="0"/>
			<xs:element name="asPatientOfOtherProvider" type="PRPA_MT201302UV02.PatientOfOtherProvider" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="asMember" type="PRPA_MT201302UV02.Member" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="asOtherIDs" type="PRPA_MT201302UV02.OtherIDs" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="contactParty" type="PRPA_MT201302UV02.ContactParty" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="guardian" type="PRPA_MT201302UV02.Guardian" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="personalRelationship" type="PRPA_MT201302UV02.PersonalRelationship" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="careGiver" type="PRPA_MT201302UV02.CareGiver" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="birthPlace" type="PRPA_MT201302UV02.BirthPlace" nillable="true" minOccurs="0"/>
			<xs:element name="guarantorRole" type="COCT_MT670000UV04.GuarantorRole" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="EntityClassNonPersonLivingSubject" use="required"/>
		<xs:attribute name="determinerCode" type="EntityDeterminer" use="optional" fixed="INSTANCE"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.OtherIDs.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.OtherIDs.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.OtherIDs.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.OtherIDs">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.OtherIDs.id" maxOccurs="unbounded"/>
			<xs:element name="statusCode" type="CS" minOccurs="0"/>
			<xs:element name="effectiveTime" type="IVL_TS" minOccurs="0"/>
			<xs:element name="scopingOrganization" type="COCT_MT150002UV01.Organization" nillable="true"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClassRoot" use="required"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Patient.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.Patient.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.Patient.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.Patient.statusCode">
		<xs:complexContent>
			<xs:extension base="CS">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.Patient.statusCode.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.Patient.statusCode.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.Patient.patientPerson">
		<xs:complexContent>
			<xs:extension base="PRPA_MT201302UV02.Person">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.Patient.patientPerson.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.Patient.patientPerson.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.Patient.patientNonPersonLivingSubject">
		<xs:complexContent>
			<xs:extension base="PRPA_MT201302UV02.NonPersonLivingSubject">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.Patient.patientNonPersonLivingSubject.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.Patient.patientNonPersonLivingSubject.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.Patient">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.Patient.id" maxOccurs="unbounded"/>
			<xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="statusCode" type="PRPA_MT201302UV02.Patient.statusCode"/>
			<xs:element name="effectiveTime" type="IVL_TS" minOccurs="0"/>
			<xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="veryImportantPersonCode" type="CE" minOccurs="0"/>
			<xs:choice>
				<!-- PLEASE NOTE: The fopllowing two lines were edited manually to create a valid schema.  V3 Generator Release 3.1.6 fails to yield
        a valid schema for this design. (Probably because of the combination of a "choice" and an updateMode attribute -->
				<xs:element name="patientPerson" type="PRPA_MT201302UV02.Patient.patientPerson" nillable="true"/>
				<xs:element name="patientNonPersonLivingSubject" type="PRPA_MT201302UV02.Patient.patientNonPersonLivingSubject" nillable="true"/>
			</xs:choice>
			<xs:element name="providerOrganization" type="COCT_MT150003UV03.Organization" nillable="true" minOccurs="0"/>
			<xs:element name="subjectOf" type="PRPA_MT201302UV02.Subject4" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="coveredPartyOf" type="PRPA_MT201302UV02.CoveredParty" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClass" use="required" fixed="PAT"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.PatientOfOtherProvider">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="subjectOf" type="PRPA_MT201302UV02.Subject3"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClass" use="required" fixed="PAT"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Person.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.Person.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.Person.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.Person">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.Person.id" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="name" type="PN" maxOccurs="unbounded"/>
			<xs:element name="desc" type="ED" minOccurs="0"/>
			<xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="administrativeGenderCode" type="CE" minOccurs="0"/>
			<xs:element name="birthTime" type="TS" minOccurs="0"/>
			<xs:element name="deceasedInd" type="BL" minOccurs="0"/>
			<xs:element name="deceasedTime" type="TS" minOccurs="0"/>
			<xs:element name="multipleBirthInd" type="BL" minOccurs="0"/>
			<xs:element name="multipleBirthOrderNumber" type="INT" minOccurs="0"/>
			<xs:element name="organDonorInd" type="BL" minOccurs="0"/>
			<xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="maritalStatusCode" type="CE" minOccurs="0"/>
			<xs:element name="educationLevelCode" type="CE" minOccurs="0"/>
			<xs:element name="disabilityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="livingArrangementCode" type="CE" minOccurs="0"/>
			<xs:element name="religiousAffiliationCode" type="CE" minOccurs="0"/>
			<xs:element name="raceCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="ethnicGroupCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="asPatientOfOtherProvider" type="PRPA_MT201302UV02.PatientOfOtherProvider" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="asEmployee" type="PRPA_MT201302UV02.Employee" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="asCitizen" type="PRPA_MT201302UV02.Citizen" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="asStudent" type="PRPA_MT201302UV02.Student" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="asMember" type="PRPA_MT201302UV02.Member" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="asOtherIDs" type="PRPA_MT201302UV02.OtherIDs" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="contactParty" type="PRPA_MT201302UV02.ContactParty" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="guardian" type="PRPA_MT201302UV02.Guardian" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="personalRelationship" type="PRPA_MT201302UV02.PersonalRelationship" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="careGiver" type="PRPA_MT201302UV02.CareGiver" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="birthPlace" type="PRPA_MT201302UV02.BirthPlace" nillable="true" minOccurs="0"/>
			<xs:element name="guarantorRole" type="COCT_MT670000UV04.GuarantorRole" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="languageCommunication" type="PRPA_MT201302UV02.LanguageCommunication" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="EntityClass" use="optional" fixed="PSN"/>
		<xs:attribute name="determinerCode" type="EntityDeterminer" use="optional" fixed="INSTANCE"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.PersonalRelationship.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.PersonalRelationship.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.PersonalRelationship.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.PersonalRelationship">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.PersonalRelationship.id" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="code" type="CE"/>
			<xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="statusCode" type="CS" minOccurs="0"/>
			<xs:element name="effectiveTime" type="IVL_TS" minOccurs="0"/>
			<xs:choice>
				<xs:element name="relationshipHolder1" type="COCT_MT030007UV.Person" nillable="true"/>
				<xs:element name="relationshipHolder2" type="COCT_MT030007UV.NonPersonLivingSubject" nillable="true"/>
			</xs:choice>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClass" use="optional" fixed="PRS"/>
		<xs:attribute name="negationInd" type="bl" use="optional"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Student.id">
		<xs:complexContent>
			<xs:extension base="II">
				<xs:attribute name="updateMode" type="PRPA_MT201302UV02.Student.id.updateMode"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:simpleType name="PRPA_MT201302UV02.Student.id.updateMode">
		<xs:restriction base="xs:token">
			<xs:enumeration value="A"/>
			<xs:enumeration value="N"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PRPA_MT201302UV02.Student">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="id" type="PRPA_MT201302UV02.Student.id" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="statusCode" type="CS" minOccurs="0"/>
			<xs:element name="effectiveTime" type="IVL_TS" minOccurs="0"/>
			<xs:element name="schoolOrganization" type="COCT_MT150007UV.Organization" nillable="true" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="classCode" type="RoleClass" use="optional" fixed="STD"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Subject2">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="position" type="COCT_MT960000UV05.Position" nillable="true"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Subject3">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="careProvision" type="COCT_MT820000UV.CareProvision"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
	</xs:complexType>
	<xs:complexType name="PRPA_MT201302UV02.Subject4">
		<xs:sequence>
			<xs:group ref="InfrastructureRootElements"/>
			<xs:element name="administrativeObservation" type="PRPA_MT201302UV02.AdministrativeObservation" nillable="true"/>
		</xs:sequence>
		<xs:attributeGroup ref="InfrastructureRootAttributes"/>
		<xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
		<xs:attribute name="typeCode" type="ParticipationTargetSubject" use="optional" default="SBJ"/>
	</xs:complexType>
</xs:schema>
