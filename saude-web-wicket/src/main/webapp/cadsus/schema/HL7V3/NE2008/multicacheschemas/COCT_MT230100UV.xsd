<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type COCT_MT230100UV.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:include schemaLocation="COCT_MT440001UV.xsd"/>
   <xs:complexType name="COCT_MT230100UV.Agency">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="ON" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="PUB"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Approval">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="holder" type="COCT_MT230100UV.Holder" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="author" type="COCT_MT230100UV.Author" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassContract" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Author">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="time" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="territorialAuthority" type="COCT_MT230100UV.TerritorialAuthority"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="AUT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Characteristic">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="ANY" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Content">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="quantity" type="RTO_QTY_QTY" minOccurs="0" maxOccurs="1"/>
         <xs:element name="containerPackagedMedicine" type="COCT_MT230100UV.PackagedMedicine"
                     minOccurs="1"
                     maxOccurs="1"/>
         <xs:element name="subjectOf1" type="COCT_MT230100UV.Subject14" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf2" type="COCT_MT230100UV.Subject11" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="CONT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Country">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CD" minOccurs="1" maxOccurs="1"/>
         <xs:element name="name" type="TN" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassState" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.DistributedProduct">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="distributingManufacturer" type="COCT_MT230100UV.Manufacturer"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassDistributedMaterial" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Holder">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="role" type="COCT_MT230100UV.Role" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="HLD"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Ingredient">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="quantity" type="RTO_QTY_QTY" minOccurs="0" maxOccurs="1"/>
         <xs:element name="ingredient" type="COCT_MT230100UV.Substance" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassIngredientEntity" use="required"/>
      <xs:attribute name="negationInd" type="bl" use="optional" default="false"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.ManufacturedProduct">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="manufacturer" type="COCT_MT230100UV.Manufacturer" minOccurs="1"
                     maxOccurs="1"/>
         <xs:element name="subjectOf1" type="COCT_MT230100UV.Subject25" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf2" type="COCT_MT230100UV.Subject15" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf3" type="COCT_MT230100UV.Subject16" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassManufacturedProduct" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Manufacturer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="1"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asRelatedManufacturer" type="COCT_MT230100UV.RelatedManufacturer"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassOrganization" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Medication">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="administerableMedicine" type="COCT_MT230100UV.Medicine" minOccurs="1"
                     maxOccurs="1"/>
         <xs:element name="subjectOf1" type="COCT_MT230100UV.Subject2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf2" type="COCT_MT230100UV.Subject1" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf3" type="COCT_MT230100UV.Subject22" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf4" type="COCT_MT230100UV.Subject3" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf5" type="COCT_MT230100UV.Subject7" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="ADMM"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Medicine">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="TN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="riskCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="handlingCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="formCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="lotNumberText" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="expirationTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="stabilityTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asDistributedProduct" type="COCT_MT230100UV.DistributedProduct"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asMedicineManufacturer" type="COCT_MT230100UV.MedicineManufacturer"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asContent" type="COCT_MT230100UV.Content" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="asSpecializedKind" type="COCT_MT230100UV.SpecializedKind" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="part" type="COCT_MT230100UV.Part" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="ingredient" type="COCT_MT230100UV.Ingredient" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassManufacturedMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.MedicineClass">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="name" type="TN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="formCode" type="CE" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassManufacturedMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminerDetermined" use="optional"
                    fixed="KIND"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.MedicineManufacturer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="manufacturer" type="COCT_MT230100UV.Manufacturer" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassManufacturedProduct" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.ObservationGoal">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="ANY" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="GOL"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.PackagedMedicine">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="TN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="formCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="lotNumberText" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="capacityQuantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="capTypeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asManufacturedProduct" type="COCT_MT230100UV.ManufacturedProduct"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="asSuperContent" type="COCT_MT230100UV.SuperContent" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="subContent" type="COCT_MT230100UV.SubContent" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassContainer" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Part">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="quantity" type="RTO_QTY_QTY" minOccurs="0" maxOccurs="1"/>
         <xs:element name="partMedicine" type="COCT_MT230100UV.Medicine" minOccurs="1" maxOccurs="1"/>
         <xs:element name="subjectOf" type="COCT_MT230100UV.Subject4" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassPartitivePartByBOT" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Policy">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassRoot" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="DEF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.RelatedManufacturer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="representedManufacturer" type="COCT_MT230100UV.Manufacturer"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassAssignedEntity" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Role">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="playingManufacturer" type="COCT_MT230100UV.Manufacturer" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassRoot" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.SpecializedKind">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="generalizedMedicineClass" type="COCT_MT230100UV.MedicineClass"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassIsSpeciesEntity" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.SubContent">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="quantity" type="RTO_QTY_QTY" minOccurs="0" maxOccurs="1"/>
         <xs:element name="containedPackagedMedicine" type="COCT_MT230100UV.PackagedMedicine"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="CONT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.SubIngredient">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="quantity" type="RTO_QTY_QTY" minOccurs="0" maxOccurs="1"/>
         <xs:element name="ingredient" type="COCT_MT230100UV.Substance" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassIngredientEntity" use="required"/>
      <xs:attribute name="negationInd" type="bl" use="optional" default="false"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Subject1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="approval" type="COCT_MT230100UV.Approval" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Subject11">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="approval" type="COCT_MT230100UV.Approval" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Subject14">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="policy" type="COCT_MT230100UV.Policy" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Subject15">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="policy" type="COCT_MT230100UV.Policy" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Subject16">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="approval" type="COCT_MT230100UV.Approval" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Subject2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="policy" type="COCT_MT230100UV.Policy" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Subject22">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="characteristic" type="COCT_MT230100UV.Characteristic" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Subject25">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="characteristic" type="COCT_MT230100UV.Characteristic" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Subject3">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="observationGoal" type="COCT_MT230100UV.ObservationGoal" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Subject4">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="characteristic" type="COCT_MT230100UV.Characteristic" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Subject7">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="valuedItem" type="COCT_MT440001UV.ValuedItem" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="optional" fixed="SBJ"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.Substance">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="TN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="lotNumberText" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asSubstanceManufacturer" type="COCT_MT230100UV.SubstanceManufacturer"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subIngredient" type="COCT_MT230100UV.SubIngredient" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassManufacturedMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.SubstanceManufacturer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="manufacturer" type="COCT_MT230100UV.Manufacturer" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassManufacturedProduct" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.SuperContent">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="quantity" type="RTO_QTY_QTY" minOccurs="0" maxOccurs="1"/>
         <xs:element name="containerPackagedMedicine" type="COCT_MT230100UV.PackagedMedicine"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="CONT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT230100UV.TerritorialAuthority">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="territory" type="COCT_MT230100UV.Agency" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="governingCountry" type="COCT_MT230100UV.Country" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="TERR"/>
   </xs:complexType>
</xs:schema>