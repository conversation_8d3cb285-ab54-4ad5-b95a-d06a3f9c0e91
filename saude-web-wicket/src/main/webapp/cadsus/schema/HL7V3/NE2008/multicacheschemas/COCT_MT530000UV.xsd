<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type COCT_MT530000UV.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:include schemaLocation="COCT_MT230100UV.xsd"/>
   <xs:include schemaLocation="COCT_MT080000UV.xsd"/>
   <xs:include schemaLocation="COCT_MT050000UV01.xsd"/>
   <xs:include schemaLocation="COCT_MT090000UV01.xsd"/>
   <xs:include schemaLocation="COCT_MT090102UV02.xsd"/>
   <xs:complexType name="COCT_MT530000UV.Act">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="1" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="availabilityTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="priorityCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="uncertaintyCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="languageCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="subject" type="COCT_MT530000UV.Subject2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="recordTarget" type="COCT_MT530000UV.RecordTarget" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="responsibleParty" type="COCT_MT530000UV.ResponsibleParty2"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="performer" type="COCT_MT530000UV.Performer" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="author" type="COCT_MT530000UV.Author" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="dataEnterer" type="COCT_MT530000UV.DataEnterer" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="informant" type="COCT_MT530000UV.Informant" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="verifier" type="COCT_MT530000UV.Verifier" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="location" type="COCT_MT530000UV.Location" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="definition" type="COCT_MT530000UV.Definition" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="conditions" type="COCT_MT530000UV.Conditions" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf1" type="COCT_MT530000UV.SourceOf1" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf2" type="COCT_MT530000UV.SourceOf3" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf" type="COCT_MT530000UV.Subject1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="targetOf" type="COCT_MT530000UV.SourceOf2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassRoot" use="required"/>
      <xs:attribute name="moodCode" type="x_ClinicalStatementActMood" use="required"/>
      <xs:attribute name="negationInd" type="bl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.ActDefinition">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassRoot" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="DEF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.ActReference">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassRoot" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.AdministerableMaterial">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="administerableMaterialKind" type="COCT_MT530000UV.MaterialKind"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="ADMM"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Animal">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="quantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="administrativeGenderCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="birthTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="strainText" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="genderStatusCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asRole" type="COCT_MT530000UV.Role" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="birthplace" type="COCT_MT530000UV.Birthplace" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="ANM"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Author">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="noteText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="modeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="signatureCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="signatureText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="relatedEntity" type="COCT_MT530000UV.RelatedEntity" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="patient" type="COCT_MT050000UV01.Patient" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="assignedEntity" type="COCT_MT090000UV01.AssignedEntity" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="AUT"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="OP"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Birthplace">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="birthplace" type="COCT_MT530000UV.Place" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="BIRTHPL"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Component">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="seperatableInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:choice>
               <xs:element name="observation" type="COCT_MT530000UV.Observation" nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="substanceAdministration" type="COCT_MT530000UV.SubstanceAdministration"
                           nillable="true"
                           minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="supply" type="COCT_MT530000UV.Supply" nillable="true" minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="procedure" type="COCT_MT530000UV.Procedure" nillable="true" minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="encounter" type="COCT_MT530000UV.Encounter" nillable="true" minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="act" type="COCT_MT530000UV.Act" nillable="true" minOccurs="1"
                           maxOccurs="1"/>
               <xs:element name="organizer" type="COCT_MT530000UV.Organizer" nillable="true" minOccurs="1"
                           maxOccurs="1"/>
            </xs:choice>
            <xs:element name="actReference" type="COCT_MT530000UV.ActReference" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipHasComponent" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional" default="true"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Conditions">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="conjunctionCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="seperatableInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="criterion" type="COCT_MT530000UV.Criterion" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipConditional" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="ON"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional" default="false"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Consumable">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:choice>
            <xs:element name="administerableMaterial" type="COCT_MT530000UV.AdministerableMaterial"
                        nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="medication" type="COCT_MT230100UV.Medication" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="CSM"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.ControlActEvent">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="code" type="CD" minOccurs="1" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="TS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="reasonCode" type="CV" minOccurs="0" maxOccurs="1"/>
         <xs:element name="responsibleParty" type="COCT_MT530000UV.ResponsibleParty1"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="author" type="COCT_MT530000UV.Author" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassControlAct" use="optional" fixed="CACT"/>
      <xs:attribute name="moodCode" type="ActMood" use="optional" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Criterion">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="ANY" minOccurs="1" maxOccurs="1"/>
         <xs:element name="interpretationCode" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="precondition" type="COCT_MT530000UV.Precondition2" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN.CRT"/>
      <xs:attribute name="negationInd" type="bl" use="optional" default="false"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.DataEnterer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="time" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="modeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="signatureCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="signatureText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assignedEntity" type="COCT_MT090000UV01.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="ENT"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="OP"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Definition">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="actDefinition" type="COCT_MT530000UV.ActDefinition" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="INST"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="AN"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional" default="false"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Device">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="manufacturedProduct" type="COCT_MT530000UV.ManufacturedProduct"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetDevice" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Encounter">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="SXCM_TS" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="availabilityTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="priorityCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="admissionReferralSourceCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="lengthOfStayQuantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="dischargeDispositionCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="preAdmitTestInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="specialCourtesiesCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="specialArrangementCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="subject" type="COCT_MT530000UV.Subject2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="recordTarget" type="COCT_MT530000UV.RecordTarget" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="responsibleParty" type="COCT_MT530000UV.ResponsibleParty2"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="performer" type="COCT_MT530000UV.Performer" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="author" type="COCT_MT530000UV.Author" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="dataEnterer" type="COCT_MT530000UV.DataEnterer" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="informant" type="COCT_MT530000UV.Informant" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="verifier" type="COCT_MT530000UV.Verifier" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="location" type="COCT_MT530000UV.Location" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="definition" type="COCT_MT530000UV.Definition" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="conditions" type="COCT_MT530000UV.Conditions" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf1" type="COCT_MT530000UV.SourceOf1" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf2" type="COCT_MT530000UV.SourceOf3" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf" type="COCT_MT530000UV.Subject1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="targetOf" type="COCT_MT530000UV.SourceOf2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="ENC"/>
      <xs:attribute name="moodCode" type="x_ClinicalStatementEncounterMood" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Entity">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="quantity" type="PQ" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="riskCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="handlingCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asRole" type="COCT_MT530000UV.Role" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassRoot" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.HealthCareFacility">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="location" type="COCT_MT530000UV.Place" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="serviceProviderOrganization" type="COCT_MT530000UV.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassServiceDeliveryLocation" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Informant">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="functionCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="modeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="relatedEntity" type="COCT_MT530000UV.RelatedEntity" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="patient" type="COCT_MT050000UV01.Patient" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="assignedEntity" type="COCT_MT090000UV01.AssignedEntity" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="INF"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="OP"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.LabeledDrug">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="1"/>
         <xs:element name="expirationTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassManufacturedMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Location">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="healthCareFacility" type="COCT_MT530000UV.HealthCareFacility"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetLocation" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.ManufacturedProduct">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:choice>
            <xs:element name="manufacturedLabeledDrug" type="COCT_MT530000UV.LabeledDrug"
                        nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="manufacturedMaterial" type="COCT_MT530000UV.Material" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
         <xs:element name="manufacturerOrganization" type="COCT_MT530000UV.Organization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassManufacturedProduct" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Material">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="1"/>
         <xs:element name="lotNumberText" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="expirationTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassManufacturedMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.MaterialKind">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="desc" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="materialPart" type="COCT_MT530000UV.MaterialPart" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminerDetermined" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.MaterialKind2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CV" minOccurs="0" maxOccurs="1"/>
         <xs:element name="desc" type="ST" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminerDetermined" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.MaterialPart">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="partMaterialKind" type="COCT_MT530000UV.MaterialKind2" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassPartitive" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Observation">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="1" maxOccurs="1"/>
         <xs:element name="derivationExpr" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="SXCM_TS" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="availabilityTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="priorityCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="repeatNumber" type="IVL_INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="uncertaintyCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="languageCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="ANY" minOccurs="0" maxOccurs="1"/>
         <xs:element name="interpretationCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="methodCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="targetSiteCode" type="CD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="subject" type="COCT_MT530000UV.Subject2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="recordTarget" type="COCT_MT530000UV.RecordTarget" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="responsibleParty" type="COCT_MT530000UV.ResponsibleParty2"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="performer" type="COCT_MT530000UV.Performer" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="author" type="COCT_MT530000UV.Author" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="dataEnterer" type="COCT_MT530000UV.DataEnterer" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="informant" type="COCT_MT530000UV.Informant" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="verifier" type="COCT_MT530000UV.Verifier" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="location" type="COCT_MT530000UV.Location" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="definition" type="COCT_MT530000UV.Definition" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="conditions" type="COCT_MT530000UV.Conditions" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="referenceRange" type="COCT_MT530000UV.ReferenceRange" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf1" type="COCT_MT530000UV.SourceOf1" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf2" type="COCT_MT530000UV.SourceOf3" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf" type="COCT_MT530000UV.Subject1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="targetOf" type="COCT_MT530000UV.SourceOf2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="x_ClinicalStatementObservationMood" use="required"/>
      <xs:attribute name="negationInd" type="bl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.ObservationRange">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="ANY" minOccurs="1" maxOccurs="1"/>
         <xs:element name="interpretationCode" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="precondition" type="COCT_MT530000UV.Precondition1" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN.CRT"/>
      <xs:attribute name="negationInd" type="bl" use="optional" default="false"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Organization">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="name" type="ON" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassOrganization" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Organizer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="availabilityTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="priorityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="subject" type="COCT_MT530000UV.Subject2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="recordTarget" type="COCT_MT530000UV.RecordTarget" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="responsibleParty" type="COCT_MT530000UV.ResponsibleParty2"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="performer" type="COCT_MT530000UV.Performer" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="author" type="COCT_MT530000UV.Author" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="dataEnterer" type="COCT_MT530000UV.DataEnterer" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="informant" type="COCT_MT530000UV.Informant" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="verifier" type="COCT_MT530000UV.Verifier" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="location" type="COCT_MT530000UV.Location" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="definition" type="COCT_MT530000UV.Definition" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="conditions" type="COCT_MT530000UV.Conditions" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="component" type="COCT_MT530000UV.Component" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf1" type="COCT_MT530000UV.SourceOf1" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf2" type="COCT_MT530000UV.SourceOf3" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf" type="COCT_MT530000UV.Subject1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="targetOf" type="COCT_MT530000UV.SourceOf2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassContainer" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Performer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="functionCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="modeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="relatedEntity" type="COCT_MT530000UV.RelatedEntity" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="patient" type="COCT_MT050000UV01.Patient" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="assignedEntity" type="COCT_MT090000UV01.AssignedEntity" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationPhysicalPerformer" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="OP"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Person">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="PN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="administrativeGenderCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="birthTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="multipleBirthInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="multipleBirthOrderNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="maritalStatusCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="religiousAffiliationCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="raceCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="ethnicGroupCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="asRole" type="COCT_MT530000UV.Role" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="birthplace" type="COCT_MT530000UV.Birthplace" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="PSN"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Place">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassPlace" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Precondition1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="conjunctionCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="seperatableInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="criterion" type="COCT_MT530000UV.Criterion" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="PRCN"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="OP"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional" default="true"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Precondition2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="conjunctionCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="seperatableInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="criterion" type="COCT_MT530000UV.Criterion" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="PRCN"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="ON"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional" default="false"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Procedure">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="SXCM_TS" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="availabilityTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="priorityCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="interruptibleInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="uncertaintyCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="languageCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="methodCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="approachSiteCode" type="CD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="targetSiteCode" type="CD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="subject" type="COCT_MT530000UV.Subject2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="device" type="COCT_MT530000UV.Device" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="product" type="COCT_MT530000UV.Product2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="recordTarget" type="COCT_MT530000UV.RecordTarget" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="responsibleParty" type="COCT_MT530000UV.ResponsibleParty2"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="performer" type="COCT_MT530000UV.Performer" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="author" type="COCT_MT530000UV.Author" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="dataEnterer" type="COCT_MT530000UV.DataEnterer" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="informant" type="COCT_MT530000UV.Informant" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="verifier" type="COCT_MT530000UV.Verifier" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="location" type="COCT_MT530000UV.Location" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="definition" type="COCT_MT530000UV.Definition" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="conditions" type="COCT_MT530000UV.Conditions" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf1" type="COCT_MT530000UV.SourceOf1" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf2" type="COCT_MT530000UV.SourceOf3" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf" type="COCT_MT530000UV.Subject1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="targetOf" type="COCT_MT530000UV.SourceOf2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassProcedure" use="required"/>
      <xs:attribute name="moodCode" type="x_ClinicalStatementProcedureMood" use="required"/>
      <xs:attribute name="negationInd" type="bl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Product1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="manufacturedProduct" type="COCT_MT530000UV.ManufacturedProduct"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="PRD"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Product2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="specimen" type="COCT_MT080000UV.Specimen" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="PRD"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.RecordTarget">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="patient" type="COCT_MT050000UV01.Patient" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="RCT"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="OP"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.ReferenceRange">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="seperatableInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="observationRange" type="COCT_MT530000UV.ObservationRange" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="REFV"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="ON"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional" default="false"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.RelatedEntity">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:choice>
            <xs:choice>
               <xs:element name="relatedPerson" type="COCT_MT530000UV.Person" nillable="true"
                           minOccurs="0"
                           maxOccurs="1"/>
               <xs:element name="relatedAnimal" type="COCT_MT530000UV.Animal" nillable="true"
                           minOccurs="0"
                           maxOccurs="1"/>
            </xs:choice>
            <xs:element name="relatedEntity" type="COCT_MT530000UV.Entity" nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
         </xs:choice>
         <xs:choice>
            <xs:choice>
               <xs:element name="scopingPerson" type="COCT_MT530000UV.Person" nillable="true"
                           minOccurs="0"
                           maxOccurs="1"/>
               <xs:element name="scopingAnimal" type="COCT_MT530000UV.Animal" nillable="true"
                           minOccurs="0"
                           maxOccurs="1"/>
            </xs:choice>
            <xs:element name="scopingEntity" type="COCT_MT530000UV.Entity" nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassMutualRelationship" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.ResponsibleParty1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="assignedPerson" type="COCT_MT090102UV02.AssignedPerson" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="RESP"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.ResponsibleParty2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="assignedEntity" type="COCT_MT090000UV01.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="RESP"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Role">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="scopingOrganization" type="COCT_MT530000UV.Organization" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassRoot" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.SourceOf1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="pauseQuantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="conjunctionCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="seperatableInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="actReference" type="COCT_MT530000UV.ActReference" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required"/>
      <xs:attribute name="inversionInd" type="bl" use="optional"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional" default="false"/>
      <xs:attribute name="negationInd" type="bl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.SourceOf2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="pauseQuantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="conjunctionCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="seperatableInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="observation" type="COCT_MT530000UV.Observation" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="substanceAdministration" type="COCT_MT530000UV.SubstanceAdministration"
                        nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="supply" type="COCT_MT530000UV.Supply" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="procedure" type="COCT_MT530000UV.Procedure" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="encounter" type="COCT_MT530000UV.Encounter" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="act" type="COCT_MT530000UV.Act" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="organizer" type="COCT_MT530000UV.Organizer" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="AN"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional" default="true"/>
      <xs:attribute name="negationInd" type="bl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.SourceOf3">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="pauseQuantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="conjunctionCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="seperatableInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="observation" type="COCT_MT530000UV.Observation" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="substanceAdministration" type="COCT_MT530000UV.SubstanceAdministration"
                        nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="supply" type="COCT_MT530000UV.Supply" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="procedure" type="COCT_MT530000UV.Procedure" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="encounter" type="COCT_MT530000UV.Encounter" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="act" type="COCT_MT530000UV.Act" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="organizer" type="COCT_MT530000UV.Organizer" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="AN"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional" default="true"/>
      <xs:attribute name="negationInd" type="bl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Subject1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="controlActEvent" type="COCT_MT530000UV.ControlActEvent" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="optional" fixed="SUBJ"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional"/>
      <xs:attribute name="contextConductionInd" type="bl" use="optional" default="false"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Subject2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="awarenessCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="patient" type="COCT_MT050000UV01.Patient" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="relatedEntity" type="COCT_MT530000UV.RelatedEntity" nillable="true"
                        minOccurs="1"
                        maxOccurs="1"/>
            <xs:element name="specimen" type="COCT_MT080000UV.Specimen" nillable="true" minOccurs="1"
                        maxOccurs="1"/>
         </xs:choice>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="OP"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.SubstanceAdministration">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="SXCM_TS" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="availabilityTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="priorityCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="repeatNumber" type="IVL_INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="languageCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="routeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="approachSiteCode" type="CD" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="doseQuantity" type="IVL_PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="rateQuantity" type="IVL_PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="doseCheckQuantity" type="RTO_QTY_QTY" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="maxDoseQuantity" type="RTO_PQ_PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="administrationUnitCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="subject" type="COCT_MT530000UV.Subject2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="consumable" type="COCT_MT530000UV.Consumable" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="recordTarget" type="COCT_MT530000UV.RecordTarget" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="responsibleParty" type="COCT_MT530000UV.ResponsibleParty2"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="performer" type="COCT_MT530000UV.Performer" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="author" type="COCT_MT530000UV.Author" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="dataEnterer" type="COCT_MT530000UV.DataEnterer" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="informant" type="COCT_MT530000UV.Informant" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="verifier" type="COCT_MT530000UV.Verifier" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="location" type="COCT_MT530000UV.Location" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="definition" type="COCT_MT530000UV.Definition" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="conditions" type="COCT_MT530000UV.Conditions" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf1" type="COCT_MT530000UV.SourceOf1" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf2" type="COCT_MT530000UV.SourceOf3" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf" type="COCT_MT530000UV.Subject1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="targetOf" type="COCT_MT530000UV.SourceOf2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="SBADM"/>
      <xs:attribute name="moodCode" type="x_ClinicalStatementSubstanceMood" use="required"/>
      <xs:attribute name="negationInd" type="bl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Supply">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="SXCM_TS" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="availabilityTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="priorityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="repeatNumber" type="IVL_INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="independentInd" type="BL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="languageCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="quantity" type="PQ" minOccurs="0" maxOccurs="1"/>
         <xs:element name="expectedUseTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="subject" type="COCT_MT530000UV.Subject2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="product" type="COCT_MT530000UV.Product1" minOccurs="1"
                     maxOccurs="unbounded"/>
         <xs:element name="recordTarget" type="COCT_MT530000UV.RecordTarget" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="responsibleParty" type="COCT_MT530000UV.ResponsibleParty2"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="performer" type="COCT_MT530000UV.Performer" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="author" type="COCT_MT530000UV.Author" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="dataEnterer" type="COCT_MT530000UV.DataEnterer" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="informant" type="COCT_MT530000UV.Informant" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="verifier" type="COCT_MT530000UV.Verifier" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="location" type="COCT_MT530000UV.Location" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="definition" type="COCT_MT530000UV.Definition" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="conditions" type="COCT_MT530000UV.Conditions" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf1" type="COCT_MT530000UV.SourceOf1" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="sourceOf2" type="COCT_MT530000UV.SourceOf3" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf" type="COCT_MT530000UV.Subject1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="targetOf" type="COCT_MT530000UV.SourceOf2" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassSupply" use="required"/>
      <xs:attribute name="moodCode" type="x_ClinicalStatementSupplyMood" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT530000UV.Verifier">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="noteText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="time" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="modeCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="signatureCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="signatureText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assignedEntity" type="COCT_MT090000UV01.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationVerifier" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional" default="OP"/>
   </xs:complexType>
</xs:schema>