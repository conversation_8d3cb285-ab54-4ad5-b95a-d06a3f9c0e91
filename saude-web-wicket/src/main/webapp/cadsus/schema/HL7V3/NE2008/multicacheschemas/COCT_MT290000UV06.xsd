<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type COCT_MT290000UV06.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:include schemaLocation="COCT_MT050000UV01.xsd"/>
   <xs:include schemaLocation="COCT_MT960000UV05.xsd"/>
   <xs:include schemaLocation="COCT_MT240003UV02.xsd"/>
   <xs:complexType name="COCT_MT290000UV06.AdministrativeDiagnosis">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="CD" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.AdministrativeDiagnosisReference">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.AssignedEntity">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:choice>
            <xs:element name="assignedProviderPerson" type="COCT_MT290000UV06.ProviderPerson"
                        nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
            <xs:element name="assignedNonPersonLivingSubject"
                        type="COCT_MT290000UV06.NonPersonLivingSubject"
                        nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
            <xs:element name="assignedDevice" type="COCT_MT290000UV06.Device2" nillable="true"
                        minOccurs="0"
                        maxOccurs="1"/>
         </xs:choice>
         <xs:element name="indirectAuthority" type="COCT_MT290000UV06.IndirectAuthorithyOver"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassAssignedEntity" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Author">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="assignedEntity" type="COCT_MT290000UV06.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="AUT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Author1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="time" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assignedEntity" type="COCT_MT290000UV06.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="AUT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Author2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="time" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assignedEntity" type="COCT_MT290000UV06.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="AUT"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.BillableClinicalService">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="1" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="priorityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="confidentialityCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="repeatNumber" type="IVL_INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="reasonCode" type="CE" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="subject" type="COCT_MT290000UV06.Subject5" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="reusableDevice" type="COCT_MT290000UV06.ReusableDevice" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="product" type="COCT_MT290000UV06.Product1" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="responsibleParty" type="COCT_MT290000UV06.ResponsibleParty"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="secondaryPerformer" type="COCT_MT290000UV06.SecondaryPerformer"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="performer" type="COCT_MT290000UV06.Performer" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="author" type="COCT_MT290000UV06.Author" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="consultant" type="COCT_MT290000UV06.Consultant" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="location" type="COCT_MT290000UV06.Location" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="inFulfillmentOf" type="COCT_MT290000UV06.InFulfillmentOf" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="reason1" type="COCT_MT290000UV06.Reason1" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="reason2" type="COCT_MT290000UV06.Reason4" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="reason3" type="COCT_MT290000UV06.Reason3" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="component" type="COCT_MT290000UV06.Component2" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="subjectOf1" type="COCT_MT290000UV06.Subject2" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="subjectOf2" type="COCT_MT290000UV06.Subject" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="componentOf" type="COCT_MT290000UV06.Component1" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassRoot" use="required"/>
      <xs:attribute name="moodCode" type="ActMoodCompletionTrack" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.BillableModifier">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Component1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="patientEncounter" type="COCT_MT290000UV06.PatientEncounter"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipHasComponent" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Component2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="specimenCollectionEvent" type="COCT_MT290000UV06.SpecimenCollectionEvent"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipHasComponent" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Consultant">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="assignedEntity" type="COCT_MT290000UV06.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="CON"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Device">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassAssignedEntity" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Device2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="manufacturerModelName" type="SC" minOccurs="0" maxOccurs="1"/>
         <xs:element name="softwareName" type="SC" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassDevice" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.HealthCareProvider">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="healthCareProviderPerson" type="COCT_MT290000UV06.ProviderPerson"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClass" use="required" fixed="PROV"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.InFulfillmentOf">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="serviceRequest" type="COCT_MT290000UV06.ServiceRequest" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipFulfills" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.IndirectAuthorithyOver">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="healthCareProvider" type="COCT_MT290000UV06.HealthCareProvider"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="RoleLinkType" use="required" fixed="INDAUTH"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Injury">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="CE" minOccurs="1" maxOccurs="1"/>
         <xs:element name="targetSiteCode" type="CD" minOccurs="1" maxOccurs="1"/>
         <xs:element name="origin" type="COCT_MT290000UV06.Origin" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.InjuryLocation">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="TN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="locatedInjuryPlace" type="COCT_MT290000UV06.InjuryPlace" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="subjectOf" type="COCT_MT290000UV06.Subject3" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassLocatedEntity" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.InjuryPlace">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="EN" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="desc" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="positionText" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="injuryLocation" type="COCT_MT290000UV06.InjuryLocation" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassPlace" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Location">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="serviceDeliveryLocation" type="COCT_MT240003UV02.ServiceDeliveryLocation"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetLocation" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Location1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="serviceDeliveryLocation" type="COCT_MT240003UV02.ServiceDeliveryLocation"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetLocation" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.ManufacturedMaterial">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassManufacturedMaterial" use="required"/>
      <xs:attribute name="determinerCode" type="x_DeterminerInstanceKind" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.ManufacturedProduct">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="manufacturedMaterial" type="COCT_MT290000UV06.ManufacturedMaterial"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="manufacturerManufacturedProductOrganization"
                     type="COCT_MT290000UV06.ManufacturedProductOrganization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassManufacturedProduct" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.ManufacturedProductOrganization">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="ON" minOccurs="0" maxOccurs="1"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassOrganization" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.NonPersonLivingSubject">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="name" type="TN" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClassNonPersonLivingSubject" use="required"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Origin">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="injuryLocation" type="COCT_MT290000UV06.InjuryLocation" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="ORG"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.PatientCareProvisionRequest">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="author" type="COCT_MT290000UV06.Author2" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="reason" type="COCT_MT290000UV06.Reason" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassCareProvision" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="RQO"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.PatientEncounter">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="activityTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="priorityCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="admissionReferralSourceCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="dischargeDispositionCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="reason" type="COCT_MT290000UV06.Reason5" nillable="true" minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="ENC"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Performer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="functionCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assignedEntity" type="COCT_MT290000UV06.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationPhysicalPerformer" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.PresentingIndication">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="text" type="ED" minOccurs="0" maxOccurs="1"/>
         <xs:element name="effectiveTime" type="IVL_TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="value" type="ANY" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassObservation" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Product1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="manufacturedProduct" type="COCT_MT290000UV06.ManufacturedProduct"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="PRD"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Product2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="specimen" type="COCT_MT290000UV06.Specimen" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="PRD"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.ProviderPerson">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="name" type="PN" minOccurs="1" maxOccurs="1"/>
         <xs:element name="telecom" type="TEL" minOccurs="0" maxOccurs="1"/>
         <xs:element name="administrativeGenderCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="birthTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="addr" type="AD" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="EntityClass" use="required" fixed="PSN"/>
      <xs:attribute name="determinerCode" type="EntityDeterminer" use="required" fixed="INSTANCE"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Reason">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="administrativeDiagnosisReference"
                     type="COCT_MT290000UV06.AdministrativeDiagnosisReference"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipReason" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Reason1">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="1" maxOccurs="1"/>
         <xs:element name="injury" type="COCT_MT290000UV06.Injury" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipReason" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Reason3">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="priorityNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="administrativeDiagnosis" type="COCT_MT290000UV06.AdministrativeDiagnosis"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipReason" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Reason4">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="patientCareProvisionRequest"
                     type="COCT_MT290000UV06.PatientCareProvisionRequest"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipReason" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Reason5">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="priorityNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="presentingIndication" type="COCT_MT290000UV06.PresentingIndication"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipReason" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.ResponsibleParty">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="assignedEntity" type="COCT_MT290000UV06.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="RESP"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.ReusableDevice">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="device" type="COCT_MT290000UV06.Device" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="RDV"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.SecondaryPerformer">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="functionCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="assignedEntity" type="COCT_MT290000UV06.AssignedEntity" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationType" use="required" fixed="SPRF"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.ServiceRequest">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="code" type="CD" minOccurs="0" maxOccurs="1"/>
         <xs:element name="author" type="COCT_MT290000UV06.Author1" nillable="true" minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassRoot" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="RQO"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Specimen">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="RoleClassSpecimen" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.SpecimenCollectionEvent">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="product" type="COCT_MT290000UV06.Product2" minOccurs="1" maxOccurs="1"/>
         <xs:element name="location" type="COCT_MT290000UV06.Location1" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClassProcedure" use="required"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Subject">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="substitution" type="COCT_MT290000UV06.Substitution" nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="SUBJ"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Subject2">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="billableModifier" type="COCT_MT290000UV06.BillableModifier"
                     nillable="true"
                     minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ActRelationshipType" use="required" fixed="SUBJ"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Subject3">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="position" type="COCT_MT960000UV05.Position" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Subject5">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="patient" type="COCT_MT050000UV01.Patient" nillable="true" minOccurs="1"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="typeCode" type="ParticipationTargetSubject" use="required"/>
      <xs:attribute name="contextControlCode" type="ContextControl" use="optional"/>
   </xs:complexType>
   <xs:complexType name="COCT_MT290000UV06.Substitution">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
      <xs:attribute name="classCode" type="ActClass" use="required" fixed="SUBST"/>
      <xs:attribute name="moodCode" type="ActMood" use="required" fixed="EVN"/>
   </xs:complexType>
</xs:schema>