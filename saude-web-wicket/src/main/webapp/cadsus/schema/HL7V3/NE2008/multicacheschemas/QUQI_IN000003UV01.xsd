<?xml version="1.0" encoding="utf-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:mif="urn:hl7-org:v3/mif"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified">
   <xs:annotation>
      <xs:documentation>Source Information
     Rendered by: RoseTree 4.2.7
     Rendered on: 
This document was rendered into XML using software provided to HL7 by Beeler Consulting LLC.
 PubDB to MIF Transform: $RCSfile: PubDbXmlToMIF.xsl,v $ $Revision: 1.11 $ $Date: 2007/10/19 05:55:13 $
  Fix names transform: $Id: FixMifNames.xsl,v 1.8 2007/03/20 02:48:49 wbeeler Exp $
  HTML to MIF Markup transform: $Id: HtmlToMIFMarkup.xsl,v 1.4 2007/03/20 02:48:49 wbeeler Exp $
  Base transform: $Id: ConvertBase.xsl,v 1.5 2007/10/19 05:55:13 wbeeler Exp $
  Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
 Generated using schema builder version: 3.1.6 and DynamicMifToXSD.xsl version: 1.4
 Dynamic MIF to Schema Transform: $Id: DynamicMifToXsd.xsl,v 1.12 2007/10/19 05:55:13 wbeeler Exp $
  Static MIF to Schema Transform: $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
  Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:include schemaLocation="MCCI_MT000300UV01.xsd"/>
   <!-- Changes to properly define the message, and to enable proper WSDL operations definition
   vassil 2007-11-14 -->
   <xs:include schemaLocation="QUQI_MT000001UV01.xsd"/>
   <xs:element name="QUQI_IN000003UV01" type="QUQI_IN000003UV01_Type"/>
   <xs:element name="QUQI_IN000003UV01_Cancel" type="QUQI_IN000003UV01_Type"/>
   <xs:complexType name="QUQI_IN000003UV01_Type">
      <xs:complexContent>
         <xs:extension base="QUQI_IN000003UV01.MCCI_MT000300UV01.Message">
            <xs:attribute name="ITSVersion" type="xs:string" use="required" fixed="XML_1.0"/>
         </xs:extension>
      </xs:complexContent>
   </xs:complexType>
   <xs:complexType name="QUQI_IN000003UV01.MCCI_MT000300UV01.Message">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="creationTime" type="TS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="securityText" type="ST" minOccurs="0" maxOccurs="1"/>
         <xs:element name="versionCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="interactionId" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="profileId" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="processingCode" type="CS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="processingModeCode" type="CS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="acceptAckCode" type="CS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="attachmentText" type="ED" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="receiver" type="MCCI_MT000300UV01.Receiver" minOccurs="1"
            maxOccurs="unbounded"/>
         <xs:element name="respondTo" type="MCCI_MT000300UV01.RespondTo" nillable="true"
            minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="sender" type="MCCI_MT000300UV01.Sender" minOccurs="1" maxOccurs="1"/>
         <xs:element name="attentionLine" type="MCCI_MT000300UV01.AttentionLine" nillable="true"
            minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="acknowledgement" type="MCCI_MT000300UV01.Acknowledgement" nillable="true"
            minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="controlActProcess"
            type="QUQI_MT000001UV01.ControlActProcess" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
</xs:schema>