<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="urn:hl7-org:v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:ex="urn:hl7-org/v3-example"
           targetNamespace="urn:hl7-org:v3"
           elementFormDefault="qualified"><!--
*****************************************************************************************************************
* XML schema for message type PRPA_MT201306UV02.
* Source information:
*     Rendered by: Visio to MIF transform
*     Rendered on: 
* 
*
* Generated by XMLITS version 3.1.6
*   MIF to XSD Transform $Id: StaticMifToXsd.xsl,v 1.30 2007/12/06 05:50:08 wbeeler Exp $
*     Package Id Conversion: $Id: TransformPackageIds.xsl,v 1.6 2007/03/20 02:48:49 wbeeler Exp $
*
* Copyright (c) 2002, 2003, 2004, 2005, 2006, 2007 Health Level Seven. All rights reserved.
*  Redistribution and use in source and binary forms, with or without
*  modification, are permitted provided that the following conditions
*  are met:
*  1. Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in the
*     documentation and/or other materials provided with the distribution.
*  3. All advertising materials mentioning features or use of this software
*     must display the following acknowledgement:
*       This product includes software developed by Health Level Seven.
*  THIS SOFTWARE IS PROVIDED BY HEALTH LEVEL SEVEN, INC. AND CONTRIBUTORS "AS IS" AND
*  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
*  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
*  ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
*  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
*  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
*  OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
*  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
*  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
*  OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
*  SUCH DAMAGE.
*
********************************************************************************************************************
	  --><xs:annotation>
      <xs:documentation>Generated using schema builder version 3.1.6. Stylesheets:

StaticMifToXsd.xsl version 2.0</xs:documentation>
   </xs:annotation>
   <xs:include schemaLocation="../coreschemas/infrastructureRoot.xsd"/>
   <xs:complexType name="PRPA_MT201306UV02.LivingSubjectAdministrativeGender">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="CE" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.LivingSubjectBirthPlaceAddress">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="AD" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.LivingSubjectBirthPlaceName">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="EN" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.LivingSubjectBirthTime">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="IVL_TS" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.LivingSubjectDeceasedTime">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="IVL_TS" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.LivingSubjectId">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="II" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.LivingSubjectName">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="EN" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.MatchAlgorithm">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="ANY" minOccurs="1" maxOccurs="1"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.MatchCriterionList">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="matchAlgorithm" type="PRPA_MT201306UV02.MatchAlgorithm" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="matchWeight" type="PRPA_MT201306UV02.MatchWeight" nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="minimumDegreeMatch" type="PRPA_MT201306UV02.MinimumDegreeMatch"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.MatchWeight">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="ANY" minOccurs="1" maxOccurs="1"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.MinimumDegreeMatch">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="ANY" minOccurs="1" maxOccurs="1"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.MothersMaidenName">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="PN" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.OtherIDsScopingOrganization">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="II" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.ParameterList">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="id" type="II" minOccurs="0" maxOccurs="1"/>
         <xs:element name="livingSubjectAdministrativeGender"
                     type="PRPA_MT201306UV02.LivingSubjectAdministrativeGender"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="livingSubjectBirthPlaceAddress"
                     type="PRPA_MT201306UV02.LivingSubjectBirthPlaceAddress"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="livingSubjectBirthPlaceName"
                     type="PRPA_MT201306UV02.LivingSubjectBirthPlaceName"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="livingSubjectBirthTime" type="PRPA_MT201306UV02.LivingSubjectBirthTime"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="livingSubjectDeceasedTime"
                     type="PRPA_MT201306UV02.LivingSubjectDeceasedTime"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="livingSubjectId" type="PRPA_MT201306UV02.LivingSubjectId" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="livingSubjectName" type="PRPA_MT201306UV02.LivingSubjectName"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="mothersMaidenName" type="PRPA_MT201306UV02.MothersMaidenName"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="otherIDsScopingOrganization"
                     type="PRPA_MT201306UV02.OtherIDsScopingOrganization"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="patientAddress" type="PRPA_MT201306UV02.PatientAddress" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="patientStatusCode" type="PRPA_MT201306UV02.PatientStatusCode"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="patientTelecom" type="PRPA_MT201306UV02.PatientTelecom" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="principalCareProviderId" type="PRPA_MT201306UV02.PrincipalCareProviderId"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
         <xs:element name="principalCareProvisionId"
                     type="PRPA_MT201306UV02.PrincipalCareProvisionId"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.PatientAddress">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="AD" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.PatientStatusCode">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="CV" minOccurs="1" maxOccurs="1"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.PatientTelecom">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="TEL" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.PrincipalCareProviderId">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="II" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.PrincipalCareProvisionId">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="value" type="II" minOccurs="1" maxOccurs="unbounded"/>
         <xs:element name="semanticsText" type="ST" minOccurs="1" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.QueryByParameter">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="queryId" type="II" minOccurs="1" maxOccurs="1"/>
         <xs:element name="statusCode" type="CS" minOccurs="1" maxOccurs="1"/>
         <xs:element name="modifyCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="responseElementGroupId" type="II" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="responseModalityCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="responsePriorityCode" type="CS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="initialQuantity" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="initialQuantityCode" type="CE" minOccurs="0" maxOccurs="1"/>
         <xs:element name="executionAndDeliveryTime" type="TS" minOccurs="0" maxOccurs="1"/>
         <xs:element name="matchCriterionList" type="PRPA_MT201306UV02.MatchCriterionList"
                     nillable="true"
                     minOccurs="0"
                     maxOccurs="1"/>
         <xs:element name="parameterList" type="PRPA_MT201306UV02.ParameterList" minOccurs="1"
                     maxOccurs="1"/>
         <xs:element name="sortControl" type="PRPA_MT201306UV02.SortControl" nillable="true"
                     minOccurs="0"
                     maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
   <xs:complexType name="PRPA_MT201306UV02.SortControl">
      <xs:sequence>
         <xs:group ref="InfrastructureRootElements"/>
         <xs:element name="sequenceNumber" type="INT" minOccurs="0" maxOccurs="1"/>
         <xs:element name="elementName" type="SC" minOccurs="0" maxOccurs="1"/>
         <xs:element name="directionCode" type="CS" minOccurs="0" maxOccurs="1"/>
      </xs:sequence>
      <xs:attributeGroup ref="InfrastructureRootAttributes"/>
      <xs:attribute name="nullFlavor" type="NullFlavor" use="optional"/>
   </xs:complexType>
</xs:schema>