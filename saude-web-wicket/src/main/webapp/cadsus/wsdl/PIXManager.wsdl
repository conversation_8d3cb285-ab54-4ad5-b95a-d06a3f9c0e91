<?xml version="1.0" encoding="UTF-8"?>
<definitions name="PIXManager" targetNamespace="urn:ihe:iti:pixv3:2007"
    xmlns:tns="urn:ihe:iti:pixv3:2007" 
    xmlns:wsoap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns="http://schemas.xmlsoap.org/wsdl/"
    xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:hl7="urn:hl7-org:v3">
    <documentation>Example WSDL for PIX Manager</documentation>
    <types>
        <xsd:schema elementFormDefault="qualified" targetNamespace="urn:hl7-org:v3"
            xmlns:hl7="urn:hl7-org:v3">
            <!-- Include the message schema -->
            <xsd:include
                schemaLocation="../schema/HL7V3/NE2008/multicacheschemas/PRPA_IN201301UV02.xsd"/>
        </xsd:schema>
        <xsd:schema elementFormDefault="qualified" targetNamespace="urn:hl7-org:v3"
            xmlns:hl7="urn:hl7-org:v3">
            <!-- Include the message schema -->
            <xsd:include
                schemaLocation="../schema/HL7V3/NE2008/multicacheschemas/PRPA_IN201302UV02.xsd"/>
        </xsd:schema>
        <xsd:schema elementFormDefault="qualified" targetNamespace="urn:hl7-org:v3"
            xmlns:hl7="urn:hl7-org:v3">
            <!-- Include the message schema -->
            <xsd:include
                schemaLocation="../schema/HL7V3/NE2008/multicacheschemas/PRPA_IN201304UV02.xsd"/>
        </xsd:schema>
        <xsd:schema elementFormDefault="qualified" targetNamespace="urn:hl7-org:v3"
            xmlns:hl7="urn:hl7-org:v3">
            <!-- Include the message schema -->
            <xsd:include
                schemaLocation="../schema/HL7V3/NE2008/multicacheschemas/MCCI_IN000002UV01.xsd"/>
        </xsd:schema>
        <xsd:schema elementFormDefault="qualified" targetNamespace="urn:hl7-org:v3"
            xmlns:hl7="urn:hl7-org:v3">
            <!-- Include the message schema -->
            <xsd:include
                schemaLocation="../schema/HL7V3/NE2008/multicacheschemas/PRPA_IN201309UV02.xsd"/>
        </xsd:schema>
        <xsd:schema elementFormDefault="qualified" targetNamespace="urn:hl7-org:v3"
            xmlns:hl7="urn:hl7-org:v3">
            <!-- Include the message schema -->
            <xsd:include
                schemaLocation="../schema/HL7V3/NE2008/multicacheschemas/PRPA_IN201310UV02.xsd"/>
        </xsd:schema>
    </types>
    <message name="PRPA_IN201301UV02_Message">
        <part element="hl7:PRPA_IN201301UV02" name="Body"/>
    </message>
    <message name="PRPA_IN201302UV02_Message">
        <part element="hl7:PRPA_IN201302UV02" name="Body"/>
    </message>
    <message name="PRPA_IN201304UV02_Message">
        <part element="hl7:PRPA_IN201304UV02" name="Body"/>
    </message>
    <message name="MCCI_IN000002UV01_Message">
        <part element="hl7:MCCI_IN000002UV01" name="Body"/>
    </message>
    <message name="PRPA_IN201309UV02_Message">
        <part element="hl7:PRPA_IN201309UV02" name="Body"/>
    </message>
    <message name="PRPA_IN201310UV02_Message">
        <part element="hl7:PRPA_IN201310UV02" name="Body"/>
    </message>
    <portType name="PIXManager_PortType">
        <operation name="PIXManager_PRPA_IN201301UV02">
            <input message="tns:PRPA_IN201301UV02_Message"
                wsaw:Action="urn:hl7-org:v3:PRPA_IN201301UV02"/>
            <output message="tns:MCCI_IN000002UV01_Message"
                wsaw:Action="urn:hl7-org:v3:MCCI_IN000002UV01"/>
        </operation>
        <operation name="PIXManager_PRPA_IN201302UV02">
            <input message="tns:PRPA_IN201302UV02_Message"
                wsaw:Action="urn:hl7-org:v3:PRPA_IN201302UV02"/>
            <output message="tns:MCCI_IN000002UV01_Message"
                wsaw:Action="urn:hl7-org:v3:MCCI_IN000002UV01"/>
        </operation>
        <operation name="PIXManager_PRPA_IN201304UV02">
            <input message="tns:PRPA_IN201304UV02_Message"
                wsaw:Action="urn:hl7-org:v3:PRPA_IN201304UV02"/>
            <output message="tns:MCCI_IN000002UV01_Message"
                wsaw:Action="urn:hl7-org:v3:MCCI_IN000002UV01"/>
        </operation>
        <operation name="PIXManager_PRPA_IN201309UV02">
            <input message="tns:PRPA_IN201309UV02_Message"
                wsaw:Action="urn:hl7-org:v3:PRPA_IN201309UV02"/>
            <output message="tns:PRPA_IN201310UV02_Message"
                wsaw:Action="urn:hl7-org:v3:PRPA_IN201310UV02"/>
        </operation>
    </portType>
    <binding name="PIXManager_Binding_Soap12" type="tns:PIXManager_PortType">
        <wsoap12:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="PIXManager_PRPA_IN201301UV02">
            <wsoap12:operation soapAction="urn:hl7-org:v3:PRPA_IN201301UV02"/>
            <input>
                <wsoap12:body use="literal"/>
            </input>
            <output>
                <wsoap12:body use="literal"/>
            </output>
        </operation>
        <operation name="PIXManager_PRPA_IN201302UV02">
            <wsoap12:operation soapAction="urn:hl7-org:v3:PRPA_IN201302UV02"/>
            <input>
                <wsoap12:body use="literal"/>
            </input>
            <output>
                <wsoap12:body use="literal"/>
            </output>
        </operation>
        <operation name="PIXManager_PRPA_IN201304UV02">
            <wsoap12:operation soapAction="urn:hl7-org:v3:PRPA_IN201304UV02"/>
            <input>
                <wsoap12:body use="literal"/>
            </input>
            <output>
                <wsoap12:body use="literal"/>
            </output>
        </operation>
        <operation name="PIXManager_PRPA_IN201309UV02">
            <wsoap12:operation soapAction="urn:hl7-org:v3:PRPA_IN201309UV02"/>
            <input>
                <wsoap12:body use="literal"/>
            </input>
            <output>
                <wsoap12:body use="literal"/>
            </output>
        </operation>
    </binding>
    <service name="PIXManager_Service">
        <port binding="tns:PIXManager_Binding_Soap12" name="PIXManager_Port_Soap12">
            <wsoap12:address location="https://servicoshm.saude.gov.br/cadsus/PIXManager"/>
        </port>
    </service>
</definitions>
