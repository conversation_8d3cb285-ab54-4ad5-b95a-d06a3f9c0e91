var elements = Array(
	"article", 
	"aside", 
	"audio", 
	"canvas", 
	"command", 
	"datalist", 
	"details", 
	"embed", 
	"figcaption", 
	"figure", 
	"footer", 
	"header", 
	"hgroup", 
	"keygen", 
	"mark", 
	"meter", 
	"nav", 
	"output", 
	"progress", 
	"rp", 
	"rt", 
	"ruby", 
	"section", 
	"source", 
	"summary", 
	"time", 
	"video",
	"wicket:panel", 
	"wicket:child", 
	"wicket:extend", 
	"wicket:message", 
	"wicket:container", 
	"wicket:child", 
	"wicket:child");

for(var c = 0; c < elements.length; c++){
	document.createElement(elements[c]);
}
