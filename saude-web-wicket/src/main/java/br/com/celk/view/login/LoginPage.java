package br.com.celk.view.login;

import br.com.celk.cluster.util.ServerId;
import br.com.celk.component.appletbiometria.IAppletAction;
import br.com.celk.component.biometria.Biometria;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.passwordfield.PasswordField;
import br.com.celk.io.LogoHelper;
import br.com.celk.resources.Resources;
import br.com.celk.system.Application;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.LoginHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.bemvindo.BemVindoPage;
import br.com.celk.view.controle.usuario.EdicaoCpfTelefone;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.home.HomePage;
import br.com.celk.view.publico.autenticacaoDoisFatores.AutenticacaoDoisFatoresPage;
import br.com.celk.view.senhaexpirada.SenhaExpiradaPage;
import br.com.ksisolucoes.TipoEstabelecimento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.dto.EmpresasUsuarioDTO;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.SessaoWeb;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.ProgramaPagina;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import org.apache.wicket.ajax.AbstractDefaultAjaxBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.WebPage;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Button;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.StatelessForm;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.image.NonCachingImage;
import org.apache.wicket.markup.html.link.ResourceLink;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.protocol.http.WebSession;
import org.apache.wicket.request.Request;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.request.resource.ResourceReference;
import org.apache.wicket.request.resource.ResourceStreamResource;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.string.StringValue;

import javax.resource.ResourceException;
import javax.ws.rs.core.Response;
import java.io.File;
import java.io.IOException;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.jar.Manifest;

/**
 * <AUTHOR>
 */
public class LoginPage extends WebPage implements IAppletAction {

    private AbstractDefaultAjaxBehavior behavior;
    private AbstractAjaxLink btnAcessarComCertificado;
    private WebMarkupContainer containerCertificado;

    private InputField txtLogin;
    private PasswordField txtSenha;
    private Button btnAcessar;
    private Model<String> modelTituloAba;
    private WebMarkupContainer containerExibirLogo;

    private String login;
    private String senha;
    private List<Empresa> empresas = new ArrayList<Empresa>();

    private Long usuarioBiometria;
    private Usuario usuario;
    private Empresa empresa;
    private LoginCommons loginCommons;
    private Biometria biometria;
    private DlgRecuperarSenha dlgRecuperarSenha;

    public LoginPage(PageParameters parameters) {
        super(parameters);
    }

    @Override
    protected void onInitialize() {
        super.onInitialize();
        loginCommons = new LoginCommons();

        Form form = null;

        setStatelessHint(true);
        form = new StatelessForm("form");

        biometria = new Biometria("biometria");
        form.add(biometria);
        biometria.setListener(this);

        AbstractAjaxButton btnBiometria = new AbstractAjaxButton("btnBiometria") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (login == null) {
                    erroAutenticacao(BundleManager.getString("informeUsuario"));
                    return;
                }

                Usuario usuario = LoadManager.getInstance(Usuario.class)
                        .addProperty(Usuario.PROP_CODIGO)
                        .addProperty(VOUtils.montarPath(Usuario.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(Usuario.PROP_PROFISSIONAL, Profissional.PROP_CHAVE_BIOMETRIA))
                        .addParameter(new QueryCustom.QueryCustomParameter(Usuario.PROP_TIPO_USUARIO, BuilderQueryCustom.QueryParameter.IN, Usuario.TipoUsuario.valueList(Arrays.asList(Usuario.TipoUsuario.USUARIO_SAUDE, Usuario.TipoUsuario.USUARIO_INTEGRACAO))))
                        .addInterceptor(new LoadInterceptor() {
                            @Override
                            public void customHQL(HQLHelper hql, String alias) {
                                hql.addToWhereWhithAnd("upper(" + alias + ".login) =", br.com.ksisolucoes.util.Coalesce.asString(login).toUpperCase());
                            }
                        })
                        .start().getVO();
                if (usuario != null && usuario.getProfissional() != null && usuario.getProfissional().getChaveBiometria() != null) {
                    usuarioBiometria = usuario.getCodigo();
                    target.appendJavaScript("validarDigital('" + usuario.getProfissional().getChaveBiometria() + "');");

                } else {
                    usuarioBiometria = null;
                    erroAutenticacao(BundleManager.getString("usuarioNaoRegistradoParaBiometria"));
                }
            }
        };
        String parametroBiometria = null;
        try {
            parametroBiometria = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("usaBiometria");
            btnBiometria.setVisible(RepositoryComponentDefault.SIM.equals(parametroBiometria));
            form.add(btnBiometria);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        String title = null;
        try {
            title = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("tituloPaginaLogin");
        } catch (DAOException e) {
            Loggable.log.error(e);
        }

        add(new Label("tituloAba", modelTituloAba = Model.of(title)));

        form.setModel(new CompoundPropertyModel(this));
        form.setOutputMarkupId(true);

        form.add(new Image("gemIco", Resources.Images.CELK_SAUDE.resourceReference()));
        form.add(new Image("imgChrome", Resources.Images.CHROME.resourceReference()));
        form.add(new Image("imgFirefox", Resources.Images.FIREFOX.resourceReference()));
        form.add(new Image("imgMonitor", Resources.Images.MONITOR.resourceReference()));
        form.add(new Image("imgGlobe", Resources.Images.GLOBE.resourceReference()));

        containerExibirLogo = new WebMarkupContainer("containerExibirLogo");
        containerExibirLogo.add(new Image("imgCelk", Resources.Images.CELK.resourceReference()));
        containerExibirLogo.setVisible(loginCommons.isExibirLogoCelk());
        form.add(new Image("imgSuporte", Resources.Images.PERSONAL.resourceReference()));
        form.add(containerExibirLogo);

        form.add(loginCommons.getFamiliasContainer());

        String telefoneSuporte = loginCommons.getGeral().getParametro("Numero_suporte");
        form.add(new Label("numeroSuporte", BundleManager.getString("ligue0800", telefoneSuporte)));

        form.add(txtLogin = new InputField("login"));
        form.add(txtSenha = new PasswordField("senha"));

        form.add(btnAcessar = new Button("btnAcessar") {
            @Override
            public void onSubmit() {
                try {
                    autenticarUsuario();
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                    erroAutenticacao("Login inválido, tente novamente");
                } catch (ValidacaoException | ResourceException ex) {
                    erroAutenticacao(ex.getMessage());
                }
            }
        });

        form.add(dlgRecuperarSenha = new DlgRecuperarSenha("modalEsqueciMinhaSenha"));
        form.add(new AjaxLink<String>("linkEsqueciMinhaSenha") {
            @Override
            public void onClick(AjaxRequestTarget target) {
                dlgRecuperarSenha.show(target);
            }
        });

        String msgErro = "";
        StringValue stringValue = getPageParameters().get("erro");
        if (stringValue != null) {
            msgErro = stringValue.toString();
        }

        WebMarkupContainer panelErro = new WebMarkupContainer("panelErro");
        form.add(panelErro);

        MultiLineLabel lblErro = new MultiLineLabel("msgErro", msgErro);
        lblErro.setEscapeModelStrings(false);

        panelErro.add(lblErro);

        if (Coalesce.asString(msgErro).isEmpty()) {
            panelErro.setVisible(false);
        }

        carregarLogos(form);

        form.add(new Label("gemVersion", new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                try {
                    String serverName = Coalesce.asString(ServerId.getName());
                    if (!serverName.isEmpty()) {
                        serverName = " | ".concat(serverName);
                    }

                    Manifest manifest = new Manifest(Application.get().getServletContext().getResourceAsStream("/META-INF/MANIFEST.MF"));
                    return BundleManager.getString("versaoLoginX", manifest.getMainAttributes().getValue("Gem-Version") + serverName);
                } catch (IOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
                return "";
            }
        }) {
            @Override
            protected boolean getStatelessHint() {
                return true;
            }
        });


        form.add(containerCertificado = new WebMarkupContainer("containerCertificado"));
        containerCertificado.setOutputMarkupId(true);
        containerCertificado.setVisible(false);

        try {
            Long habilitarLoginComCertificadoDigital = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("HabilitarLoginComCertificadoDigital");
            if (RepositoryComponentDefault.SIM_LONG.equals(habilitarLoginComCertificadoDigital)) {

                Long obrigarCertificado = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ObrigarCertificadoParaIpNaoLiberado");
                if (RepositoryComponentDefault.SIM_LONG.equals(obrigarCertificado)) {
                    boolean ipLiberado = false;
                    String ip = WicketMethods.getIpClient();
                    if (ip != null) {
                        ipLiberado = BOFactoryWicket.getBO(BasicoFacade.class).consultaAcessoIp(ip);
                    }
                    if (!ipLiberado) {
                        txtLogin.setVisible(false);
                        txtSenha.setVisible(false);
                        btnAcessar.setVisible(false);
                    }
                }

                containerCertificado.setVisible(true);
            }
        } catch (SGKException e) {
            Loggable.log.error(e);
        }

        containerCertificado.add(btnAcessarComCertificado = new AbstractAjaxLink("btnAcessarComCertificado") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onActionLoginComCertificado(target);
            }
        });

        btnAcessarComCertificado.add(new Image("imgAcessarComCertificado", Resources.Images.ACESSAR_COM_CERTIFICADO.resourceReference()));

        add(behavior = new AbstractDefaultAjaxBehavior() {

            @Override
            protected void respond(AjaxRequestTarget target) {
                Request request = RequestCycle.get().getRequest();
                onRespondBehavior(request);
            }

        });

        add(form);
    }

    private void onActionLoginComCertificado(AjaxRequestTarget target) {
        target.appendJavaScript("AuthenticateCertified.login('" + behavior.getCallbackUrl() + "');");
    }

    private void onRespondBehavior(Request request) {
        String statusParam = request.getPostParameters().getParameterValue("status").toString();

        Response.Status status = null;
        if (statusParam != null) {
            status = Response.Status.fromStatusCode(Integer.parseInt(statusParam));
        }

        if (Response.Status.OK.equals(status)) {
            setResponsePage(AuthPage.class);
        } else {
            String msgNaoFoiPossivelAcessarSistemaPorMeioCertificadoDigital = BundleManager.getString("msgNaoFoiPossivelAcessarSistemaPorMeioCertificadoDigital");
            String msgPadraoAcessoCertificadoDigital = BundleManager.getString("msgPadraoAcessoCertificadoDigital");
            String msgNaoPossivelValidarCertificadoApiSerasaIdentific = BundleManager.getString("msgNaoPossivelValidarCertificadoApiSerasaIdentific");

            Loggable.log.warn(msgNaoFoiPossivelAcessarSistemaPorMeioCertificadoDigital);
            Loggable.log.error(msgNaoPossivelValidarCertificadoApiSerasaIdentific + (status != null ? ", a requisição retornou " + status.getReasonPhrase() : ""));

            erroAutenticacao(msgNaoFoiPossivelAcessarSistemaPorMeioCertificadoDigital + "\n\n" + msgPadraoAcessoCertificadoDigital);
        }
    }

    private void autenticarUsuario() throws ValidacaoException, DAOException, ResourceException {
        if (login == null || senha == null) {
            throw new ValidacaoException(BundleManager.getString("informeUsuarioSenha"));
        }

        Usuario usuarioLogado = LoginHelper.autenticar(login, senha);

        Long parametroAutenticacaoDoisFatores = null;
        parametroAutenticacaoDoisFatores = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("AutenticacaoDoisFatores");
        if (RepositoryComponentDefault.SIM_LONG.equals(parametroAutenticacaoDoisFatores)) {
            if ((usuarioLogado.getTelefone() == null || !verificaCelular(usuarioLogado.getTelefone())) && usuarioLogado.getEmail() == null) {
                throw new ValidacaoException(BundleManager.getString("contatoNaoEncontradoCadastro"));
            }
            setResponsePage(new AutenticacaoDoisFatoresPage(usuarioLogado, loginCommons));
        } else {
            efetivarLogin(usuarioLogado);
        }
    }

    private void efetivarLogin(Usuario usuario) throws ValidacaoException, DAOException, ResourceException {
        this.usuario = usuario;
        List<Long> tipoEstabelecimentoList = TipoEstabelecimento.codigos();
        tipoEstabelecimentoList.remove(TipoEstabelecimento.VIGILANCIA_EXTERNO.value()); //não aparecer no login estabelecimentos da vigilância.

        empresas = BOFactoryWicket.getBO(UsuarioFacade.class).getListEmpresasUsuario(new EmpresasUsuarioDTO(usuario, tipoEstabelecimentoList));

        Long obrigarEnderecoMac = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ObrigarEnderecoMac");
        if (RepositoryComponentDefault.SIM_LONG.equals(obrigarEnderecoMac)) {
            String mac = null;
            try {
                mac = WicketMethods.getMacAddress();
            } catch (SocketException | UnknownHostException e) {
                Loggable.log.error(e);
            }
            boolean macLiberado = BOFactoryWicket.getBO(BasicoFacade.class).consultaAcessoMac(mac);
            if (!macLiberado) {
                throw new ValidacaoException(BundleManager.getString("macXNaoLiberadoParaAcessoAoSistema", mac));
            }
        }

        if (getSession().isTemporary()) {
            getSession().bind();
        }
        String tenant = TenantContext.getContext();
        long timeSession = 0;
        if (tenant != null) {
            Date data = DataUtil.getDataAtual();
            timeSession = data.getTime();
            SessaoWeb sessaoWeb = new SessaoWeb();
            sessaoWeb.setDataEntrada(data);
            sessaoWeb.setIdSessao(ApplicationSession.get().getId() + timeSession);
            sessaoWeb.setHost(WicketMethods.getIpClient());
            sessaoWeb.setUsuario(usuario);
            try {
                BOFactoryWicket.save(sessaoWeb);
            } catch (SGKException ex) {
                Loggable.log.error(BundleManager.getString("naoFoiPossivelRegistrarSessao"), ex);
            }
        }
        ApplicationSession.get().setTenant(TenantContext.getRealContext());
        ApplicationSession.get().setTimeSession(timeSession);

        try {
            Manifest manifest = new Manifest(Application.get().getServletContext().getResourceAsStream("/META-INF/MANIFEST.MF"));
            String version = manifest.getMainAttributes().getValue("Gem-Version");
            ApplicationSession.get().setVersao(version);
        } catch (IOException e) {
            Loggable.log.error(e);
        }

        termosAceite(usuario);
    }

    private void termosAceite(Usuario usuario) throws ValidacaoException, DAOException {
        String mesagemAvisoLogin = loginCommons.getGeral().getParametro("MensagemAvisoLogin");

        if (mesagemAvisoLogin != null && !mesagemAvisoLogin.isEmpty()) {
            redirectAvisoLogin(usuario);
        } else {
            LoginHelper.configurarAceiteTermosDeUso(usuario);

            if (RepositoryComponentDefault.NAO_LONG.equals(usuario.getAceiteTermoUso())) {
                redirectAceiteTermoUso(usuario);
            } else {
                String validaTelefoneCpf = loginCommons.getGeral().getParametro("validaCpfTelefoneUsuario");
                if ((usuario.getTelefone() == null || usuario.getCpf() == null) && RepositoryComponentDefault.SIM.equals(validaTelefoneCpf)) {
                    redirectEdicaoTelefone(usuario);
                } else {
                    redirectLoginUnidade(usuario);
                }
            }
        }
    }

    private void redirectLoginUnidade(Usuario usuario) throws ValidacaoException, DAOException {
        if (apenasUmaEmpresaUsuario()) {
            getPrimeiraEmpresa();
            acessarSistema();
        } else {
            setResponsePage(new LoginUnidadePage(usuario, empresas));
        }
    }


    private void redirectEdicaoTelefone(Usuario usuario) {
        if (apenasUmaEmpresaUsuario()) {
            setResponsePage(new EdicaoCpfTelefone(usuario, getPrimeiraEmpresa()));
        } else {
            setResponsePage(new EdicaoCpfTelefone(usuario, empresas));
        }
    }

    private void redirectAceiteTermoUso(Usuario usuario) {
        if (apenasUmaEmpresaUsuario()) {
            setResponsePage(new TermoUsoPage(usuario, getPrimeiraEmpresa()));
        } else {
            setResponsePage(new TermoUsoPage(usuario, empresas));
        }
    }

    private void redirectAvisoLogin(Usuario usuario) {
        if (apenasUmaEmpresaUsuario()) {
            setResponsePage(new LoginAvisoPage(usuario, getPrimeiraEmpresa()));
        } else {
            setResponsePage(new LoginAvisoPage(usuario, empresas));
        }
    }

    private boolean apenasUmaEmpresaUsuario() {
        return empresas.size() == 1;
    }

    private Empresa getPrimeiraEmpresa() {
        empresa = empresas.get(0);
        return empresa;
    }

    private void acessarSistema() throws ValidacaoException, DAOException {
        if (empresa == null) {
            throw new ValidacaoException(BundleManager.getString("informe_unidade"));
        }
        usuario = LoginHelper.iniciarSessao(usuario, empresa);
        if (usuario.getDataRegistro() == null) {
            setResponsePage(BemVindoPage.class);
        } else if (usuario.getDiasExpirarSenha() != null
                && Data.adjustRangeHour(Data.addDias(usuario.getDataRegistro(), usuario.getDiasExpirarSenha().intValue())).getDataInicial().before(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial())) {
            setResponsePage(SenhaExpiradaPage.class);
        } else if (usuario.getProgramaWeb() != null) {
            getSession().setAttribute("mobilePaginaUnica", false);
            Class classe = buscarPrograma(usuario.getProgramaWeb());
            boolean pagePermitted = new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSession().getUsuario(), classe.getName());
            if (pagePermitted) {
                setResponsePage(classe);
            } else {
                throw new ValidacaoException(BundleManager.getString("msgUsuarioSemPermissao"));
            }
        } else {
            setResponsePage(HomePage.class);
        }

    }

    private Class buscarPrograma(ProgramaWeb programaWeb) {
        Class classe = null;
        programaWeb = LoadManager.getInstance(ProgramaWeb.class)
                .setId(programaWeb.getCodigo())
                .addProperties(new HQLProperties(ProgramaWeb.class).getProperties())
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CAMINHO_PAGINA))
                .start().getVO();
        try {
            String caminhoPagina = programaWeb.getProgramaPaginaPrincipal().getCaminhoPagina();
            if (caminhoPagina != null) {
                classe = Class.forName(caminhoPagina);
            }
        } catch (ClassNotFoundException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return classe;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (isMobile()) {
            setResponsePage(LoginMobilePage.class);
        }

        response.render(CssHeaderItem.forReference(Resources.CSS_GEM_SAUDE));
        response.render(CssHeaderItem.forReference(Resources.CSS_JQUERY_UI_CUSTOM));
        response.render(JavaScriptHeaderItem.forReference(Application.get().getJavaScriptLibrarySettings().getJQueryReference()));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_MIGRATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DATA_TABLE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_VALIDATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_MASKEDINPUT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_SHORTCUTS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_TREEVIEW));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JGROWL));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_PRINT_ELEMENT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DIRTY_FORMS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_FORM));
        if (txtLogin.isVisible()) {
            response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(txtLogin)));
        }
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_AUTHENTICATE_CERTIFIED));
    }

    private boolean isMobile() {
        String userAgent = WebSession.get().getClientInfo().getUserAgent().toLowerCase();
        return userAgent.contains("android")
                || userAgent.contains("webos")
                || userAgent.contains("iphone")
                || userAgent.contains("ipad")
                || userAgent.contains("ipod")
                || userAgent.contains("blackberry")
                || userAgent.contains("windows phone");
    }

    private void erroAutenticacao(String message) {
        PageParameters pp = new PageParameters();
        pp.add("erro", message);

        setResponsePage(LoginPage.class, pp);
    }

    @Override
    public void search(AjaxRequestTarget target, String key) {
        if (key != null && key.toUpperCase().equals("OK") && usuarioBiometria != null) {
            try {
                Usuario usu = LoadManager.getInstance(Usuario.class).setId(usuarioBiometria).start().getVO();
                efetivarLogin(usu);
            } catch (DAOException ex) {
                erroAutenticacao("Login inválido, tente novamente");
            } catch (ValidacaoException | ResourceException ex) {
                erroAutenticacao(ex.getMessage());
            }
        } else {
            erroAutenticacao("Login inválido, tente novamente");
        }
    }

    @Override
    public void register(AjaxRequestTarget target, String key) {
    }

    public void carregarLogos(Form form) {

        ResourceLink<ResourceReference> favIcon = new ResourceLink<ResourceReference>("lnkFavicon", Resources.Images.FAVICON.resourceReference());
        Image logoSistemaTelaLogin = new Image("logoSistemaTelaLogin", Resources.Images.CELK_SAUDE.resourceReference());
        Image logoLogin = new Image("logo", Resources.Images.CELK_SAUDE_LOGO.resourceReference());


        try {
            File logoFaviconFile = LogoHelper.getLogoFavicon();
            if (logoFaviconFile != null) {
                FileResourceStream fileResourceStream = new FileResourceStream(logoFaviconFile);
                favIcon = new ResourceLink<ResourceReference>("lnkFavicon", new ResourceStreamResource(fileResourceStream)) {
                    @Override
                    protected boolean getStatelessHint() {
                        return true;
                    }
                };
            }
        } catch (Exception ex) {
            favIcon = new ResourceLink<>("lnkFavicon", Resources.Images.FAVICON.resourceReference());
        }


        try {
            File logoLoginFile = LogoHelper.getLogoLogin();
            if (logoLoginFile != null) {
                FileResourceStream fileResourceStream = new FileResourceStream(logoLoginFile);
                logoLogin = new NonCachingImage("logo", new ResourceStreamResource(fileResourceStream)) {
                    @Override
                    protected boolean getStatelessHint() {
                        return true;
                    }
                };
            }
        } catch (Throwable ex) {
            logoLogin = new Image("logo", Resources.Images.CELK_SAUDE_LOGO.resourceReference());
        }

        try {
            File logoSistemaTelaLoginFile = LogoHelper.getLogoSistemaTelaLogin();
            if (logoSistemaTelaLoginFile != null) {
                FileResourceStream fileResourceStream = new FileResourceStream(logoSistemaTelaLoginFile);
                logoSistemaTelaLogin = new NonCachingImage("logoSistemaTelaLogin", new ResourceStreamResource(fileResourceStream)) {
                    @Override
                    protected boolean getStatelessHint() {
                        return true;
                    }
                };
            }
        } catch (Throwable ex) {
            logoSistemaTelaLogin = new Image("logoSistemaTelaLogin", Resources.Images.CELK_SAUDE.resourceReference());
        }

        add(favIcon);
        form.add(logoLogin, logoSistemaTelaLogin);
    }

    public boolean verificaCelular(String numero) {
        return numero != null && numero.matches("\\(\\d{2}\\)\\s9\\d{4}-\\d{4}");
    }

}