<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <link wicket:id="lnkFavicon" rel="shortcut icon" type="image/x-icon"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE8" />
        <title><wicket:message wicket:id="tituloAba" /></title>
        <!--[if lt IE 9]>
                <script src="/gemweb/assets/scripts/html5.js" type="text/javascript"></script>
        <![endif]-->
        <script>
            (function (i, s, o, g, r, a, m) {
                i['GoogleAnalyticsObject'] = r;
                i[r] = i[r] || function () {
                    (i[r].q = i[r].q || []).push(arguments)
                }, i[r].l = 1 * new Date();
                a = s.createElement(o),
                        m = s.getElementsByTagName(o)[0];
                a.async = 1;
                a.src = g;
                m.parentNode.insertBefore(a, m)
            })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

            ga('create', 'UA-65776665-1', 'auto');
            ga('send', 'pageview');
        </script>
    </head>
    <body>
    <header>
        <nav id="master">
            <a wicket:id="linkHome" id="logo" href="#"><img wicket:id="gemIco" style="max-width:190px; max-height:37px;"/></a>
            <div wicket:id="reportNotificationJewel" class="bubble-reports"/>
            <div wicket:id="messagingNotificationJewel" class="bubble-messaging"/>
            <div class="bubble-help">
                <a wicket:id="linkAjuda" id="linkAjuda" href="#" class="icon24 ajuda"></a>
            </div>
            <div class="bubble-help">
                <a wicket:id="linkSuporte" id="linkSuporte" href="#" class="icon24 suporte"></a>
                <div style="margin-top: 5px; margin-left: 10px;"><label style="color : #ffffff; font-size: 13.2px; text-shadow: 0px 1px 0px #242A30;" wicket:id="contatoSuporte" title="Suporte"/></div>
            </div>
            <div>
                <div class="nivel-1">
                    <label class="first" style="color : #ffffff; text-shadow: 0px 1px 0px #242A30;"  wicket:id="empresaLogada"/><a/>
                    <a wicket:id="linkMinhaConta" href="#"><label wicket:id="usuarioLogado" /></a>
                    <a wicket:id="linkConfiguracoes" href="#"><wicket:message key="configuracoes"/></a>
                    <a wicket:id="linkAtalhos" href="#"><img wicket:id="atalhosIcon" /></a>
                    <a wicket:id="linkSair" style="border-right: 0px;" href="#"><wicket:message key="sair"/></a>
                </div>
                <div class="nivel-2">
                    <span><label wicket:id="ultimoAcesso"/></span>
                    <span><label wicket:id="ultimaTentativa"/></span>
                    <span><label wicket:id="competenciaAtual"/></span>
                    <span class="last"><label wicket:id="gemVersion"/></span>
                </div>
            </div>
        </nav>
        <div wicket:id="menu"/>
        <div wicket:id="dev"/>
        <div wicket:id="ambienteTestePanel"/>
        <div wicket:id="loginBloqueadoPanel"/>
    </header>
        <wicket:child />

        <div wicket:id="growl"/>
        <div wicket:id="asyncNotificationPanel"/>
        <div wicket:id="authorizationNotificationPanel"/>

        <form wicket:id="formModal">
            <div wicket:id="modals"/>
        </form>

        <wicket:fragment wicket:id="asyncNotificationPanel-fragment">
        </wicket:fragment>
        <wicket:fragment wicket:id="authorizationNotification-fragment">
        </wicket:fragment>
    </body>
    <footer wicket:id="footerPanel" />

    <wicket:fragment wicket:id="login-bloqueado-fragment">
        <div id="warning-fixed" class="warning error login-bloqueado">
            <span class="icon32 lock"/>
            <span class="text-login"><wicket:message key="loginBloqueado"/></span>
        </div>
    </wicket:fragment>

    <wicket:fragment wicket:id="ambiente-teste-fragment">
        <div id="warning-fixed" class="warning error login-bloqueado" style="opacity: 0.86;">
            <span class="icon32 monitor"/>
            <span class="text-login"><wicket:message key="ambienteTeste"/></span>
        </div>
    </wicket:fragment>

</html>
