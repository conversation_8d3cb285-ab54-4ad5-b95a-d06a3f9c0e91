package br.com.celk.template;

import br.com.celk.annotation.template.Template;
import br.com.celk.component.async.AsyncNotificationPanel;
import br.com.celk.component.authotization.AuthorizationNotificationPanel;
import br.com.celk.component.behavior.JGrowlBehavior;
import br.com.celk.component.button.ScrollToTopBehavior;
import br.com.celk.component.dialog.DlgAjuda;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.interfaces.IModalMessagePage;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.menu.Menu;
import br.com.celk.component.messaging.jewel.MessagingNotificationJewel;
import br.com.celk.component.modalmessage.ModalMessage;
import br.com.celk.component.report.async.ReportNotificationJewel;
import br.com.celk.component.template.footer.FooterPanel;
import br.com.celk.component.window.IWindows;
import br.com.celk.io.LogoHelper;
import br.com.celk.resources.Resources;
import br.com.celk.system.asyncprocess.interfaces.IAsyncReportNotification;
import br.com.celk.system.asyncprocess.interfaces.IMessagingNotification;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.authorization.interfaces.IAuthorizationController;
import br.com.celk.system.authorization.interfaces.IAuthorizationPanel;
import br.com.celk.system.authorization.interfaces.impl.DefaultAuthorizationController;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.LinkConstants;
import br.com.celk.template.controle.PainelControlePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.celk.view.ajuda.AjudaPage;
import br.com.celk.view.controle.usuario.MinhaContaPage;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.celk.view.home.HomePage;
import br.com.celk.view.login.LoginPage;
import br.com.celk.view.login.LoginUnidadePage;
import br.com.celk.view.novidades.JGrowlNovidade;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.dto.NovidadeSistemaDTO;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTO;
import br.com.ksisolucoes.bo.controle.interfaces.dto.DTOControleHorario;
import br.com.ksisolucoes.bo.controle.interfaces.dto.EmpresasUsuarioDTO;
import br.com.ksisolucoes.bo.controle.interfaces.dto.QueryBuscaCaminhoMenuWebDTO;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.dto.EventoSistemaDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoServidor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoCampoObrigatorioException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EventoSistema;
import br.com.ksisolucoes.vo.controle.HorarioDiaSemana;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.ProgramaFavorito;
import br.com.ksisolucoes.vo.controle.web.ProgramaPagina;
import br.com.ksisolucoes.vo.controle.web.ProgramaPaginaPermissao;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.devutils.debugbar.DebugBar;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;
import org.apache.wicket.feedback.FeedbackMessage;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.WebPage;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.image.NonCachingImage;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.html.link.ResourceLink;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.markup.html.panel.Fragment;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.request.resource.ResourceReference;
import org.apache.wicket.request.resource.ResourceStreamResource;
import org.apache.wicket.util.resource.FileResourceStream;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
@Template
public abstract class TemplatePage extends WebPage implements IModalMessagePage, IWindows, IAsyncReportNotification, IMessagingNotification, ITemplatePage {

    private ReportNotificationJewel reportNotificationJewel;
    private MessagingNotificationJewel messagingNotificationJewel;
    private AjudaPage ajudaPage;
    private WebMarkupContainer divGrowl;
    private ModalMessage modalMessage;
    private RepeatingView modals;
    private Form formModals;
    private List<Long> pageActions;
    private Long codigoPrograma;
    private Map<Long, List<Long>> actionsPermitted = new HashMap<Long, List<Long>>();
    private Menu menu;
    private IAuthorizationController authorizationController;
    private IAuthorizationPanel authorizationPanel;
    private Component focusComponentAfterException;
    private DlgAjuda dlgAtalhos;

    private Label label;
    private Label labelSuporte;
    private DlgConfirmacaoOk dlgConfirmacaoOk;

    private JGrowlNovidade jGrowlNovidade;
    private Long countAlteracao;
    Model<String> modelTituloAba;

    private boolean viewBackToTop = true;
    private Boolean mobilePaginaUnica;
    private boolean gerarEventoAcesso;
    private Long habilitarNPS;

    public TemplatePage() {
        this(false);
    }

    public TemplatePage(boolean gerarEventoAcesso) {
        this.gerarEventoAcesso = gerarEventoAcesso;
        initTemplate();
    }

    public TemplatePage(IModel<?> model) {
        this(model, false);
    }

    public TemplatePage(IModel<?> model, boolean gerarEventoAcesso) {
        super(model);
        this.gerarEventoAcesso = gerarEventoAcesso;
        initTemplate();
    }

    public TemplatePage(PageParameters parameters) {
        this(parameters, false);
    }

    public TemplatePage(PageParameters parameters, boolean gerarEventoAcesso) {
        super(parameters);
        this.gerarEventoAcesso = gerarEventoAcesso;
        initTemplate();
    }

    private void initTemplate() {
        mobilePaginaUnica = getSession().getAttribute("mobilePaginaUnica") == null ? false : (Boolean) getSession().getAttribute("mobilePaginaUnica");

        BookmarkablePageLink linkMinhaConta;
        BookmarkablePageLink linkConfiguracoes;

        ResourceLink<ResourceReference> rlnkFavicon = null;

        File logoFaviconFile = LogoHelper.getLogoFavicon();
        try (FileResourceStream fileResourceStream = new FileResourceStream(logoFaviconFile)) {
            if (logoFaviconFile != null) {
                rlnkFavicon = new ResourceLink<ResourceReference>("lnkFavicon", new ResourceStreamResource(fileResourceStream)) {
                    @Override
                    protected boolean getStatelessHint() {
                        return true;
                    }
                };
            }
        } catch (Throwable ex) {
            Loggable.log.debug("FAVICON NAO ENCONTRADO!");
        } finally {
            if (rlnkFavicon == null) {
                rlnkFavicon = new ResourceLink<ResourceReference>("lnkFavicon", Resources.Images.FAVICON.resourceReference());
            }
        }

        add(rlnkFavicon);

        try {
            String title = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("tituloPaginaLogin");
            add(new Label("tituloAba", modelTituloAba = Model.of(title)));
        } catch (DAOException e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        if (getTituloPrograma() != null && !getTituloPrograma().isEmpty()) {
            setTituloAba(getTituloPrograma());
        }

        BookmarkablePageLink linkHome = new BookmarkablePageLink("linkHome", HomePage.class);
        if (mobilePaginaUnica) {
            Class classe = buscarProgramaUnico(ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().getProgramaWeb());
            linkHome = new BookmarkablePageLink("linkHome", classe);
        }
        linkHome.add(getImagePadrao());

        add(linkHome);
        add(reportNotificationJewel = new ReportNotificationJewel("reportNotificationJewel"));
        add(messagingNotificationJewel = new MessagingNotificationJewel("messagingNotificationJewel"));

        add(linkAjuda());

        add(linkMinhaConta = new BookmarkablePageLink("linkMinhaConta", MinhaContaPage.class));

        if (!getRequest().getRequestParameters().getParameterValue("cdPrg").isEmpty()) {
            codigoPrograma = Long.parseLong(getRequest().getRequestParameters().getParameterValue("cdPrg").toString());
        }

        add(linkConfiguracoes = new BookmarkablePageLink("linkConfiguracoes", PainelControlePage.class));

        add(new AbstractAjaxLink("linkSair") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                logout();
            }
        });

        AbstractAjaxLink linkAtalhos;
        add(linkAtalhos = new AbstractAjaxLink("linkAtalhos") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                if (dlgAtalhos == null) {
                    dlgAtalhos = new DlgAjuda(newModalId()) {
                    };
                }
                addModal(target, dlgAtalhos);
                dlgAtalhos.show(target);
            }
        });

        linkAtalhos.add(new Image("atalhosIcon", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.ALT.resourceReference();
            }
        }));
        linkAtalhos.add(new AttributeModifier("title", BundleManager.getString("atalhos")));


        add(menu = new Menu("menu"));

        menu.setVisible(!mobilePaginaUnica);

        if (getApplication().getDebugSettings().isDevelopmentUtilitiesEnabled()) {
            add(new DebugBar("dev"));
        } else {
            add(new EmptyPanel("dev").setVisible(false));
        }

        linkMinhaConta.add(new Label("usuarioLogado", new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return StringUtil.getStringMaxPrecision(ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().getNome(), 22) + "...";
            }
        }));
        linkMinhaConta.add(new AttributeModifier("title", ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().getNome()));
        linkConfiguracoes.setVisible(ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().isNivelAdminOrMaster());

        add(label = new Label("empresaLogada", new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                String descricaoEmpresa = StringUtil.getStringMaxPrecision(ApplicationSession.get().getSessaoAplicacao().<Empresa>getEmpresa().getDescricao(), 38) + "...";
                return descricaoEmpresa;
            }
        }));
        label.setOutputMarkupId(true);
        label.add(new AttributeModifier("title", ApplicationSession.get().getSessaoAplicacao().<Empresa>getEmpresa().getDescricao()));

        add(linkSuporte());

        add(labelSuporte = new Label("contatoSuporte", new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                String contatoSuporte = "";
                try {
                    contatoSuporte = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("Numero_suporte");
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
                return contatoSuporte;
            }
        }));
        labelSuporte.setOutputMarkupId(true);

        AbstractAjaxLink linkEmpresa;
        add(linkEmpresa = new AbstractAjaxLink("btnTrocarEmpresa") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                List<Empresa> lstEmpresa = BOFactoryWicket.getBO(UsuarioFacade.class).getListEmpresasUsuario(new EmpresasUsuarioDTO(SessaoAplicacaoImp.getInstance().getUsuario()));
                Page page = new LoginUnidadePage(SessaoAplicacaoImp.getInstance().getUsuario(), lstEmpresa);
                setResponsePage(page);
            }
        });

        linkEmpresa.add(new Image("editarIcon", new LoadableDetachableModel<ResourceReference>() {
            @Override
            protected ResourceReference load() {
                return Resources.Images.TROCAR_EMPRESA.resourceReference();
            }
        }));
        linkEmpresa.add(new AttributeModifier("title", BundleManager.getString("alterarEmpresa")));
        linkEmpresa.setEnabled(false);


        add(new Label("competenciaAtual", new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                Long diaInicioCompetencia = CargaBasicoPadrao.getInstance().getParametroAtendimento().getDiaInicioCompetencia();
                String competenciaAtual = "";
                if (diaInicioCompetencia != null) {
                    Date compAtual = Data.competenciaData(diaInicioCompetencia.intValue(), Data.getDataAtual());
                    competenciaAtual = new SimpleDateFormat("MM/yyyy").format(compAtual);
                }
                return competenciaAtual;
            }
        }));

        add(new Label("ultimoAcesso", new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                StringBuilder sb = new StringBuilder();
                sb.append(Bundle.getStringApplication("data_ultimo_acesso", ApplicationSession.get().getSessaoAplicacao().getDataUltimoAcesso()));
                return sb.toString();
            }
        }));

        add(new Label("ultimaTentativa", new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                StringBuilder sb = new StringBuilder();
                sb.append(Bundle.getStringApplication("data_ultima_tentativa", ApplicationSession.get().getSessaoAplicacao().getDataUltimaTentativa()));

                return sb.toString();
            }
        }).setVisible(ApplicationSession.get().getSessaoAplicacao().getDataUltimaTentativa() != null));

        Long loginBloqueado = CargaBasicoPadrao.getInstance().getParametroPadrao().getLoginBloqueado();
        if (RepositoryComponentDefault.SimNaoLong.SIM.value().equals(loginBloqueado)
                && ApplicationSession.get().getSessaoAplicacao().getUsuario().isNivelAdminOrMaster()) {
            Fragment loginBloqueadoPanel = new Fragment("loginBloqueadoPanel", "login-bloqueado-fragment", this);
            add(loginBloqueadoPanel);
        } else {
            add(new EmptyPanel("loginBloqueadoPanel"));
        }

        if (!"producao".equals(System.getProperty("ambiente"))) {
            add(new Fragment("ambienteTestePanel", "ambiente-teste-fragment", this));
        } else {
            add(new EmptyPanel("ambienteTestePanel"));
        }

        add(new FooterPanel("footerPanel"));

        add(new AsyncNotificationPanel("asyncNotificationPanel", "asyncNotificationPanel-fragment", this));

        authorizationPanel = new AuthorizationNotificationPanel("authorizationNotificationPanel");
        add((Component) authorizationPanel);

        divGrowl = new WebMarkupContainer("growl");

        divGrowl.add(new JGrowlBehavior(divGrowl));

        add(divGrowl);

        initModalsRepeater();

        add(new Label("gemVersion", new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return ApplicationSession.get().getVersao();
            }
        }));

        if (viewBackToTop) {
            add(new ScrollToTopBehavior());
        }

        addNotasSistema();
        gerarEventoAcesso();
    }

    private AjaxLink linkAjuda() {
        AjaxLink linkAjuda = new AjaxLink<String>("linkAjuda") {
            @Override
            public void onClick(AjaxRequestTarget target) {
                target.appendJavaScript("setTimeout(\"window.open('".concat(LinkConstants.AJUDA_PAGE).concat("','_blank')\", 100);"));
            }
        };
        linkAjuda.add(new AttributeModifier("title", BundleManager.getString("ajuda_descricao")));
        return linkAjuda;
    }

    private AjaxLink linkSuporte() {
        String urlSistemaSuporte = "";
        try {
            urlSistemaSuporte = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("urlSistemaSuporte");
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        final String finalUrlSistemaSuporte = urlSistemaSuporte;
        AjaxLink linkSuporte = new AjaxLink<String>("linkSuporte") {
            @Override
            public void onClick(AjaxRequestTarget target) {
                if (StringUtils.isNotEmpty(finalUrlSistemaSuporte)) {
                    target.appendJavaScript("setTimeout(\"window.open('".concat(finalUrlSistemaSuporte).concat("','_blank')\", 100);"));
                }
            }
        };
        linkSuporte.add(new AttributeModifier("title", BundleManager.getString("suporte")));
        linkSuporte.setEnabled(StringUtils.isNotEmpty(urlSistemaSuporte));
        return linkSuporte;
    }

    private Component getImagePadrao() {
        Image imagemLogo = new Image("gemIco", Resources.Images.CELK_SAUDE_LOGO.resourceReference());

        try {
            File logoSistemaFile = LogoHelper.getLogoSistema();
            if (logoSistemaFile != null) {
                FileResourceStream fileResourceStream = new FileResourceStream(logoSistemaFile);
                imagemLogo = new NonCachingImage("gemIco", new ResourceStreamResource(fileResourceStream)) {
                    @Override
                    protected boolean getStatelessHint() {
                        return true;
                    }
                };
            }
        } catch (Throwable ex) {
            imagemLogo = new Image("gemIco", Resources.Images.CELK_SAUDE_LOGO.resourceReference());
        }

        return imagemLogo;
    }

    private void addNotasSistema() {
        try {
            NovidadeSistemaDTO dto = new NovidadeSistemaDTO();
            dto.setCodigoPrograma(codigoPrograma);
            dto.setVersao(ApplicationSession.get().getVersao());

            countAlteracao = BOFactoryWicket.getBO(BasicoFacade.class).consultarNovasNotasPorUsuario(dto);
            if (countAlteracao != null && countAlteracao > 0) {
                jGrowlNovidade = new JGrowlNovidade();
                add(jGrowlNovidade.createBehavior(getCodigoPrograma()));
            }
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    public void setTituloAba(String titulo) {
        modelTituloAba.setObject(titulo);
    }

    private void initModalsRepeater() {
        formModals = new Form("formModal");
        formModals.setOutputMarkupId(true);
        formModals.add(modals = new RepeatingView("modals"));
        modals.add(modalMessage = new ModalMessage(modals.newChildId()) {
            @Override
            public void onOkAction(AjaxRequestTarget target) {
                TemplatePage.this.onExceptionOkAction(target);
            }
        });
        add(formModals);
    }

    public void logout() throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(UsuarioFacade.class).logout(ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario());
        getSession().invalidate();
        setResponsePage(LoginPage.class);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        response.render(CssHeaderItem.forReference(Resources.CSS_GEM_SAUDE));
        response.render(CssHeaderItem.forReference(Resources.CSS_WICKET_BOOTSTRAP));
        response.render(CssHeaderItem.forReference(Resources.CSS_JQUERY_UI_CUSTOM));
//        response.render(JavaScriptHeaderItem.forReference(Application.get().getJavaScriptLibrarySettings().getJQueryReference()));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_MIGRATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DATA_TABLE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_VALIDATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_MASKEDINPUT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_SHORTCUTS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_TREEVIEW));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JGROWL));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_PRINT_ELEMENT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DIRTY_FORMS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_FORM));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_SHORTCUT));
        response.render(OnDomReadyHeaderItem.forScript("$('body').find('form').keydown(function(ev){\n"
                + "        if(ev.which == 13 && ev.target.nodeName!='TEXTAREA') ev.preventDefault();\n"
                + "    });"));
        FormComponent componenteRequestFocus = getComponentRequestFocus();
        if (componenteRequestFocus != null) {
            response.render(OnDomReadyHeaderItem.forScript("$('#" + componenteRequestFocus.getMarkupId() + "').focus();"));
            response.render(OnDomReadyHeaderItem.forScript("$('#" + componenteRequestFocus.getMarkupId() + "').select();"));
        }
        if (countAlteracao != null && countAlteracao > 0) {
            response.render(OnDomReadyHeaderItem.forScript(jGrowlNovidade.growlNovaMensagem(BundleManager.getString("msgExistemNovasAlteracoesNestaTela_x", countAlteracao), true)));
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(getHabilitarNPS())) {
            response.render(OnDomReadyHeaderItem.forScript(Resources.getScriptNPS()));
        }
    }

    public FormComponent getComponentRequestFocus() {
        return null;
    }

    @Override
    public void addModal(ModalWindow window) {
        modals.add(window);
    }

    @Override
    public void addModal(AjaxRequestTarget target, ModalWindow window) {
        modals.add(window);
        target.add(formModals);
    }

    @Override
    public String newModalId() {
        return modals.newChildId();
    }

    public abstract String getTituloPrograma();

    public void growlInfo(AjaxRequestTarget target, String message) {
        growlMessage(target, message, FeedbackMessage.INFO);
    }

    public void growlWarn(AjaxRequestTarget target, String message) {
        growlMessage(target, message, FeedbackMessage.WARNING);
    }

    public void growlError(AjaxRequestTarget target, String message) {
        growlMessage(target, message, FeedbackMessage.ERROR);
    }

    public void growlMessage(AjaxRequestTarget target, String message, int lvl) {
        getSession().getFeedbackMessages().add(new FeedbackMessage(divGrowl, message, lvl));
        target.add(divGrowl);
    }

    @Override
    public void modalWarn(AjaxRequestTarget target, Throwable ex) {
        modalMessage.warn(target, ex);
    }

    /**
     * Ação do botão OK do dialog da exceção. Por padrão seta foco no componente
     * se não for null
     *
     * @param target
     */
    public void onExceptionOkAction(AjaxRequestTarget target) {
        if (getFocusComponentAfterException() != null) {
            target.focusComponent(getFocusComponentAfterException());
        }
    }

    public Component getFocusComponentAfterException() {
        return focusComponentAfterException;
    }

    /**
     * Seta o componente que receberá o foco após o botão OK do dialog da
     * exceção ser pressionado
     *
     * @param focusComponentAfterException
     */
    public void setFocusComponentAfterException(Component focusComponentAfterException) {
        this.focusComponentAfterException = focusComponentAfterException;
    }

    @Override
    public void modalError(AjaxRequestTarget target, Throwable ex) {
        modalMessage.error(target, ex);
    }

    @Override
    public boolean isActionPermitted(Permissions action) {
        return isActionPermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), action);
    }

    @Override
    public boolean isActionPermitted(Permissions action, boolean ignoreUserLevel) {
        return isActionPermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), action, ignoreUserLevel);
    }

    @Override
    public boolean isActionPermitted(Usuario usuario, Permissions action) {
        return isActionPermitted(usuario, action, getClass());
    }

    @Override
    public boolean isActionPermitted(Usuario usuario, Permissions action, boolean ignoreUserLevel) {
        return isActionPermitted(usuario, action, getClass(), ignoreUserLevel);
    }

    @Override
    public boolean isActionPermitted(Permissions action, Class clazz) {
        return isActionPermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), action, clazz);
    }

    @Override
    public boolean isActionPermitted(Permissions action, Class clazz, boolean ignoreUserLevel) {
        return isActionPermitted(ApplicationSession.get().getSession().<Usuario>getUsuario(), action, clazz, ignoreUserLevel);
    }

    @Override
    public boolean isActionPermitted(Usuario usuario, Permissions action, Class clazz) {
        return isActionPermitted(usuario, action, clazz, false);
    }

    public boolean isActionPermitted(Usuario usuario, Permissions action, Class clazz, boolean ignoreUserLevel) {
        if (!ignoreUserLevel && usuario.isNivelAdminOrMaster()) {
            return true;
        }

        if (pageActions == null) {
            pageActions = new ArrayList<Long>();
            List<ProgramaPaginaPermissao> permissoes = LoadManager.getInstance(ProgramaPaginaPermissao.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProgramaPaginaPermissao.PROP_PROGRAMA_PAGINA, ProgramaPagina.PROP_CAMINHO_PAGINA), clazz.getName()))
                    .start().getList();
            for (ProgramaPaginaPermissao programaPaginaPermissao : permissoes) {
                pageActions.add(programaPaginaPermissao.getPermissaoWeb().getCodigo());
            }
        }

        if (!pageActions.contains(action.value())) {
            return true;
        }

        if (!actionsPermitted.containsKey(usuario.getCodigo())) {
            List<Long> permissoes = new PermissoesWebUtil().getActionsPermitted(usuario.getCodigo(), clazz.getName());
            actionsPermitted.put(usuario.getCodigo(), permissoes);
        }

        List<Long> permissions = actionsPermitted.get(usuario.getCodigo());
        if (permissions == null) {
            return false;
        }

        return permissions.contains(action.value());
    }

    @Override
    public void notifyReport(AjaxRequestTarget target, AsyncProcess asyncProcess) {
        reportNotificationJewel.notifyReport(target, asyncProcess);
    }

    @Override
    public void notifyMessage(AjaxRequestTarget target, QueryConsultaMensagensDTO dto) {
        messagingNotificationJewel.notifyMessage(target, dto);
    }

    public void addFavorito(AjaxRequestTarget target, ProgramaFavorito programaFavorito) {
        menu.adicionarFavorito(target, programaFavorito);
    }

    public final Long getCodigoPrograma() {
        return codigoPrograma;
    }

    public String getDescricaoCaminhoMenu() {
        StringBuilder sb = new StringBuilder();
        String caminho = getCaminhoMenu();
        if (!"".equals(br.com.celk.util.Coalesce.asString(caminho))) {
            String[] split = caminho.split(":;");
            for (int i = 0; i < split.length; i++) {
                if (!"".equals(br.com.celk.util.Coalesce.asString(split[i]))) {
                    if (i != 0) {
                        sb.append(" / ");
                    }
                    String lnk = split[i];
                    sb.append(lnk);
                }
            }
        }

        return sb.toString();
    }

    private String getCaminhoMenu() {
        String caminho = "";
        if (getCodigoPrograma() != null) {
            ProgramaWeb vo = LoadManager.getInstance(ProgramaWeb.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(ProgramaWeb.PROP_CODIGO, getCodigoPrograma()))
                    .start().getVO();
            try {
                List<QueryBuscaCaminhoMenuWebDTO> caminhos = BOFactoryWicket.getBO(UsuarioFacade.class).buscarCaminhoMenuWeb(vo);
                caminho = caminhos.get(0).getCaminho();
            } catch (DAOException ex) {
                Logger.getLogger(TemplatePage.class.getName()).log(Level.SEVERE, null, ex);
            } catch (ValidacaoException ex) {
                Logger.getLogger(TemplatePage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return caminho;
    }

    public IAuthorizationController getAuthorizationController() {
        if (this.authorizationController == null) {
            this.authorizationController = new DefaultAuthorizationController(this, authorizationPanel);
        }

        return this.authorizationController;
    }

    public void setAuthorizationController(IAuthorizationController authorizationController) {
        this.authorizationController = authorizationController;
    }

    public DropDown getEmpresasUsuario() {
        DropDown dropDownUnidadeLogada = new DropDown("unidade", new PropertyModel(this, "unidade"));
        List<Empresa> listaUnidade;
        try {
            listaUnidade = BOFactoryWicket.getBO(UsuarioFacade.class).getListEmpresasUsuario(new EmpresasUsuarioDTO(SessaoAplicacaoImp.getInstance().getUsuario()));
        } catch (ValidacaoException | DAOException ex) {
            Logger.getLogger(TemplatePage.class.getName()).log(Level.SEVERE, null, ex);
            listaUnidade = null;
        }

        if (CollectionUtils.isNotNullEmpty(listaUnidade) && listaUnidade.size() == 1) {
            dropDownUnidadeLogada.setVisible(false);
            label.setVisible(true);
        } else {
            dropDownUnidadeLogada.setVisible(true);
            label.setVisible(false);
        }

        for (Empresa empresa : listaUnidade) {
            dropDownUnidadeLogada.addChoice(empresa, empresa.getDescricao());
        }

        dropDownUnidadeLogada.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {

            }
        });

        return dropDownUnidadeLogada;
    }

    public void alterarEmpresa(AjaxRequestTarget target, Empresa unidade) {
        initDlgConfirmacao(target);
        try {
            validarHorarioAcesso(unidade);
        } catch (ValidacaoException ex) {
            Loggable.log.info(ex.getMessage());
            dlgConfirmacaoOk.setMessage(target, ex.getMessage());
            dlgConfirmacaoOk.show(target);
            return;
        } catch (DAOException ex) {
            Logger.getLogger(TemplatePage.class.getName()).log(Level.SEVERE, null, ex);
            dlgConfirmacaoOk.setMessage(target, ex.getMessage());
            dlgConfirmacaoOk.show(target);
            return;
        }

        SessaoAplicacaoServidor sessaoAplicacao = SessaoAplicacaoServidor.getNewInstance(SessaoAplicacaoImp.getInstance().getUsuario(), unidade, Bundle.getLocale());
        sessaoAplicacao.setExecucaoLocal(false);
        sessaoAplicacao.setIpClient(WicketMethods.getIpClient());
        ApplicationSession.get().setSessaoAplicacao(sessaoAplicacao);

        setResponsePage(HomePage.class);
    }

    private void validarHorarioAcesso(Empresa unidade) throws ValidacaoException, DAOException {
        if (!SessaoAplicacaoImp.getInstance().getUsuario().isNivelAdminOrMaster()) {
            Calendar calendar = GregorianCalendar.getInstance();

            DTOControleHorario controleHorario = new DTOControleHorario();

            calendar.setTime(Data.getDataAtual());

            controleHorario.setDiaSemana(new Integer(calendar.get(Calendar.DAY_OF_WEEK)).longValue());

            calendar.setTime(Data.parserHour(Data.formatarHora(Data.getDataAtual())));
            controleHorario.setHoraInicial(calendar.getTime());
            controleHorario.setHoraFinal(calendar.getTime());
            controleHorario.setUsuario(SessaoAplicacaoImp.getInstance().<Usuario>getUsuario());
            controleHorario.setEmpresa(unidade);

            List<HorarioDiaSemana> validarHorarios = BOFactory.getBO(UsuarioFacade.class).validarHorarios(controleHorario, DTOControleHorario.TipoValidacao.UNIDADE_INFORMADA);

            if (!CollectionUtils.isNotNullEmpty(validarHorarios)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_usuario_nao_habilitado_para_acessar_esta_unidade_neste_horario"));
            }
        }
    }

    private void initDlgConfirmacao(AjaxRequestTarget target) {
        if (dlgConfirmacaoOk == null) {
            addModal(target, dlgConfirmacaoOk = new DlgConfirmacaoOk(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    target.add(label);
                }
            });
        }
    }

    public void setViewBackToTop(boolean viewBackToTop) {
        this.viewBackToTop = viewBackToTop;
    }

    private Class buscarProgramaUnico(ProgramaWeb programaWeb) {
        Class classe = null;
        programaWeb = LoadManager.getInstance(ProgramaWeb.class)
                .setId(programaWeb.getCodigo())
                .addProperties(new HQLProperties(ProgramaWeb.class).getProperties())
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CAMINHO_PAGINA))
                .start().getVO();
        try {
            String caminhoPagina = programaWeb.getProgramaPaginaPrincipal().getCaminhoPagina();
            if (caminhoPagina != null) {
                classe = Class.forName(caminhoPagina);
            }
        } catch (ClassNotFoundException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return classe;
    }

    private void gerarEventoAcesso() {
        if (gerarEventoAcesso) {
            try {
                EventoSistemaDTO eventoSistemaDTO = new EventoSistemaDTO();
                StringBuilder dsEvento = new StringBuilder("Acesso a página (");
                dsEvento.append(getCodigoPrograma());
                dsEvento.append(") ");
                dsEvento.append(getDescricaoCaminhoMenu());
                dsEvento.append(".");
                eventoSistemaDTO.setNivelCriticidade(EventoSistema.NivelCriticidade.INFORMACAO.value());
                eventoSistemaDTO.setKeyword(Bundle.getStringApplication("key_acesso"));
                eventoSistemaDTO.setTipoEvento(EventoSistema.TipoEvento.PROGRAMA.value());
                eventoSistemaDTO.setIdentificacaoEvento(EventoSistema.IdentificacaoEvento.ACESSOS.value());
                eventoSistemaDTO.setFonteEvento(Bundle.getStringApplication("rotulo_acesso"));
                eventoSistemaDTO.setDescricao(dsEvento.toString());

                BOFactoryWicket.getBO(CommomFacade.class).gerarEventoSistema(eventoSistemaDTO);
            } catch (ValidacaoException e) {
                Logger.getLogger(TemplatePage.class.getName()).log(Level.SEVERE, null, e);
            } catch (DAOException e) {
                Logger.getLogger(TemplatePage.class.getName()).log(Level.SEVERE, null, e);
            }
        }
    }

    private Long getHabilitarNPS() {
        if (habilitarNPS == null) {
            try {
                habilitarNPS = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("habilitarNPS");
            } catch (DAOException e) {
                br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        return habilitarNPS;
    }

    protected void validarObjetoObrigatorio(Object objeto, Component componente, String mensagem) throws ValidacaoException {
        if (objeto == null) {
            if (componente != null) setFocusComponentAfterException(componente);
            throw new ValidacaoCampoObrigatorioException(BundleManager.getString(mensagem));
        }
    }

    protected void validarRegra(boolean regra, Component componente, String mensagem) throws ValidacaoException {
        if (regra) {
            if (componente != null) setFocusComponentAfterException(componente);
            throw new ValidacaoException(Bundle.getStringApplication(mensagem));
        }
    }

    public MessagingNotificationJewel getMessagingNotificationJewel() {
        return messagingNotificationJewel;
    }
}
