package br.com.celk.template;

import br.com.celk.system.authorization.Permissions;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 * Created by diego on 15/07/16.
 */
public interface ITemplatePage {

    boolean isActionPermitted(Permissions action);

    boolean isActionPermitted(Permissions action, boolean ignoreUserLevel);

    boolean isActionPermitted(Usuario usuario, Permissions action);

    boolean isActionPermitted(Usuario usuario, Permissions action, boolean ignoreUserLevel);

    boolean isActionPermitted(Permissions action, Class clazz);

    boolean isActionPermitted(Permissions action, Class clazz, boolean ignoreUserLevel);

    boolean isActionPermitted(Usuario usuario, Permissions action, Class clazz);

    boolean isActionPermitted(Usuario usuario, Permissions action, Class clazz, boolean ignoreUserLevel);

}
