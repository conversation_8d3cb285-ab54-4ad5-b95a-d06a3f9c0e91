package br.com.celk.component.menu;

import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import br.com.celk.component.menu.estrutura.MenuItem;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.Coalesce;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR> Colombo
 */
public class MenuController implements Serializable {
    
    private MenuCache menuCache = MenuCache.get();
    
    private List<MenuItem> fullUserMenuTree;
    private List<ProgramaWeb> programasUsuario;
    private Long layoutMenu;
    
    private IProgramaPermitido programaPermitido = new IProgramaPermitido() {

        @Override
        public boolean isProgramaPermitido(MenuWeb menuPrograma, List<ProgramaWeb> programasUsuario) {
            if (ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().isNivelMaster()) {
            return true;
            }
            if (RepositoryComponentDefault.SIM.equals(menuPrograma.getProgramaWeb().getAtivo())) {
                if ((ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().isNivelAdminOrMaster() ||
                        programasUsuario.contains(menuPrograma.getProgramaWeb()))) {
                    return true;
                }
            }
            return false;
        }
    };

    public MenuController() {
    }
    
    public MenuController(IProgramaPermitido programaPermitido) {
        this.programaPermitido = programaPermitido;
    }
    
    public void buildFullMenuTree(){
        List<MenuItem> fullMenuTree = new ArrayList<MenuItem>();
        for (MenuWeb modulo : getModulos()) {
            
            if(!layoutMenuValido(modulo)){
                continue;
            }
            
            MenuItem menuModulo = new MenuItem();
            menuModulo.setDescricao(Coalesce.asString(modulo.getDescricao()));
            menuModulo.setMenuWeb(modulo);

            if (modulo.getProgramaWeb() != null) {
                menuModulo.setClassName(modulo.getProgramaWeb().getProgramaPaginaPrincipal().getCaminhoPagina());
            }
            
            buildMenu(menuModulo);
            
            if (CollectionUtils.isNotNullEmpty(menuModulo.getChilds())) {
                fullMenuTree.add(menuModulo);
            }
        }
        
        this.fullUserMenuTree = fullMenuTree;
    }

    public List<MenuItem> getMenu(){
        if (this.fullUserMenuTree == null) {
            AbstractSessaoAplicacao sessaoAplicacao = ApplicationSession.get().getSessaoAplicacao();
            this.programasUsuario = new PermissoesWebUtil().getAllUserPrograms(sessaoAplicacao.<Usuario>getUsuario());
            buildFullMenuTree();
        }
        
        return this.fullUserMenuTree;
    }

    private boolean isProgramaPermitido(MenuWeb menuPrograma){
        return this.programaPermitido.isProgramaPermitido(menuPrograma, programasUsuario);
    }

    private void buildMenu(MenuItem pai){
        List<MenuItem> items = new ArrayList<MenuItem>();

        for (MenuWeb item : getChilds(pai.getMenuWeb())) {
            
            if(!layoutMenuValido(item)){
                continue;
            }

            MenuItem menuItem = new MenuItem();
            menuItem.setChilds(items);
            menuItem.setMenuWeb(item);
            menuItem.setDescricao(Coalesce.asString(item.getDescricao()));
            if (item.getProgramaWeb() != null) {
                menuItem.setClassName(item.getProgramaWeb().getProgramaPaginaPrincipal().getCaminhoPagina());
            }
            
            buildMenu(menuItem);

            if (CollectionUtils.isNotNullEmpty(menuItem.getChilds())
                    || (menuItem.getMenuWeb().getProgramaWeb() != null && isProgramaPermitido(menuItem.getMenuWeb()))) {
                items.add(menuItem);
            }
        }
        pai.setChilds(items);
    }
    
    private List<MenuWeb> getModulos(){
        return this.menuCache.getModulos();
    }
    
    private List<MenuWeb> getChilds(MenuWeb menuWeb){
        List<MenuWeb> childsList = this.menuCache.getChilds(menuWeb);
        return childsList == null ? Collections.EMPTY_LIST : childsList;
    }
    
    private Long getLaytouMenu(){
        if(layoutMenu == null){
            layoutMenu = MenuWeb.LayoutMenu.PADRAO.value();

            try {
                layoutMenu = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("layoutMenu");
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return layoutMenu;
    }
    
    private boolean layoutMenuValido(MenuWeb menuWeb){
        if(!ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario().isNivelMaster() && !MenuWeb.LayoutMenu.PADRAO.value().equals(getLaytouMenu())){
            if(!getLaytouMenu().equals(menuWeb.getLayoutMenu()) && !MenuWeb.LayoutMenu.PADRAO.value().equals(menuWeb.getLayoutMenu())){
                return false;
            }
        }
        
        return true;
    }
    
}
