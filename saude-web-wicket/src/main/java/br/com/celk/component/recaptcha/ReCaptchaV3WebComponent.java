package br.com.celk.component.recaptcha;

import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.MarkupStream;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.IHeaderContributor;
import org.apache.wicket.markup.html.WebComponent;
import org.apache.wicket.request.cycle.RequestCycle;

/**
 * WebComponent para Google reCAPTCHA v3 que funciona de forma invisível
 * <p>
 * reCAPTCHA v3 retorna uma pontuação (score) de 0.0 a 1.0:
 * - 1.0 = muito provavelmente humano
 * - 0.0 = muito provavelmente bot
 * - recomendado: 0.5
 * <p>
 * Uso simples:
 * ReCaptchaV3WebComponent recaptcha = new ReCaptchaV3WebComponent("recaptcha", "submit");
 * form.add(recaptcha);
 * <p>
 */
public class ReCaptchaV3WebComponent extends WebComponent implements IHeaderContributor {

    private static final long serialVersionUID = 1L;

    private String siteKey;
    private boolean enabled = true;
    private String action = "submit";
    private String hiddenFieldId;
    private double minScore = 0.5; // Score mínimo para considerar válido

    // Controle de validação com tempo de validade
    private String lastValidatedToken;
    private long lastValidationTime;
    private static final long TOKEN_VALIDITY_DURATION = 120000; // 2 minutos em millisegundos

    public ReCaptchaV3WebComponent(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        setOutputMarkupPlaceholderTag(true);

        // Gerar ID único para o campo hidden
        this.hiddenFieldId = "recaptcha_token_" + getId();

        loadConfiguration();
    }

    private void loadConfiguration() {
        String googleReCaptchaSiteKey = ReCaptchaV3Service.getSiteKey();
        if (ReCaptchaV3Service.isEnabled()) {
            this.siteKey = googleReCaptchaSiteKey;
        } else {
            enabled = false;
        }
    }

    @Override
    protected void onComponentTag(ComponentTag tag) {
        super.onComponentTag(tag);

        if (enabled) {
            // Transformar a tag em um span invisível (reCAPTCHA v3 é invisível)
            tag.setName("span");
            tag.put("class", "recaptcha-v3-component");
            tag.put("id", getMarkupId());
            tag.put("style", "display: none;");
        } else {
            // Se desabilitado, criar span vazio e invisível
            tag.setName("span");
            tag.put("style", "display: none;");
        }
    }

    @Override
    public void onComponentTagBody(MarkupStream markupStream, ComponentTag openTag) {
        if (enabled) {
            // Gerar campos ocultos para armazenar token e score
            StringBuilder html = new StringBuilder();
            html.append("<input type=\"hidden\" id=\"").append(escapeHtml(hiddenFieldId)).append("\" name=\"").append(escapeHtml(hiddenFieldId)).append("\" />");
            replaceComponentTagBody(markupStream, openTag, html.toString());
        } else {
            // Se desabilitado, não renderizar nada
            replaceComponentTagBody(markupStream, openTag, "");
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        if (!enabled) {
            return;
        }

        // Carregar script do Google reCAPTCHA v3
        response.render(JavaScriptHeaderItem.forUrl("https://www.google.com/recaptcha/api.js?render=" + siteKey));

        String componentId = getMarkupId();
        String escapedSiteKey = escapeJavaScript(siteKey);
        String escapedAction = escapeJavaScript(action);
        String escapedFieldId = escapeJavaScript(hiddenFieldId);

        StringBuilder scriptBuilder = new StringBuilder();

        // JavaScript com renovação automática
        scriptBuilder.append("var recaptchaTokenTimestamp_").append(componentId).append(" = 0;");
        scriptBuilder.append("var recaptchaTokenDuration = 110000;"); // 110 segundos (um pouco menos que 2 min)

        scriptBuilder.append("function executeRecaptcha_").append(componentId).append("() {");
        scriptBuilder.append("  if (typeof grecaptcha !== 'undefined') {");
        scriptBuilder.append("    grecaptcha.ready(function() {");
        scriptBuilder.append("      grecaptcha.execute('").append(escapedSiteKey).append("', { action: '").append(escapedAction).append("' }).then(function(token) {");
        scriptBuilder.append("        var field = document.getElementById('").append(escapedFieldId).append("');");
        scriptBuilder.append("        if (field) {");
        scriptBuilder.append("          field.value = token;");
        scriptBuilder.append("          recaptchaTokenTimestamp_").append(componentId).append(" = Date.now();");
        scriptBuilder.append("        }");
        scriptBuilder.append("      });");
        scriptBuilder.append("    });");
        scriptBuilder.append("  } else {");
        scriptBuilder.append("    setTimeout(executeRecaptcha_").append(componentId).append(", 1000);");
        scriptBuilder.append("  }");
        scriptBuilder.append("}");

        // Função para verificar se token precisa ser renovado
        scriptBuilder.append("function checkTokenExpiration_").append(componentId).append("() {");
        scriptBuilder.append("  var elapsed = Date.now() - recaptchaTokenTimestamp_").append(componentId).append(";");
        scriptBuilder.append("  if (elapsed > recaptchaTokenDuration) {");
        scriptBuilder.append("    executeRecaptcha_").append(componentId).append("();");
        scriptBuilder.append("  }");
        scriptBuilder.append("}");

        // Executar quando a página carregar
        scriptBuilder.append("executeRecaptcha_").append(componentId).append("();");

        // Verificar expiração a cada 30 segundos
        scriptBuilder.append("setInterval(checkTokenExpiration_").append(componentId).append(", 30000);");

        // Função global para refresh manual
        scriptBuilder.append("window.refreshRecaptcha_").append(componentId).append(" = executeRecaptcha_").append(componentId).append(";");

        response.render(OnLoadHeaderItem.forScript(scriptBuilder.toString()));
    }

    /**
     * Verifica se o reCAPTCHA v3 foi completado (tem token)
     */
    public boolean isCompleted() {
        if (!enabled) {
            return true; // Se desabilitado, considera como completado
        }

        try {
            String token = RequestCycle.get().getRequest()
                    .getRequestParameters()
                    .getParameterValue(hiddenFieldId)
                    .toString();
            return token != null && !token.trim().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Obtém o token do reCAPTCHA v3
     */
    public String getRecaptchaToken() {
        if (!enabled) {
            return null;
        }

        try {
            return RequestCycle.get().getRequest()
                    .getRequestParameters()
                    .getParameterValue(hiddenFieldId)
                    .toString();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * considerando o tempo de validade do token. Renova automaticamente tokens expirados.
     */
    public ReCaptchaV3Service.ValidationResult validate(String expectedAction, double minScore) {
        if (!enabled) {
            // Se desabilitado, retorna sucesso
            ReCaptchaV3Service.ValidationResult result = new ReCaptchaV3Service.ValidationResult();
            result.setSuccess(true);
            result.setScore(1.0);
            result.setAction(expectedAction);
            return result;
        }

        String currentToken = getRecaptchaToken();
        long currentTime = System.currentTimeMillis();

        // Verificar se é o mesmo token e ainda está dentro do tempo de validade
        if (lastValidatedToken != null &&
                lastValidatedToken.equals(currentToken) &&
                (currentTime - lastValidationTime) < TOKEN_VALIDITY_DURATION) {

            // Token já validado e ainda válido, retornar sucesso sem re-validar
            ReCaptchaV3Service.ValidationResult cachedResult = new ReCaptchaV3Service.ValidationResult();
            cachedResult.setSuccess(true);
            cachedResult.setScore(minScore); // Usar score mínimo como referência
            cachedResult.setAction(expectedAction);
            return cachedResult;
        }

        // Validar com o Google (primeira vez ou token expirado)
        ReCaptchaV3Service.ValidationResult result = ReCaptchaV3Service.validate(currentToken, expectedAction, minScore);

        // Se falhou por token expirado/inválido, tentar renovar automaticamente
        if (!result.isSuccess() && isTokenExpiredError(result)) {

            clearValidationCache();

            // Aguardar um momento para permitir que o frontend gere novo token
            try {
                Thread.sleep(1000); // 1 segundo
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            String newToken = getRecaptchaToken();
            if (newToken != null && !newToken.equals(currentToken)) {
                result = ReCaptchaV3Service.validate(newToken, expectedAction, minScore);
            }
        }

        if (result.isSuccess()) {
            lastValidatedToken = getRecaptchaToken();
            lastValidationTime = currentTime;
        }

        return result;
    }

    /**
     * Verifica se o erro é relacionado a token expirado/inválido
     */
    private boolean isTokenExpiredError(ReCaptchaV3Service.ValidationResult result) {
        if (result.getErrorCodes() != null) {
            for (String errorCode : result.getErrorCodes()) {
                if ("timeout-or-duplicate".equals(errorCode) ||
                        "invalid-input-response".equals(errorCode) ||
                        "missing-input-response".equals(errorCode)) {
                    return true;
                }
            }
        }
        return result.getScore() <= 0.1;
    }

    public void clearValidationCache() {
        lastValidatedToken = null;
        lastValidationTime = 0;
    }

    public void validateOrThrow() throws RuntimeException {
        validateOrThrow(this.action, this.minScore);
    }

    public void validateOrThrow(String expectedAction, double minScore) throws RuntimeException {
        if (!enabled) {
            return; // Se desabilitado, não validar
        }

        // Tentar validação com renovação automática
        ReCaptchaV3Service.ValidationResult result = validateWithAutoRenewal(expectedAction, minScore);

        if (!result.isSuccess()) {
            String errorMsg = String.format("Falha na verificação de segurança após tentativas de renovação (score: %.2f)", result.getScore());
            throw new RuntimeException(errorMsg);
        }
    }

    private ReCaptchaV3Service.ValidationResult validateWithAutoRenewal(String expectedAction, double minScore) {
        if (!isCompleted()) {
            ReCaptchaV3Service.ValidationResult failResult = new ReCaptchaV3Service.ValidationResult();
            failResult.setSuccess(false);
            failResult.setScore(0.0);
            failResult.setErrorCodes(new String[]{"missing-input-response"});
            return failResult;
        }

        ReCaptchaV3Service.ValidationResult result = validate(expectedAction, minScore);

        // Se falhou por token expirado, aguardar renovação automática
        if (!result.isSuccess() && isTokenExpiredError(result)) {

            // Aguardar renovação automática (JavaScript deve renovar em até 2 segundos)
            waitForTokenRenewal(3000);

            // Tentar novamente
            if (isCompleted()) {
                result = validate(expectedAction, minScore);
            }
        }

        return result;
    }

    private void waitForTokenRenewal(long maxWaitMs) {
        String originalToken = getRecaptchaToken();
        long startTime = System.currentTimeMillis();

        while ((System.currentTimeMillis() - startTime) < maxWaitMs) {
            try {
                Thread.sleep(500); // Aguardar 500ms entre verificações

                String currentToken = getRecaptchaToken();
                if (currentToken != null && !currentToken.equals(originalToken)) {
                    break;
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }


    private String escapeHtml(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#x27;");
    }

    private String escapeJavaScript(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\\", "\\\\")
                .replace("'", "\\'")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }

    @Override
    public boolean isEnabled() {
        return enabled && super.isEnabled();
    }

    public void setReCaptchaEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getSiteKey() {
        return siteKey;
    }

    public void setSiteKey(String siteKey) {
        this.siteKey = siteKey;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public double getMinScore() {
        return minScore;
    }

    public void setMinScore(double minScore) {
        this.minScore = minScore;
    }
}
