package br.com.celk.component.consulta;

import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.template.cadastro.interfaces.ICadastroListener;
import br.com.celk.view.prontuario.sintomafatorrisco.AutoCompleteConsultaSintomaFatorRisco;
import br.com.celk.view.prontuario.sintomafatorrisco.DlgCadastroSintomaFatorRisco;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SintomaFatorRisco;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponentPanel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public class PnlSintomaFatorRisco extends FormComponentPanel<SintomaFatorRisco> {

    private DlgCadastroSintomaFatorRisco dlgCadastroSintomaFatorRisco;
    private AutoCompleteConsultaSintomaFatorRisco autoCompleteConsultaSintomaFatorRisco;
    private AbstractAjaxLink btnCadastrarSintomaFatorRisco;

    private SintomaFatorRisco sintomaFatorRisco;

    public PnlSintomaFatorRisco(String id) {
        super(id);
        init();
    }

    public PnlSintomaFatorRisco(String id, IModel model) {
        super(id, model);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        add(autoCompleteConsultaSintomaFatorRisco = (AutoCompleteConsultaSintomaFatorRisco) new AutoCompleteConsultaSintomaFatorRisco("sintomaFatorRisco", new PropertyModel<SintomaFatorRisco>(this, "sintomaFatorRisco")));

        add(btnCadastrarSintomaFatorRisco = new AbstractAjaxLink("btnCadastrarSintomaFatorRisco") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                cadastrarSintomaFatorRisco(target);
            }
        });
    }

    private void cadastrarSintomaFatorRisco(AjaxRequestTarget target) {
        if (dlgCadastroSintomaFatorRisco == null) {
            WindowUtil.addModal(target, getForm(), dlgCadastroSintomaFatorRisco = new DlgCadastroSintomaFatorRisco(WindowUtil.newModalId(this)));
            dlgCadastroSintomaFatorRisco.add(new ICadastroListener<SintomaFatorRisco>() {
                @Override
                public void onSalvar(AjaxRequestTarget target, SintomaFatorRisco sintomaFatorRisco) throws ValidacaoException, DAOException {
                    autoCompleteConsultaSintomaFatorRisco.limpar(target);
                    autoCompleteConsultaSintomaFatorRisco.setComponentValue(sintomaFatorRisco);
                    target.add(autoCompleteConsultaSintomaFatorRisco);
                }
            });
        }
        dlgCadastroSintomaFatorRisco.limpar(target);
        dlgCadastroSintomaFatorRisco.show(target);
    }

    @Override
    protected void convertInput() {
        setConvertedInput(sintomaFatorRisco);
    }

    @Override
    protected void onBeforeRender() {
        Object modelObject = getModelObject();
        if (modelObject == null) {
            modelObject = getConvertedInput();
        }
        autoCompleteConsultaSintomaFatorRisco.setModelObject(modelObject);
        super.onBeforeRender();
    }
    
    public void limpar(AjaxRequestTarget target){
        autoCompleteConsultaSintomaFatorRisco.limpar(target);
        target.add(autoCompleteConsultaSintomaFatorRisco);
    }
    
    public void addRequired(){
        autoCompleteConsultaSintomaFatorRisco.setRequired(true);
        autoCompleteConsultaSintomaFatorRisco.getTxtDescricao().addRequiredClass();
    }
    
    public void removeRequired(){
        autoCompleteConsultaSintomaFatorRisco.setRequired(false);
        autoCompleteConsultaSintomaFatorRisco.getTxtDescricao().removeRequiredClass();
    }

    public AutoCompleteConsultaSintomaFatorRisco getAutoCompleteConsultaSintomaFatorRisco() {
        return autoCompleteConsultaSintomaFatorRisco;
    }

    public AbstractAjaxLink getBtnCadastrarSintomaFatorRisco() {
        return btnCadastrarSintomaFatorRisco;
    }
}
