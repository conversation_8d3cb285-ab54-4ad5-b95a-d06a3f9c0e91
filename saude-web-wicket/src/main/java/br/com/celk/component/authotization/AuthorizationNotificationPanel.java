package br.com.celk.component.authotization;

import br.com.celk.system.authorization.interfaces.IAuthorizationPanel;
import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public class AuthorizationNotificationPanel extends Panel implements IAuthorizationPanel{ 

    private static final String AUTH_PANEL_ID = "usuarioAutorizado";
    
    public AuthorizationNotificationPanel(String id) {
        super(id);
        setOutputMarkupId(true);
        AbstractSessaoAplicacao authorizedSession = ApplicationSession.get().getAuthorizedSession();
        if (authorizedSession!=null) {
            onAuthorize(null);
        } else {
            onUnauthorize(null);
        }
    }
    
//    @Subscribe(contextAwareFilter=AuthorizationUserFilter.class)
//    public void onMessage(AjaxRequestTarget target, AuthorizationMessage event){
//        if (event.getType().equals(AuthorizationMessage.Type.AUTHORIZATION)) {
//            if (ApplicationSession.get().getAuthorizedSession()!=null) {
//                onAuthorize(target);
//            }
//        } else {
//            onUnauthorize(target);
//        }
//    }
    
    @Override
    public void onAuthorize(AjaxRequestTarget target){
        Panel panel = new AuthorizedUserPanel(AUTH_PANEL_ID);
        Component authPanel = setAuthPanel(panel);
        if (target!=null) {
            target.add(authPanel);
        }
    }
    
    @Override
    public void onUnauthorize(AjaxRequestTarget target){
        EmptyPanel emptyPanel = new EmptyPanel(AUTH_PANEL_ID);
        Component authPanel = setAuthPanel(emptyPanel);
        if (target!=null) {
            target.add(authPanel);
        }
    }

    private Component setAuthPanel(Component component){
        Component existing = get(AUTH_PANEL_ID);
        component.setOutputMarkupId(true);
        if (existing!=null) {
            return existing.replaceWith(component);
        } else {
            return add(component);
        }
    }
    
}
