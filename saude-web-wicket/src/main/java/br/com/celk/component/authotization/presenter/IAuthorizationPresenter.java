package br.com.celk.component.authotization.presenter;

import br.com.celk.component.authotization.view.IAuthorizationView;
import br.com.celk.system.authorization.interfaces.action.IAuthorizationAction;
import br.com.celk.system.authorization.interfaces.action.IAuthorizationClose;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.io.Serializable;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
public interface IAuthorizationPresenter extends Serializable{

    public void autorizar(AjaxRequestTarget target, String identificador) throws ValidacaoException, DAOException;
    
    public void fechar(AjaxRequestTarget target);
    
    public void setAuthorizationAction(IAuthorizationAction... actions);
    
    public void setCloseAction(IAuthorizationClose onClose);
    
    public void setView(IAuthorizationView view);

    public void setComponentRequestFocus(FormComponent component);
    
    public FormComponent getComponentRequestFocus();
    
}
