package br.com.celk.component.biometria;

import br.com.celk.component.appletbiometria.*;
import br.com.celk.component.ajaxcalllistener.DefaultListenerLoading;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AbstractDefaultAjaxBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.html.IHeaderContributor;
import org.apache.wicket.markup.html.WebComponent;
import org.apache.wicket.request.cycle.RequestCycle;

/**
 * <AUTHOR>
 * <PERSON> em: Jan 2, 2014
 */
public class Biometria extends WebComponent implements IHeaderContributor {

    private static final long serialVersionUID = 1L;

    private IAppletAction listener = null;
    private AbstractDefaultAjaxBehavior behavior;

    public Biometria(String id) {
        super(id);
        
        try {
            init();
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void init() throws DAOException{
        setOutputMarkupId(true);
        
        behavior = new AbstractDefaultAjaxBehavior() {
            @Override
            protected void respond(AjaxRequestTarget target) {
                if(listener != null){
                    String methodType = RequestCycle.get().getRequest().getRequestParameters().getParameterValue("methodType").toString();
                    if("register".equals(methodType)){
                        listener.register(target, RequestCycle.get().getRequest().getRequestParameters().getParameterValue("key").toString());
                    }else if("search".equals(methodType)){
                        listener.search(target, RequestCycle.get().getRequest().getRequestParameters().getParameterValue("key").toString());
                    }
                }
            }

            @Override
            public void renderHead(Component component, IHeaderResponse response) {
                super.renderHead(component, response);
                String script = "var param1Value = appletParameter;";
                script += getCallbackScript();
                
               
                response.render(JavaScriptHeaderItem.forScript("function validarDigital(digitalRecuperada){"+
//                        "    appletSearchListener('ok'); return;"+ // apenas para teste
                        
                        "    $.ajax({\n" +
                        "        url: 'http://localhost:9000/api/public/v1/captura/Comparar?Digital=' + digitalRecuperada,\n" +
                        "        type: 'GET',\n" +
                        "        success: function (data) {\n" +
                        "           appletSearchListener(data);"+
                        "        }\n" +
                        "    })"                        
                        + "}", "validarDigital"));
                
                response.render(JavaScriptHeaderItem.forScript("function cadastrarDigital(){"+
//                        "           appletRegisterListener('myKey'); return;" +//apenas para teste
                        
                        "$.ajax({\n" +
                        "    url: 'http://localhost:9000/api/public/v1/captura/Enroll/1',\n" +
                        "    type: 'GET',\n" +
                        "    success: function (data) {\n" +
                        "       if (data != '') {\n" +
                        "           appletRegisterListener(data);\n" +
                        "       }\n" +
                        "    }\n" +
                        "})"
                        + "}", "cadastrarDigital"));
                
                response.render(JavaScriptHeaderItem.forScript("function appletSearchListener(appletParameter){"
                        + "var param2Value = 'search';"
                            + script
                        + "}", "appletSearch"));
                
                response.render(JavaScriptHeaderItem.forScript("function appletRegisterListener(appletParameter){"
                        + "var param2Value = 'register';"
                            + script
                        + "}", "appletRegister"));
                
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                attributes.getExtraParameters().put("key", "PLACEHOLDER1");
                attributes.getExtraParameters().put("methodType", "PLACEHOLDER2");
                attributes.getAjaxCallListeners().add(new DefaultListenerLoading());
            }
            
            @Override
            public CharSequence getCallbackScript() {
              String script = super.getCallbackScript().toString();
              script = script.replace("\"PLACEHOLDER1\"", "param1Value");
              script = script.replace("\"PLACEHOLDER2\"", "param2Value");
              return script;
            }            
            
        };
        add(behavior);        
    }

    public void setListener(IAppletAction action){
        listener = action;
    }
}
