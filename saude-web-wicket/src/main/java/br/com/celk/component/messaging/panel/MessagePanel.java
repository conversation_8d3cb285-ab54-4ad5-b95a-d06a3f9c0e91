package br.com.celk.component.messaging.panel;

import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.view.comunicacao.mensagem.CaixaMensagensPage;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.Mensagem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public class MessagePanel extends Panel implements IMessagePanel{

    public MessagePanel(String id, QueryConsultaMensagensDTO dto) {
        super(id);
        setDefaultModel(new Model<QueryConsultaMensagensDTO>(dto));
        init();
    }
    
    private void init(){
        setOutputMarkupId(true);
        
        AbstractAjaxLink linkVisualizarMensagem;
        add(linkVisualizarMensagem = new AbstractAjaxLink("linkVisualizarMensagem") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new CaixaMensagensPage(MessagePanel.this.getModel().getObject()));
            }
        });
        
        linkVisualizarMensagem.add(new Label("nomeUsuario", getModel().getObject().getMensagem().getMensagemOrigem().getUsuario().getNome()));
        
        linkVisualizarMensagem.add(new Label("data", Data.formatarDataHora(getModel().getObject().getMensagem().getData())));
        
        linkVisualizarMensagem.add(new Label("conteudo", getModel().getObject().getMensagem().getAssunto()));
    }

    @Override
    public void notifyMessage(AjaxRequestTarget target, Mensagem mensagem) {
    }

    private Model<QueryConsultaMensagensDTO> getModel(){
        return (Model<QueryConsultaMensagensDTO>) getDefaultModel();
    }
    
}
