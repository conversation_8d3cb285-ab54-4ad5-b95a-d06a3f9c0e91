package br.com.celk.component.authotization;

import br.com.celk.component.authotization.presenter.IAuthorizationPresenter;
import br.com.celk.component.authotization.view.IAuthorizationView;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.panel.ViewPanel;
import br.com.celk.component.passwordfield.PasswordField;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public class PnlAuthorization extends ViewPanel implements IAuthorizationView{

    private IModel<String> modelIdentificador;
    private PasswordField txtPassword;
    
    private IAuthorizationPresenter authorizationPresenter;
    
    public PnlAuthorization(String id, IAuthorizationPresenter authorizationPresenter) {
        super(id);
        this.authorizationPresenter = authorizationPresenter;
        init();
    }

    private void init() {
        Form form = new Form("form");
        form.add(txtPassword = new PasswordField("password", modelIdentificador = new Model<String>()));
        form.add(new AbstractAjaxButton("btnAutorizar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                authorizationPresenter.autorizar(target, modelIdentificador.getObject());
            }
        });
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                authorizationPresenter.fechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
        
        authorizationPresenter.setComponentRequestFocus(txtPassword);
    }
    
    @Override
    public void limpar(AjaxRequestTarget target){
        txtPassword.limpar(target);
    }

}
