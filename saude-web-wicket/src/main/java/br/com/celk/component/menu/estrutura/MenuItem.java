package br.com.celk.component.menu.estrutura;

import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class MenuItem implements Serializable {

    private String descricao;
    private String className;
    private MenuWeb menuWeb;
    private List<MenuItem> childs;

    public MenuItem() {
    }

    public MenuItem(String descricao, String className) {
        this.descricao = descricao;
        this.className = className;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String url) {
        this.className = url;
    }

    public MenuWeb getMenuWeb() {
        return menuWeb;
    }

    public void setMenuWeb(MenuWeb menuWeb) {
        this.menuWeb = menuWeb;
    }

    public List<MenuItem> getChilds() {
        return childs;
    }

    public void setChilds(List<MenuItem> childs) {
        this.childs = childs;
    }

}
