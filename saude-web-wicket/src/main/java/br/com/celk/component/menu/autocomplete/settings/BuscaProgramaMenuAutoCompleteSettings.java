package br.com.celk.component.menu.autocomplete.settings;

import br.com.celk.component.consulta.configurator.autocomplete.AbstractAutoCompleteSettings;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class BuscaProgramaMenuAutoCompleteSettings extends AbstractAutoCompleteSettings {

    @Override
    public Map<String, String> getJsonPropertyMap(PesquisaObjectInterface o) {
        Map<String, String> propertiesMap = new HashMap<String, String>();
        
        propertiesMap.put("id", o.getIdentificador());
        propertiesMap.put("name", o.getDescricaoVO());
        
        if (o instanceof ProgramaWeb) {
            ProgramaWeb bean = (ProgramaWeb)o;
            propertiesMap.put("caminho", bean.getDescricaoCaminhoMenu());
        }
        
        return propertiesMap;
    }

    @Override
    public String getResultsFormatter() {
        StringBuilder builder = new StringBuilder();
        builder.append("<li>");
            builder.append("<div style=\"display: inline-block; padding-left: 10px;\">");
                builder.append("<div class=\"nivel-1\"> '+item.name+' </div>");
                builder.append("<div class=\"nivel-2\" > <span style=\"padding-left: 10px; \" >'+item.caminho+' </span> </div>");
            builder.append("</div>");
        builder.append("</li>");
        
        return builder.toString();
    }
    
}
