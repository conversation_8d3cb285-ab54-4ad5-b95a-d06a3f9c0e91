package br.com.celk.component.appletetiqueta;

import br.com.celk.component.appletbiometria.*;
import br.com.celk.resources.Resources;
import br.com.celk.system.Application;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import com.google.gson.Gson;
import java.io.IOException;
import java.text.Normalizer;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.jar.Manifest;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.MarkupStream;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.html.IHeaderContributor;
import org.apache.wicket.markup.html.WebComponent;
import org.apache.wicket.protocol.http.servlet.ServletWebRequest;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.util.value.IValueMap;
import org.apache.wicket.util.value.ValueMap;

/**
 * <AUTHOR>
 */
public class AppletEtiqueta extends WebComponent implements IHeaderContributor {

    private static final long serialVersionUID = 1L;

    private static final String ATTRIBUTE_WIDTH = "width";
    private static final String ATTRIBUTE_HEIGHT = "height";
    private static final String ATTRIBUTE_CODE = "code";
    private static final String ATTRIBUTE_CODEBASE = "codebase";
    private static final String ATTRIBUTE_ID = "id";
    private static final String ATTRIBUTE_ARCHIVE = "archive";

    private String minimalVersion;
    private IValueMap appletAttributes = new ValueMap();
    private IValueMap appletParameters = new ValueMap();
    private HashMap<Object, IAppletAction> appletListener = new LinkedHashMap<Object, IAppletAction>();

    public AppletEtiqueta(String id) {
        super(id);

        try {
            init();
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void configuracoes() {
        String protocol = ((ServletWebRequest) RequestCycle.get().getRequest()).getContainerRequest().getRequestURL().toString();
        protocol = protocol.substring(0, protocol.indexOf(":"));
        String host = ((ServletWebRequest) RequestCycle.get().getRequest()).getContainerRequest().getServerName();
        int port = ((ServletWebRequest) RequestCycle.get().getRequest()).getContainerRequest().getServerPort();

        try {
            Manifest manifest = new Manifest(Application.get().getServletContext().getResourceAsStream("/META-INF/MANIFEST.MF"));
            String version = manifest.getMainAttributes().getValue("Gem-Version");

            setArchive("applet-etiqueta-" + version + ".jar");
        } catch (IOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        setId("etqApplet");
        setCodebase(protocol + "://" + host + ":" + port + "/applets");
        setCode("qz.PrintApplet.class");
        setWidth(1);
        setHeight(1);
        setMinimalVersion("1.6");

//<editor-fold defaultstate="collapsed" desc="JNLP">
//        criar template do jnlp para ver se o java libera a execução da aplicação, hoje está bloqueando por questão de segurança
//        addParameter("jnlp_href","biometria_applet.jnlp");
//        addParameter("jnlp_embedded","PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVV"
//                + "RGLTgiPz4NCjxqbmxwIGhyZWY9ImJpb21ldHJpYV9hcHBsZXQuam5scCI+DQo"
//                + "gICAgPGluZm9ybWF0aW9uPg0KICAgICAgICA8dGl0bGU+QmlvbWV0cmlhIENF"
//                + "TEs8L3RpdGxlPg0KICAgICAgICA8dmVuZG9yPkNFTEsgU2lzdGVtYXM8L3Zlb"
//                + "mRvcj4NCiAgICA8L2luZm9ybWF0aW9uPg0KICAgIDxyZXNvdXJjZXM+DQogIC"
//                + "AgICAgIDxqMnNlIHZlcnNpb249IjEuNysiIC8+DQogICAgICAgIDxqYXIgaHJ"
//                + "lZj0iYmlvbWV0cmlhL2FwcGxldC1iaW9tZXRyaWEtMy4wLjExLVNOQVBTSE9U"
//                + "LmphciIgbWFpbj0idHJ1ZSIgLz4NCiAgICA8L3Jlc291cmNlcz4NCiAgICA8Y"
//                + "XBwbGV0LWRlc2MgDQogICAgICAgICBuYW1lPSJCaW9tZXRyaWEgQ0VMSyINCi"
//                + "AgICAgICAgIG1haW4tY2xhc3M9ImJyLmNvbS5jZWxrLmFwcGxldC5iaW9tZXRy"
//                + "aWEuQXBwbGV0TWFpbiINCiAgICAgICAgIHdpZHRoPSIzMDAiDQogICAgICAgI"
//                + "CBoZWlnaHQ9IjMwMCI+DQogICAgIDwvYXBwbGV0LWRlc2M+DQogICAgIDx1cG"
//                + "RhdGUgY2hlY2s9ImJhY2tncm91bmQiLz4NCjwvam5scD4=");
//</editor-fold>
    }

    private void init() throws DAOException {
        configuracoes();
        setOutputMarkupId(true);
    }

    public void addActionListener(Object key, IAppletAction action) {
        appletListener.put(key, action);
    }

    /**
     * Add a parameter to the applet.
     *
     * @param key Name of the parameter.
     * @param value Value for the parameter.
     */
    public void addParameter(final String key, final Object value) {
        appletParameters.put(key, value);
    }

    /**
     * Get the applet attributes already set and assign them to the attribute
     * list for the Javascript code. And we change the tag name to "script".
     *
     * @param tag De current tag which is replaced.
     */
    @Override
    protected void onComponentTag(ComponentTag tag) {
        super.onComponentTag(tag);
        if ("applet".equalsIgnoreCase(tag.getName())) {
            final IValueMap tagAttributes = tag.getAttributes();
            final String wicketId = tagAttributes.getString("wicket:id");
            appletAttributes.putAll(tagAttributes);
            tagAttributes.clear();
            tagAttributes.put("wicket:id", wicketId);
        }
        tag.setName("script");
    }

    /**
     * Create Javascript for deployJava.runApplet.
     *
     * @param markupStream MarkupStream to be replaced.
     * @param openTag Tag we are replacing.
     */
    @Override
    public void onComponentTagBody(MarkupStream markupStream, ComponentTag openTag) {
        final StringBuilder script = new StringBuilder();
        if (appletAttributes.size() > 0) {
            String jsonAttributes = new Gson().toJson(appletAttributes);
            script.append("var attributes = ").append(jsonAttributes).append(";");
        } else {
            script.append("var attributes = {};");
        }
        if (appletParameters.size() > 0) {
            //            final JSONObject jsonParameters = JsonObject.fromObject(appletParameters);
            String jsonParameters = new Gson().toJson(appletParameters);
            script.append("var parameters = ").append(jsonParameters).append(";");
        } else {
            script.append("var parameters = {};");
        }
        if (minimalVersion != null) {
            script.append("var version = \"").append(minimalVersion).append("\";");
        } else {
            script.append("var version = null;");
        }
        script.append("if(deployJava.getJREs() == ''){alert('").append(Bundle.getStringApplication("msg_necessario_java_instalado_redirecionado_pagina")).append("')}");
        script.append("deployJava.runApplet(attributes, parameters, version);");
        replaceComponentTagBody(markupStream, openTag, script.toString());
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DEPLOY_JAVA));
        response.render(JavaScriptHeaderItem.forScript(configuraImpressora(), "configuraImpressora"));
    }

    private String configuraImpressora() {
        return "function qzReady() {\n"
                + "window[\"qz\"] = document.getElementById('etqApplet');\n"
                + "qz.findPrinter();\n"
                + "window['qzDoneFinding'] = function() {\n"
                + "var p = document.getElementById('printer');\n"
                + "var printer = qz.getPrinter();\n"
                + "// Remove reference to this function\n"
                + "window['qzDoneFinding'] = null;\n"
                + "};\n"
                + "}\n";
    }

    public void imprimir(AjaxRequestTarget target, String texto) {
        target.appendJavaScript("qz.append('" + texto + "');\n"
                + "qz.print();");
    }

    /**
     * 3,3 x 1,8 cm
     */
    public void imprimirCodigoZPL(AjaxRequestTarget target, String nomeProduto, String lote, Date validade, String referenciaProduto, Long quantidadeEtiquetas, List<Long> lstCodigosBarra) {
        nomeProduto = removeAcentos(nomeProduto);
        String texto;
        Integer quantidadeFaltante = quantidadeEtiquetas.intValue();
        Integer quantidadeX = quantidadeEtiquetas.intValue() / 3; //quantidade de interações no eixo X, contendo cada linha 3 etiquetas
        /**
         * no modelo escolhido (linguagem ZPL) as margens são: FT6 FT285 FT564 a
         * margem escolhida para o codigo de barras acresce 32 desse valor
         */
        Integer[] margens = {6, 285, 564};
        Integer indexCodBarras = 0;

        texto = "CT~~CD,~CC^~CT~"
                + "^XA~TA000~JSN^LT0^MNW^MTD^PON^PMN^LH0,0^JMA^PR4,4~SD15^JUS^LRN^CI0^XZ";

        for (int x = 0; x <= quantidadeX; x++) {
            //indica uma nova cadeia de 3 etiquetas
            texto += "^XA"
                    + "^MMT"
                    + "^PW831"
                    + "^LL0152"
                    + "^LS0";
            for (int y = 0; y < 3; y++) {
                if (quantidadeFaltante > 0) {
                    texto += "^BY2,3,52^FT" + (margens[y] + 32) + ",116^BCN,,Y,N"
                            + "^FD>;" + String.format("%010d", lstCodigosBarra.get(indexCodBarras)) + "^FS"
                            + "^FT" + margens[y] + ",18^A0N,17,16^FH\\^FD" + limitaString(nomeProduto, 30) + " ^FS"
                            + "^FT" + margens[y] + ",37^A0N,17,16^FH\\^FD" + limitaStringSegundaLinha(nomeProduto, 60) + "^FS"
                            + "^FT" + margens[y] + ",57^A0N,14,14^FH\\^FD" + (lote.equals("0") ? "" : ("L:" + limitaString(lote, 11))) + "^FS"
                            + "^FT" + (margens[y] + 100) + ",57^A0N,14,14^FH\\^FD" + Data.formatar(validade) + "^FS"
                            + "^FT" + (margens[y] + 170) + ",57^A0N,14,14^FH\\^FD" + (referenciaProduto == null ? "" : ("C:" + limitaString(referenciaProduto, 8))) + "^FS";
                    quantidadeFaltante--;
                    indexCodBarras++;
                } else {
                    break;
                }
            }
            //indica o fim da cadeia de 3 etiquetas
            texto += "^PQ1,0,1,Y^XZ";
        }

        imprimir(target, texto);
    }

    /**
     * tam 3x1,5cm
     */
    public void imprimirCodigoZPLEtqMenor(AjaxRequestTarget target, String nomeProduto, String lote, Date validade, String referenciaProduto, Long quantidadeEtiquetas, List<Long> lstCodigosBarra) {
        nomeProduto = removeAcentos(nomeProduto);
        String texto;
        Integer quantidadeFaltante = quantidadeEtiquetas.intValue();
        Integer quantidadeX = quantidadeEtiquetas.intValue() / 3; //quantidade de interações no eixo X, contendo cada linha 3 etiquetas
        /**
         * no modelo escolhido (linguagem ZPL) as margens são: FT6 FT285 FT564 a
         * margem escolhida para o codigo de barras acresce 32 desse valor
         */
        Integer[] margens = {6, 285, 564};
        Integer indexCodBarras = 0;

        texto = "^XA~TA000~JSN^LT0^MNW^MTD^PON^PMN^LH0,0^JMA^PR4,4~SD15^JUS^LRN^CI0^XZ";

        for (int x = 0; x <= quantidadeX; x++) {
            //indica uma nova cadeia de 3 etiquetas
            texto += "^XA"
                    + "^MMT"
                    + "^PW831"
                    + "^LL0152"
                    + "^LS0";
            int corretorDistanciaEntreEtiquetas = 30;
            for (int y = 0; y < 3; y++) {
                if (quantidadeFaltante > 0) {
                    texto += "^BY2,3,52^FT" + (margens[y] + 32 + corretorDistanciaEntreEtiquetas) + ",107^BCN,,Y,N"
                            + "^FD>;" + String.format("%010d", lstCodigosBarra.get(indexCodBarras)) + "^FS"
                            + "^FT" + (margens[y] + corretorDistanciaEntreEtiquetas) + ",37^A0N,17,16^FH\\^FD" + limitaString(nomeProduto, 25) + " ^FS"
                            + "^FT" + (margens[y] + corretorDistanciaEntreEtiquetas) + ",53^A0N,14,14^FH\\^FD" + (lote.equals("0") ? "" : ("L:" + limitaString(lote, 11))) + "^FS"
                            + "^FT" + (margens[y] + 100 + corretorDistanciaEntreEtiquetas) + ",53^A0N,14,14^FH\\^FD" + Data.formatar(validade) + "^FS"
                            + "^FT" + (margens[y] + 170 + corretorDistanciaEntreEtiquetas) + ",53^A0N,14,14^FH\\^FD" + (referenciaProduto == null ? "" : ("C:" + limitaString(referenciaProduto, 8))) + "^FS";
                    quantidadeFaltante--;
                    corretorDistanciaEntreEtiquetas = corretorDistanciaEntreEtiquetas - 15;
                    indexCodBarras++;
                } else {
                    break;
                }
            }
            //indica o fim da cadeia de 3 etiquetas
            texto += "^PQ1,0,1,Y^XZ";
        }

        imprimir(target, texto);
    }

    public String limitaString(String str, int tamanho) {
        if (str != null && str.length() > tamanho) {
            return str.substring(0, tamanho + 1);
        } else {
            return str;
        }
    }

    public String limitaStringSegundaLinha(String str, int tamanho) {
        if (str != null && str.length() > tamanho) {
            return str.substring(31, tamanho + 1);
        } else if (str.length() > 30) {
            return str.substring(31, str.length());
        } else {
            return "";
        }
    }

    public String removeAcentos(String str) {

        str = Normalizer.normalize(str, Normalizer.Form.NFD);
        str = str.replaceAll("[^\\p{ASCII}]", "");
        return str;

    }

    public void setMinimalVersion(final String version) {
        this.minimalVersion = version;
    }

    public void setWidth(final Integer width) {
        appletAttributes.put(ATTRIBUTE_WIDTH, width);
    }

    public void setHeight(final Integer height) {
        appletAttributes.put(ATTRIBUTE_HEIGHT, height);
    }

    public void setCode(final String code) {
        appletAttributes.put(ATTRIBUTE_CODE, code);
    }

    public void setCodebase(final String codebase) {
        appletAttributes.put(ATTRIBUTE_CODEBASE, codebase);
    }

    public void setId(final String id) {
        appletAttributes.put(ATTRIBUTE_ID, id);
    }

    public void setArchive(final String archive) {
        appletAttributes.put(ATTRIBUTE_ARCHIVE, archive);
    }

}
