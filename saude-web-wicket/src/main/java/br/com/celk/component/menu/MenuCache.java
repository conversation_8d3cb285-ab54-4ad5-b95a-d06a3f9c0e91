package br.com.celk.component.menu;

import br.com.celk.singleton.LocalCacheCelkSingleton;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.ControlePermissaoGrupoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.MenuAtualizacao;
import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import br.com.ksisolucoes.vo.controle.web.PainelControleProgramaWeb;
import br.com.ksisolucoes.vo.controle.web.ProgramaPagina;
import br.com.ksisolucoes.vo.controle.web.ProgramaPaginaPermissao;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import ch.lambdaj.Lambda;
import java.io.Serializable;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.naming.NamingException;
import org.apache.wicket.request.cycle.RequestCycle;
import org.hamcrest.Matchers;

/**
 *
 * <AUTHOR>
 */
public class MenuCache implements Serializable {

    private Map<String, List<PainelControleProgramaWeb>> painelControleProgramaMap;
    private Map<MenuWeb, List<MenuWeb>> paiMenuMap;
    private Map<ProgramaWeb, List<ProgramaPaginaPermissao>> permissoesMap;
    private List<MenuWeb> modulos;
    private Date ultimaAtualizacao;

    public static MenuCache get() {
        try {
            LocalCacheCelkSingleton proxy = LocalCacheCelkSingleton.lookup();
            String host = RequestCycle.get().getRequest().getClientUrl().getHost();
            MenuCache mc = (MenuCache) proxy.getCache(host);
            if(mc == null){
                proxy.loadCache(host, new MenuCache());
                mc = (MenuCache) proxy.getCache(host);
            }
            
            MenuAtualizacao ma = LoadManager.getInstance(MenuAtualizacao.class)
                .start().getVO();
            if(ma == null || mc.ultimaAtualizacao == null || ma.getDataAlteracao().after(mc.ultimaAtualizacao)){
                mc.recarregarMenus();
            }
            
            return mc;
        } catch (NamingException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return null;
    }

    public List<MenuWeb> getChilds(MenuWeb menuWeb) {
        if (paiMenuMap == null) {
            synchronized (this) {
                if (paiMenuMap == null) {
                    loadMenus();
                }
            }
        }
        return paiMenuMap.get(menuWeb);
    }

    public List<MenuWeb> getModulos() {
        if (modulos == null) {
            synchronized (this) {
                if (modulos == null) {
                    loadMenus();
                }
            }
        }
        return modulos;
    }

    private void loadMenus() {
        HashMap<MenuWeb, List<MenuWeb>> _paiMenuMap = new HashMap<MenuWeb, List<MenuWeb>>();
        ArrayList<MenuWeb> _modulos = new ArrayList<MenuWeb>();
        List<MenuWeb> menuItensPadrao = null;

        Long layoutMenu = null;

        try {
            layoutMenu = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("layoutMenu");
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        LoadManager loadManager = LoadManager.getInstance(MenuWeb.class)
                .addProperties(new HQLProperties(MenuWeb.class).getProperties())
                .addProperties(new HQLProperties(ProgramaWeb.class, MenuWeb.PROP_PROGRAMA_WEB).getProperties())
                .addProperties(new HQLProperties(ProgramaPagina.class, VOUtils.montarPath(MenuWeb.PROP_PROGRAMA_WEB, ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL)).getProperties())
                .addProperties(new HQLProperties(MenuWeb.class, MenuWeb.PROP_MENU_MODULO).getProperties())
                .addProperties(new HQLProperties(MenuWeb.class, MenuWeb.PROP_MENU_WEB_PAI).getProperties())
                .addProperties(new HQLProperties(MenuWeb.class, VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI)).getProperties())
                .addProperties(new HQLProperties(MenuWeb.class, VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(MenuWeb.PROP_LAYOUT_MENU, MenuWeb.LayoutMenu.PADRAO.value()))
                .addSorter(new QueryCustom.QueryCustomSorter(MenuWeb.PROP_DESCRICAO));

        menuItensPadrao = loadManager.start().getList();

        List<MenuWeb> menuItensLayout = new ArrayList<MenuWeb>();

        if (layoutMenu != null && !MenuWeb.LayoutMenu.PADRAO.value().equals(layoutMenu)) {
            loadManager = LoadManager.getInstance(MenuWeb.class)
                    .addProperties(new HQLProperties(MenuWeb.class).getProperties())
                    .addProperties(new HQLProperties(ProgramaWeb.class, MenuWeb.PROP_PROGRAMA_WEB).getProperties())
                    .addProperties(new HQLProperties(ProgramaPagina.class, VOUtils.montarPath(MenuWeb.PROP_PROGRAMA_WEB, ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL)).getProperties())
                    .addProperties(new HQLProperties(MenuWeb.class, MenuWeb.PROP_MENU_MODULO).getProperties())
                    .addProperties(new HQLProperties(MenuWeb.class, MenuWeb.PROP_MENU_WEB_PAI).getProperties())
                    .addProperties(new HQLProperties(MenuWeb.class, VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI)).getProperties())
                    .addProperties(new HQLProperties(MenuWeb.class, VOUtils.montarPath(MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI, MenuWeb.PROP_MENU_WEB_PAI)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(MenuWeb.PROP_LAYOUT_MENU, layoutMenu))
                    .addSorter(new QueryCustom.QueryCustomSorter(MenuWeb.PROP_DESCRICAO));

            menuItensLayout = loadManager.start().getList();
        }

        List<MenuWeb> menuItens = new ArrayList<MenuWeb>();

        if (CollectionUtils.isNotNullEmpty(menuItensLayout)) {
            menuItens.addAll(menuItensLayout);

            List itensPadrao = Lambda.filter(
                    Lambda.having(
                            Lambda.on(MenuWeb.class).getProgramaWeb().getCodigo(),
                            Matchers.not(
                                    Matchers.isIn(
                                            Lambda.extract(
                                                    menuItensLayout,
                                                    Lambda.on(MenuWeb.class).getProgramaWeb().getCodigo()
                                            )
                                    )
                            )
                    ),
                    menuItensPadrao);

            menuItens.addAll(itensPadrao);
        } else {
            menuItens.addAll(menuItensPadrao);
        }

        for (MenuWeb item : menuItens) {
            try {
                if (item.getProgramaWeb() != null) {
                    Class.forName(item.getProgramaWeb().getProgramaPaginaPrincipal().getCaminhoPagina());
                }
                if (item.getMenuWebPai() == null) {
                    _modulos.add(item);
                } else {
                    List<MenuWeb> l = null;
                    if (!_paiMenuMap.containsKey(item.getMenuWebPai())) {
                        l = new ArrayList<MenuWeb>();
                        _paiMenuMap.put(item.getMenuWebPai(), l);
                    } else {
                        l = _paiMenuMap.get(item.getMenuWebPai());
                    }
                    l.add(item);
                }
            } catch (ClassNotFoundException ex) {
                Loggable.log.error(BundleManager.getString("classeMenuNaoDisponivel") + ": " + item.getProgramaWeb().getProgramaPaginaPrincipal().getCaminhoPagina());
            }
        }

        modulos = _modulos;
        paiMenuMap = _paiMenuMap;
    }

    public List<PainelControleProgramaWeb> getChildsPainelControle(String nivelUsuario) {
        if (painelControleProgramaMap == null) {
            synchronized (this) {
                if (painelControleProgramaMap == null) {
                    loadMenuPainelControle();
                }
            }
        }
        return painelControleProgramaMap.get(nivelUsuario);
    }

    private void loadMenuPainelControle() {
        painelControleProgramaMap = new HashMap<String, List<PainelControleProgramaWeb>>();

        List<PainelControleProgramaWeb> programas = LoadManager.getInstance(PainelControleProgramaWeb.class)
                .addProperty(VOUtils.montarPath(PainelControleProgramaWeb.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(PainelControleProgramaWeb.PROP_DESCRICAO_BUNDLE))
                .addProperty(VOUtils.montarPath(PainelControleProgramaWeb.PROP_NIVEL_USUARIO))
                .addProperty(VOUtils.montarPath(PainelControleProgramaWeb.PROP_PROGRAMA_WEB, ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(PainelControleProgramaWeb.PROP_PROGRAMA_WEB, ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CAMINHO_PAGINA))
                .addProperty(VOUtils.montarPath(PainelControleProgramaWeb.PROP_PROGRAMA_WEB, ProgramaWeb.PROP_ATIVO))
                .addSorter(new QueryCustom.QueryCustomSorter(PainelControleProgramaWeb.PROP_DESCRICAO_BUNDLE))
                .start().getList();

        for (PainelControleProgramaWeb painelControleProgramaWeb : programas) {
            if (!painelControleProgramaMap.containsKey(painelControleProgramaWeb.getNivelUsuario())) {
                painelControleProgramaMap.put(painelControleProgramaWeb.getNivelUsuario(), new LinkedList<PainelControleProgramaWeb>());
            }
            painelControleProgramaMap.get(painelControleProgramaWeb.getNivelUsuario()).add(painelControleProgramaWeb);
        }
    }

    public synchronized void recarregarMenus() {
        MenuAtualizacao ma = LoadManager.getInstance(MenuAtualizacao.class)
            .start().getVO();
        if(ma == null){
            ma = new MenuAtualizacao();
            ma.setCodigo(1L);
            ma.setDataAlteracao(DataUtil.getDataAtual());
            try {
                BOFactoryWicket.save(ma);
            } catch (SGKException ex) {
                Loggable.log.error(ex.getMessage(),ex);
            }
        }

        ultimaAtualizacao = ma.getDataAlteracao();
        
        loadMenus();
        loadMenuPainelControle();
        loadPermissoes();
    }

    public List<ProgramaPaginaPermissao> getPermissoesPrograma(ProgramaWeb programaWeb) {
        if (permissoesMap == null) {
            synchronized (this) {
                if (permissoesMap == null) {
                    loadPermissoes();
                }
            }
        }
        return permissoesMap.get(programaWeb);
    }

    private void loadPermissoes() {
        try {
            permissoesMap = BOFactoryWicket.getBO(ControlePermissaoGrupoFacade.class).getMapPaginaPermissoes();
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
}
