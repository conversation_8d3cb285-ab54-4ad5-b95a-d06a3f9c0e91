package br.com.celk.component.async;

import br.com.celk.component.behavior.DownloadReportBehavior;
import br.com.celk.component.behavior.JGrowlBehavior;
import br.com.celk.component.jgrowl.JGrowl;
import br.com.celk.component.messaging.behavior.VisualizarMensagemBehavior;
import br.com.celk.system.Application;
import br.com.celk.system.asyncprocess.interfaces.IAsyncProcessNotification;
import br.com.celk.system.asyncprocess.interfaces.IAsyncReportNotification;
import br.com.celk.system.asyncprocess.interfaces.IMessagingNotification;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.MarkupContainer;
import org.apache.wicket.ajax.AbstractDefaultAjaxBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.attributes.CallbackParameter;
import org.apache.wicket.feedback.FeedbackMessage;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.panel.Fragment;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.http.WebRequest;
import org.apache.wicket.util.string.StringValue;

import java.io.*;
import java.nio.file.Files;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class AsyncNotificationPanel extends Fragment {

    private final AbstractDefaultAjaxBehavior behavior;

    public AsyncNotificationPanel(String id, String markupId, MarkupContainer markupProvider) {
        super(id, markupId, markupProvider);

        behavior = new AbstractDefaultAjaxBehavior() {
            @Override
            protected void respond(AjaxRequestTarget target) {
                RequestCycle cycle = RequestCycle.get();
                WebRequest webRequest = (WebRequest) cycle.getRequest();
                StringValue asyncId = webRequest.getQueryParameters().getParameterValue("asyncId");

                if ("asy".equals(asyncId.toString().split(":")[0])) {
                    AsyncProcess asyncProcess = LoadManager.getInstance(AsyncProcess.class).setId(Long.valueOf(asyncId.toString().split(":")[1])).start().getVO();
                    if (asyncProcess.getTipo().equals(AsyncProcess.TIPO_PROCESSO)) {
                        notifyProcess(target, asyncProcess);
                    } else if (asyncProcess.getTipo().equals(AsyncProcess.TIPO_RELATORIO)) {
                        notifyReport(target, asyncProcess);
                    }
                } else {
                    try {
                        QueryConsultaMensagensDTO dto = BOFactoryWicket.getBO(ComunicacaoFacade.class).consultarMensagem(Long.valueOf(asyncId.toString().split(":")[1]));

                        notifyMessaging(target, dto);
                    } catch (SGKException ex) {
                        Logger.getLogger(AsyncNotificationPanel.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
            }
        };
        add(behavior);

        setOutputMarkupId(true);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        String urlPapaleguas = Application.get().getUrlPapaleguas();
        urlPapaleguas = Coalesce.asString(urlPapaleguas).replaceAll("http", "ws");
        String clientId = TenantContext.getContext();
        String userId = ApplicationSession.get().getSessaoAplicacao().getCodigoUsuario().toString();
        String script = ""
                + "var url = '"+ urlPapaleguas +"/papaleguas/websocket/SISTEMA/" + clientId + "/" + userId + "';"
                + "var ws = new WebSocket(url);"
                + "ws.onmessage = function(message) {"
                + "var behavior = " + behavior.getCallbackFunction(CallbackParameter.explicit("asyncId")).toString()
                + "behavior(message.data);"
                + "}";

        response.render(OnLoadHeaderItem.forScript(script));
    }

    private void notifyMessaging(AjaxRequestTarget target, QueryConsultaMensagensDTO dto) {
        if (getPage() instanceof IMessagingNotification) {
            if (RepositoryComponentDefault.SimNaoLong.NAO.value().equals(dto.getMensagem().getLida())) {
                VisualizarMensagemBehavior visualizarMensagemBehavior = new VisualizarMensagemBehavior();
                add(visualizarMensagemBehavior);
                new JGrowl().growlNovaMensagem(target, dto.getMensagem(), visualizarMensagemBehavior.getMensagemUrl(dto));
            }
            ((IMessagingNotification) getPage()).notifyMessage(target, dto);
        }
    }

    private void notifyProcess(AjaxRequestTarget target, AsyncProcess event) {
        if (getPage() instanceof IAsyncProcessNotification) {
            ((IAsyncProcessNotification) getPage()).notifyProcess(target, event);
        }
    }

    private void notifyReport(AjaxRequestTarget target, AsyncProcess asyncProcess) {
        try {
            if (AsyncProcess.STATUS_CONCLUIDO_ERRO.equals(asyncProcess.getStatus())) {
                MessageUtil.growlMessage(
                        target,
                        AsyncNotificationPanel.this,
                        "Ocorreu um erro no processo! Consulte o log para mais detalhes!",
                        JGrowlBehavior.INFO_STICKY);
            } else if (AsyncProcess.STATUS_CONCLUIDO_EXITO.equals(asyncProcess.getStatus())) {
                if (!getPage().getClass().getName().equals(asyncProcess.getJifClass())) {
                    DownloadReportBehavior downloadBehavior = new DownloadReportBehavior();

                    add(downloadBehavior);

                    CharSequence reportUrl = downloadBehavior.getReportUrl(asyncProcess);
                    String caminho = downloadBehavior.getRetornoLocal();
                    Loggable.log.info("Caminho Relatorio: " + caminho);

                    File f = new File(caminho);
                    try (ObjectInputStream ois = new ObjectInputStream(Files.newInputStream(f.toPath()))) {
                        final DataReport dr;
                        dr = (DataReport) ois.readObject();

                        if (reportUrl != null) {
                            if (dr == null || dr.getJasperPrint().getPages().isEmpty()) {
                                MessageUtil.growlMessage(
                                        target,
                                        AsyncNotificationPanel.this,
                                        asyncProcess.getNomeProcesso() + ". " + BundleManager.getString("naoHaPaginasParaImprimir") + "!",
                                        FeedbackMessage.INFO);
                            } else {
                                MessageUtil.growlMessage(
                                        target,
                                        AsyncNotificationPanel.this,
                                        asyncProcess.getNomeProcesso() + " pronto! <a class='report-link' href='" + reportUrl + "' target='_blank'>clique aqui</a> para exibir.",
                                        FeedbackMessage.INFO);
                            }
                        } else {
                            MessageUtil.growlMessage(
                                    target,
                                    AsyncNotificationPanel.this,
                                    asyncProcess.getNomeProcesso() + ". Processo sem retorno!",
                                    FeedbackMessage.WARNING);
                        }

                    } catch (ClassNotFoundException | IOException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                }
            }
            if (getPage() instanceof IAsyncReportNotification) {
                ((IAsyncReportNotification) getPage()).notifyReport(target, asyncProcess);
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
}
