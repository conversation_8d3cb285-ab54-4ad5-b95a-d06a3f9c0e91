package br.com.celk.component.menu.autocomplete.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.controle.interfaces.dto.QueryBuscaProgramasWebUsuarioDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerBuscaProgramaMenu extends Panel implements IRestricaoContainer<QueryBuscaProgramasWebUsuarioDTOParam> {

    private InputField txtMenu;
    
    private QueryBuscaProgramasWebUsuarioDTOParam param = new QueryBuscaProgramasWebUsuarioDTOParam();
    
    public RestricaoContainerBuscaProgramaMenu(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtMenu = new InputField("menu"));
        
        txtMenu.addAjaxUpdateValue();
        
        add(root);
    }

    @Override
    public QueryBuscaProgramasWebUsuarioDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtMenu.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtMenu;
    }

}