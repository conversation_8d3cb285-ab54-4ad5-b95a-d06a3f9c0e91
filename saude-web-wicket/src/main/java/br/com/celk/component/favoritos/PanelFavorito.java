package br.com.celk.component.favoritos;

import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.controle.interfaces.dto.QueryBuscaCaminhoMenuWebDTO;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.ProgramaFavorito;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.link.AbstractLink;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

/**
 *
 * <AUTHOR>
 */
public class PanelFavorito extends Panel{

    private ProgramaFavorito programaFavorito;
    
    public PanelFavorito(String id, ProgramaFavorito programaFavorito) {
        super(id);
        this.programaFavorito = programaFavorito;
        
        try {
            add(createLink());
        } catch (ClassNotFoundException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        
        add(new AbstractAjaxLink("btnRemoverFavorito") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(UsuarioFacade.class).removerFavorito(PanelFavorito.this.programaFavorito.getCodigo());
                Favoritos favoritos = findParent(Favoritos.class);
                if (favoritos!=null) {
                    favoritos.removerFavorito(target, PanelFavorito.this);
                }
            }
        });
    }
    
    private AbstractLink createLink() throws ClassNotFoundException {
        AbstractLink link = null;

        link = new BookmarkablePageLink("link", Class.forName(programaFavorito.getProgramaWeb().getProgramaPaginaPrincipal().getCaminhoPagina()), new PageParameters().add("cdPrg", programaFavorito.getProgramaWeb().getCodigo()));
        
        try {
            List<QueryBuscaCaminhoMenuWebDTO> caminhos = BOFactoryWicket.getBO(UsuarioFacade.class).buscarCaminhoMenuWeb(programaFavorito.getProgramaWeb());
            link.add(createLabel(caminhos.get(0).getDescricaoLabel()));
            add(createLabelCaminho(getDescricaoCaminhoMenu(caminhos.get(0).getCaminho())));
        } catch (DAOException ex) {
            Logger.getLogger(PanelFavorito.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(PanelFavorito.class.getName()).log(Level.SEVERE, null, ex);
        }


        return link;
    }
    
    public String getDescricaoCaminhoMenu(String caminho) {
        String descricao = "";
        
        if(!"".equals(Coalesce.asString(caminho))) {
            String[] split = caminho.split(":;");

            for(int i = 0; i < split.length; i++){
                if(!"".equals(Coalesce.asString(split[i]))) {
                    if(i != 0){
                        descricao += " -> ";
                    }
                    descricao += split[i];
                }
            }
        }
        
        return descricao;
    }

    private Label createLabel(String descricaoLabel) {
        Label label;
        label = new Label("label", descricaoLabel);
        label.add(new AttributeModifier("style", "float: left; font-weight: bold;"));
        
        return label;
    }
    
    private Label createLabelCaminho(String caminho) {
        Label labelCaminho;
        
        labelCaminho = new Label("caminho", caminho);
        labelCaminho.add(new AttributeModifier("style", "float: left; padding-left: 10px;"));
        
        return labelCaminho;
    }

    public ProgramaFavorito getProgramaFavorito() {
        return programaFavorito;
    }
}