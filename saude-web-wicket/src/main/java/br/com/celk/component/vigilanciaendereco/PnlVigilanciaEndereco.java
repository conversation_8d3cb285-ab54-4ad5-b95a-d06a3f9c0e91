package br.com.celk.component.vigilanciaendereco;

import br.com.celk.component.interfaces.IComponent;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.cadastro.interfaces.ICadastroListener;
import br.com.celk.view.vigilancia.endereco.DlgCadastroVigilanciaEndereco;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponentPanel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public class PnlVigilanciaEndereco extends FormComponentPanel<VigilanciaEndereco> implements IComponent<VigilanciaEndereco> {

    private DlgCadastroVigilanciaEndereco dlgCadastroVigilanciaEndereco;
    private AutoCompleteConsultaVigilanciaEndereco autoCompleteConsultaVigilanciaEndereco;
    private AbstractAjaxLink btnCadastrarEndereco;

    public PnlVigilanciaEndereco(String id) {
        this(id, false);
    }

    public PnlVigilanciaEndereco(String id, boolean required) {
        super(id);
        init(required);
    }

    public PnlVigilanciaEndereco(String id, IModel model) {
        super(id, model);
        init(false);
    }

    private void init(boolean required) {
        setOutputMarkupId(true);

        IModel<VigilanciaEndereco> model = getModel();
        if (model == null) {
            model = new Model<VigilanciaEndereco>();
        }

        add(autoCompleteConsultaVigilanciaEndereco = new AutoCompleteConsultaVigilanciaEndereco("autocomplete", model, required));
        autoCompleteConsultaVigilanciaEndereco.setLabel(new Model(bundle("endereco")));

        add(btnCadastrarEndereco = new AbstractAjaxLink("btnCadastrarEndereco") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                cadastrarVigilanciaEndereco(target);
            }
        });
    }

    private void cadastrarVigilanciaEndereco(AjaxRequestTarget target) {
        if (dlgCadastroVigilanciaEndereco == null) {
            WindowUtil.addModal(target, getForm(), dlgCadastroVigilanciaEndereco = new DlgCadastroVigilanciaEndereco(WindowUtil.newModalId(this)));
            dlgCadastroVigilanciaEndereco.add(new ICadastroListener<VigilanciaEndereco>() {
                @Override
                public void onSalvar(AjaxRequestTarget target, VigilanciaEndereco vigilanciaEndereco) throws ValidacaoException, DAOException {
                    autoCompleteConsultaVigilanciaEndereco.limpar(target);
                    autoCompleteConsultaVigilanciaEndereco.setComponentValue(vigilanciaEndereco);
                    target.add(autoCompleteConsultaVigilanciaEndereco);
                }
            });
        }
        dlgCadastroVigilanciaEndereco.limpar(target);
        dlgCadastroVigilanciaEndereco.show(target);
    }

    @Override
    protected void convertInput() {
        setConvertedInput((VigilanciaEndereco) autoCompleteConsultaVigilanciaEndereco.getComponentValue());
    }

    @Override
    protected void onBeforeRender() {
        Object modelObject = getModelObject();

        if (modelObject == null) {
            modelObject = getConvertedInput();
        }

        autoCompleteConsultaVigilanciaEndereco.setModelObject(modelObject);

        super.onBeforeRender();
    }

    @Override
    public VigilanciaEndereco getComponentValue() {
        return getModelObject();
    }

    @Override
    public void setComponentValue(VigilanciaEndereco value) {
        setModelObject(value);
    }
    @Override
    public void addAjaxUpdateValue() {
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        setComponentValue(null);
        clearInput();
        autoCompleteConsultaVigilanciaEndereco.limpar(target);

        target.appendJavaScript(JScript.removeAutoCompleteDrop());
    }
    public void addRequired(){
        autoCompleteConsultaVigilanciaEndereco.setRequired(true);
        autoCompleteConsultaVigilanciaEndereco.getTxtDescricao().addRequiredClass();
    }
    
    public void removeRequired(){
        autoCompleteConsultaVigilanciaEndereco.setRequired(false);
        autoCompleteConsultaVigilanciaEndereco.getTxtDescricao().removeRequiredClass();
    }

    public AutoCompleteConsultaVigilanciaEndereco getAutoCompleteConsultaVigilanciaEndereco() {
        return autoCompleteConsultaVigilanciaEndereco;
    }

    public AbstractAjaxLink getBtnCadastrarEndereco() {
        return btnCadastrarEndereco;
    }
}
