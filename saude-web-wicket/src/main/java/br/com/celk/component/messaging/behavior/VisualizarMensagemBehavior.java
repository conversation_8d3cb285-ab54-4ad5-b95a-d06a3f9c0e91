package br.com.celk.component.messaging.behavior;

import br.com.celk.view.comunicacao.mensagem.CaixaMensagensPage;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTO;
import org.apache.wicket.behavior.AbstractAjaxBehavior;

/**
 *
 * <AUTHOR>
 */
public class VisualizarMensagemBehavior extends AbstractAjaxBehavior {

    private QueryConsultaMensagensDTO dto;
    
    @Override
    public void onRequest() {
        getComponent().setResponsePage(new CaixaMensagensPage(dto));
    }
    
    public String getMensagemUrl(QueryConsultaMensagensDTO dto){
        this.dto = dto;
        return getCallbackUrl().toString();
    }

}
