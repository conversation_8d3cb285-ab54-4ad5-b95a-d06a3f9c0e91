package br.com.celk.component.authotization;

import br.com.celk.component.authotization.presenter.AuthorizationPresenter;
import br.com.celk.component.authotization.presenter.IAuthorizationPresenter;
import br.com.celk.component.window.Window;
import br.com.celk.system.authorization.interfaces.action.IAuthorizationAction;
import br.com.celk.system.authorization.interfaces.action.IAuthorizationClose;
import br.com.celk.system.bundle.BundleManager;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public class DlgAuthorization extends Window{

    private IAuthorizationPresenter authorizationPresenter;
    
    public DlgAuthorization(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(60);
        setInitialWidth(300);
        setResizable(false);
        setTitle(new LoadableDetachableModel<String>() {

            @Override
            protected String load() {
                return BundleManager.getString("autorizar");
            }
        });
        PnlAuthorization pnlAuthorization;
        setContent(pnlAuthorization = new PnlAuthorization(getContentId(), getAuthorizationPresenter()));
        getAuthorizationPresenter().setView(pnlAuthorization);
    }

    public void setAuthorizationAction(IAuthorizationAction... actions) {
        getAuthorizationPresenter().setAuthorizationAction(actions);
    }
    
    public void setCloseAction(IAuthorizationClose onClose) {
        getAuthorizationPresenter().setCloseAction(onClose);
    }
    
    private IAuthorizationPresenter getAuthorizationPresenter() {
        if (this.authorizationPresenter == null) {
            this.authorizationPresenter = new AuthorizationPresenter(this);
        }
        
        return this.authorizationPresenter;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return getAuthorizationPresenter().getComponentRequestFocus();
    }

}
