package br.com.celk.component.menu;

import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.favoritos.Favoritos;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.menu.autocomplete.ConfiguratorBuscaProgramaMenu;
import br.com.celk.component.menu.estrutura.MenuItem;
import br.com.celk.component.tokenautocomplete.TokenAutoComplete;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.comunicacao.anexo.ConsultaAnexoDocumentoPage;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.ProgramaFavorito;
import br.com.ksisolucoes.vo.controle.web.ProgramaPagina;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.link.AbstractLink;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.html.link.ExternalLink;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Menu extends Panel{

    private BookmarkablePageLink linkAnexos;
    private Favoritos favoritos;
    private TokenAutoComplete tokenAutoComplete;
    
    public Menu(String id) {
        super(id);
        
        add(new ListView<MenuItem>("modulos", new LoadableDetachableModel<List<? extends MenuItem>>() {

            @Override
            protected List<? extends MenuItem> load() {
                return new MenuController().getMenu();
            }
        }) {

            @Override
            protected void populateItem(ListItem<MenuItem> item) {
                item.add(createLink(item.getModelObject()));
            
                if (item.getIndex()==0) {
                    item.add(new AttributeAppender("class", " first"));
                } else if (item.getIndex()==getModelObject().size()-1){
                    item.add(new AttributeAppender("class", " last"));
                }
                
                item.add(new ListView<MenuItem>("submenus", new LoadableDetachableModel<List<? extends MenuItem>>(item.getModelObject().getChilds()) {

                        @Override
                        protected List<? extends MenuItem> load() {
                            return ((MenuItem)findParent(ListItem.class).getModelObject()).getChilds();
                        }
                    }) {

                    @Override
                    protected void populateItem(ListItem<MenuItem> item) {
                        item.add(createLabel(item.getModelObject().getDescricao()));
                        item.add(new ListView<MenuItem>("contextos", new LoadableDetachableModel<List<? extends MenuItem>>(item.getModelObject().getChilds()) {

                                @Override
                                protected List<? extends MenuItem> load() {
                                    return ((MenuItem)findParent(ListItem.class).getModelObject()).getChilds();
                                }
                            }) {

                            @Override
                            protected void populateItem(ListItem<MenuItem> item) {
                                item.add(createLabel(item.getModelObject().getDescricao()));
                                item.add(new ListView<MenuItem>("menus", new LoadableDetachableModel<List<? extends MenuItem>>(item.getModelObject().getChilds()) {

                                        @Override
                                        protected List<? extends MenuItem> load() {
                                            return ((MenuItem)findParent(ListItem.class).getModelObject()).getChilds();
                                        }
                                    }) {

                                    @Override
                                    protected void populateItem(ListItem<MenuItem> item) {
                                        item.add(createLink(item.getModelObject()));
                                    }
                                });

                            }
                        });
                    }
                });
            }
        });
        
        add(linkAnexos = new BookmarkablePageLink("linkAnexos", ConsultaAnexoDocumentoPage.class) {
            @Override
            public boolean isVisible() {
                return new PermissoesWebUtil().isPagePermitted(ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario(), ConsultaAnexoDocumentoPage.class.getName());
            }
        });

        add(favoritos = new Favoritos("favoritos"));

        add(tokenAutoComplete = new TokenAutoComplete<ProgramaWeb>("searchPrograma", new Model<ProgramaWeb>()) {

            @Override
            public IConsultaConfigurator getConsultaConfiguratorInstance() {
                return new ConfiguratorBuscaProgramaMenu();
            }
            
            @Override
            public String[] getPropertiesLoad() {
                return VOUtils.mergeProperties(new HQLProperties(ProgramaWeb.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CODIGO),
                            VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CAMINHO_PAGINA),});
            }
        });
        
        tokenAutoComplete.add(new ConsultaListener<ProgramaWeb>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProgramaWeb object) {
                try {
                    String caminhoPagina = object.getProgramaPaginaPrincipal().getCaminhoPagina();
                    if (caminhoPagina!=null) {
                        Class forName = Class.forName(caminhoPagina);
                        setResponsePage(forName, new PageParameters().add("cdPrg", object.getCodigo()));
                    }
                } catch (ClassNotFoundException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }
        });
        tokenAutoComplete.setOnAddAction("$('#"+tokenAutoComplete.getTextField().getMarkupId()+"').tokenInput('clear');");
        tokenAutoComplete.setHintText(BundleManager.getString("digiteParaBuscarUmPrograma"));
    }

    private AbstractLink createLink(MenuItem menuItem){
        AbstractLink link = null;
        
        if (menuItem.getClassName()!=null) {
            try {
                link = new BookmarkablePageLink("link", Class.forName(menuItem.getClassName()), new PageParameters().add("cdPrg", menuItem.getMenuWeb().getProgramaWeb().getCodigo()));
            } catch (ClassNotFoundException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        } else {
            link = new ExternalLink("link", "#");
        }
        
        link.add(createLabel(menuItem.getDescricao()));
        
        return link;
    }
    
    private Label createLabel(String descricao){
        return new Label("label", new LoadableDetachableModel(descricao) {

            @Override
            protected Object load() {
                return ((MenuItem)findParent(ListItem.class).getModelObject()).getDescricao();
            }
        });
    }
    
    public void adicionarFavorito(AjaxRequestTarget target, ProgramaFavorito programaFavorito){
        favoritos.adicionarFavorito(target, programaFavorito);
    }

}
