package br.com.celk.component.favoritos;

import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.web.ProgramaFavorito;
import br.com.ksisolucoes.vo.controle.web.ProgramaPagina;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.odlabs.wiquery.ui.sortable.SortableBehavior;

/**
 *
 * <AUTHOR> claudio
 */
public class Favoritos extends Panel {

    private ListView repeatingView;
    private WebMarkupContainer sortableWicket;
    
    public Favoritos(String id) {
        super(id);

        init();
    }

    private void init() {

        IModel<List<ProgramaFavorito>> sampleEntityListModel = new LoadableDetachableModel<List<ProgramaFavorito>>() {
            @Override
            protected List<ProgramaFavorito> load() {
                return FavoritosCache.get().getFavoritos();
            }
        };
        
        repeatingView = new ListView<ProgramaFavorito>("repeaterView", sampleEntityListModel) {
                @Override
                protected void populateItem(final ListItem<ProgramaFavorito> item) {
                    item.add(new PanelFavorito("panel", item.getModelObject()).setRenderBodyOnly(true));
                    item.setOutputMarkupId(true);
                }
        };
        
        sortableWicket = new WebMarkupContainer("sortableWicket");
        sortableWicket.setOutputMarkupId(true);
        
        SortableBehavior sortableBehavior = new SortableBehavior();
        
        sortableBehavior.setUpdateEvent(new SortableBehavior.AjaxUpdateCallback() {
                      
            @Override
            public void update(AjaxRequestTarget target, Component sortedComponent, int index, Component sortItem) {
                try {
                    BOFactoryWicket.getBO(UsuarioFacade.class).reordenarFavorito(((ProgramaFavorito)sortedComponent.getDefaultModelObject()).getCodigo(), new Integer(index).longValue());
                    FavoritosCache.get().recarregarFavoritos();
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }
        });
        
        sortableBehavior.setConnectWith(".connectedSortable");
        sortableBehavior.setAxis(SortableBehavior.AxisEnum.Y);
//        sortableWicket.add(sortableBehavior);
        sortableWicket.add(repeatingView);
        add(sortableWicket);

    }
    
    public void adicionarFavorito(AjaxRequestTarget target, ProgramaFavorito programaFavorito){
            programaFavorito = LoadManager.getInstance(ProgramaFavorito.class)
                        .addProperties(new HQLProperties(ProgramaFavorito.class).getProperties())
                        .addProperties(new HQLProperties(ProgramaWeb.class, ProgramaFavorito.PROP_PROGRAMA_WEB).getProperties())
                        .addProperties(new HQLProperties(ProgramaPagina.class, VOUtils.montarPath(ProgramaFavorito.PROP_PROGRAMA_WEB, ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProgramaFavorito.PROP_CODIGO), programaFavorito.getCodigo()))
                        .start().getVO();
            
            if (!FavoritosCache.get().getFavoritos().contains(programaFavorito)) {
                target.add(sortableWicket);
                FavoritosCache.get().recarregarFavoritos();
            }
    }
    
    public void removerFavorito(AjaxRequestTarget target, PanelFavorito panelFavorito){
        repeatingView.remove(panelFavorito);
        target.add(sortableWicket);
        FavoritosCache.get().recarregarFavoritos();
        BasePage basePage = findParent(BasePage.class);
        if (basePage!=null && basePage.getCodigoPrograma()!=null && panelFavorito.getProgramaFavorito().getProgramaWeb().getCodigo().equals(basePage.getCodigoPrograma())) {
            basePage.resolveBookmarkLinkClass(target);
        }
    }

}
