package br.com.celk.component.recaptcha;

import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

/**
 * Serviço para validação do Google reCAPTCHA v3
 * <p>
 * Uso:
 * boolean isValid = ReCaptchaV3Service.isValid(token);
 * ReCaptchaV3Service.ValidationResult result = ReCaptchaV3Service.validate(token, "search", 0.5);
 */
public class ReCaptchaV3Service {
    private static final String VERIFY_URL = "https://www.google.com/recaptcha/api/siteverify";
    private static final double DEFAULT_MIN_SCORE = 0.5;

    public static boolean isEnabled() {
        return (getSiteKey() != null && !getSiteKey().trim().isEmpty()) && (getSecretKey() != null && !getSecretKey().trim().isEmpty());
    }

    /**
     * @param token Token do reCAPTCHA v3
     * @return true se válido (score >= 0.5), false caso contrário
     */
    public static boolean isValid(String token) {
        return validate(token, null, DEFAULT_MIN_SCORE).isSuccess();
    }

    /**
     * Validação completa com verificação de ação e score
     *
     * @param token          Token do reCAPTCHA v3
     * @param expectedAction Ação esperada (pode ser null)
     * @param minScore       Score mínimo para considerar válido
     * @return Resultado detalhado da validação
     */
    public static ValidationResult validate(String token, String expectedAction, double minScore) {
        ValidationResult result = new ValidationResult();

        if (token == null || token.trim().isEmpty()) {
            result.setSuccess(false);
            result.setErrorCodes(new String[]{"missing-input-response"});
            return result;
        }

        try {
            String secretKey = getSecretKey();
            if (secretKey == null) {
                result.setSuccess(false);
                result.setErrorCodes(new String[]{"missing-secret-key"});
                return result;
            }

            // Fazer chamada HTTP para o Google
            String params = "secret=" + URLEncoder.encode(secretKey, "UTF-8") +
                    "&response=" + URLEncoder.encode(token, "UTF-8");

            URL url = new URL(VERIFY_URL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setConnectTimeout(10000); // 10 segundos
            conn.setReadTimeout(10000);

            // Enviar dados
            try (OutputStream out = conn.getOutputStream()) {
                out.write(params.getBytes(StandardCharsets.UTF_8));
            }

            // Ler resposta
            String response;
            try (InputStream in = conn.getInputStream()) {
                response = new BufferedReader(new InputStreamReader(in))
                        .lines().collect(Collectors.joining("\n"));
            }

            // Processar resposta JSON usando Gson
            JsonParser parser = new JsonParser();
            JsonObject json = parser.parse(response).getAsJsonObject();

            result.setSuccess(json.has("success") ? json.get("success").getAsBoolean() : false);
            result.setScore(json.has("score") ? json.get("score").getAsDouble() : 0.0);
            result.setAction(json.has("action") ? json.get("action").getAsString() : null);
            result.setHostname(json.has("hostname") ? json.get("hostname").getAsString() : null);

            // Processar error-codes se existirem
            if (json.has("error-codes")) {
                JsonArray errorArray = json.getAsJsonArray("error-codes");
                String[] errors = new String[errorArray.size()];
                for (int i = 0; i < errorArray.size(); i++) {
                    errors[i] = errorArray.get(i).getAsString();
                }
                result.setErrorCodes(errors);
            }

            // Verificar se passou na validação básica
            if (!result.isSuccess()) {
                return result;
            }

            // Verificar score mínimo
            if (result.getScore() < minScore) {
                result.setSuccess(false);
                result.setErrorCodes(new String[]{"score-too-low"});
                return result;
            }

            // Verificar ação esperada (se especificada)
            if (expectedAction != null && !expectedAction.equals(result.getAction())) {
                result.setSuccess(false);
                result.setErrorCodes(new String[]{"action-mismatch"});
                return result;
            }

            return result;

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorCodes(new String[]{"verification-failed"});
            return result;
        }
    }

    /**
     * Obtém a chave secreta do reCAPTCHA
     */
    private static String getSecretKey() {
        return CargaBasicoPadrao.getInstance().getParametroPadrao().getGoogleReCaptchaSecretKey();
    }

    /**
     * Obtém a site key do reCAPTCHA
     * Caso não informado, desabilita o reCAPTCHA
     */
    public static String getSiteKey() {
        return CargaBasicoPadrao.getInstance().getParametroPadrao().getGoogleReCaptchaKey();
    }

    public static class ValidationResult {
        private boolean success;
        private double score;
        private String action;
        private String hostname;
        private String[] errorCodes;

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public double getScore() {
            return score;
        }

        public void setScore(double score) {
            this.score = score;
        }

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public String getHostname() {
            return hostname;
        }

        public void setHostname(String hostname) {
            this.hostname = hostname;
        }

        public String[] getErrorCodes() {
            return errorCodes;
        }

        public void setErrorCodes(String[] errorCodes) {
            this.errorCodes = errorCodes;
        }

        @Override
        public String toString() {
            return String.format("ValidationResult{success=%s, score=%.2f, action='%s', hostname='%s'}",
                    success, score, action, hostname);
        }
    }
}
