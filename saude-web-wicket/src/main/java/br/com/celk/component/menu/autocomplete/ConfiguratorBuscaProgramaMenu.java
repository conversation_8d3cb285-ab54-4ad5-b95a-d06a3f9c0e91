package br.com.celk.component.menu.autocomplete;

import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.autocomplete.IAutoCompleteSettings;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.menu.autocomplete.restricaocontainer.RestricaoContainerBuscaProgramaMenu;
import br.com.celk.component.menu.autocomplete.settings.BuscaProgramaMenuAutoCompleteSettings;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.controle.interfaces.dto.QueryBuscaProgramasWebUsuarioDTOParam;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.ProgramaPagina;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;

/**
 *
 * <AUTHOR>
 */
public class ConfiguratorBuscaProgramaMenu extends ConsultaConfigurator {

    @Override
    public void getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(ProgramaWeb.class);

        columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), ProgramaWeb.PROP_CODIGO));
    }

    @Override
    public IRestricaoContainer getRestricaoContainerInstance(String id) {
        return new RestricaoContainerBuscaProgramaMenu(id);
    }

    @Override
    public IPagerProvider getDataProviderInstance() {
        return new QueryPagerProvider<ProgramaWeb, QueryBuscaProgramasWebUsuarioDTOParam>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging<QueryBuscaProgramasWebUsuarioDTOParam> dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(UsuarioFacade.class).buscarProgramasUsuario(dataPaging);
            }

            @Override
            public void customizeParam(QueryBuscaProgramasWebUsuarioDTOParam param) {
                param.setUsuario(ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario());
            }

            @Override
            public QueryBuscaProgramasWebUsuarioDTOParam getSearchParam(String searchCriteria) {
                QueryBuscaProgramasWebUsuarioDTOParam param = new QueryBuscaProgramasWebUsuarioDTOParam();

                param.setSearchCriteria(searchCriteria);

                return param;
            }
        };
    }
    
    @Override
    public Object loadVO(String id, String[] propertiesToLoad) throws DAOException, ValidacaoException {
        Long codigo = new Long(id);
        return LoadManager.getInstance(ProgramaWeb.class)
                .setId(codigo)
                .addProperties(new HQLProperties(ProgramaWeb.class).getProperties())
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_CAMINHO_PAGINA))
                .addProperty(VOUtils.montarPath(ProgramaWeb.PROP_PROGRAMA_PAGINA_PRINCIPAL, ProgramaPagina.PROP_FLAG_PUBLICO))
                .start().getVO();
    }
    
    @Override
    public Class getReferenceClass() {
        return ProgramaWeb.class;
    }
    

    @Override
    public IAutoCompleteSettings getAutoCompleteSettingsInstance() {
        return new BuscaProgramaMenuAutoCompleteSettings();
    }
}
