/*
 * Generated by <PERSON>Rep<PERSON><PERSON> - 24/08/20 10:30
 */
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.fill.*;

import java.util.*;
import java.math.*;
import java.text.*;
import java.io.*;
import java.net.*;

import net.sf.jasperreports.engine.*;
import java.util.*;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.CollectionUtils;
import net.sf.jasperreports.engine.data.*;


/**
 *
 */
public class relatorio_impressao_comprovante_agendamento_1598275817605_50992 extends JREvaluator
{


    /**
     *
     */
    private JRFillParameter parameter_EXIBIR_CABECALHO = null;
    private JRFillParameter parameter_REPORT_CONNECTION = null;
    private JRFillParameter parameter_VERSAO_SISTEMA = null;
    private JRFillParameter parameter_JASPER_REPORT = null;
    private JRFillParameter parameter_REPORT_TIME_ZONE = null;
    private JRFillParameter parameter_modelComprovanteAgendamento = null;
    private JRFillParameter parameter_REPORT_TEMPLATES = null;
    private JRFillParameter parameter_TITULO_REPORT = null;
    private JRFillParameter parameter_EXIBIR_DIRETOR_TECNICO = null;
    private JRFillParameter parameter_REPORT_MAX_COUNT = null;
    private JRFillParameter parameter_REPORT_SCRIPTLET = null;
    private JRFillParameter parameter_EXIBIR_NUMERO_PAGINAS = null;
    private JRFillParameter parameter_nomeEmpresaRelatorio = null;
    private JRFillParameter parameter_REPORT_PARAMETERS_MAP = null;
    private JRFillParameter parameter_REPORT_RESOURCE_BUNDLE = null;
    private JRFillParameter parameter_CABECALHO_DIRETOR_TECNICO = null;
    private JRFillParameter parameter_REPORT_DATA_SOURCE = null;
    private JRFillParameter parameter_SORT_FIELDS = null;
    private JRFillParameter parameter_DESC_CABECALHO_PADRAO = null;
    private JRFillParameter parameter_UNIDADE_ATENDIMENTO = null;
    private JRFillParameter parameter_IS_IGNORE_PAGINATION = null;
    private JRFillParameter parameter_NUMERO_UNIDADE = null;
    private JRFillParameter parameter_nomePrefeitura = null;
    private JRFillParameter parameter_FILTER = null;
    private JRFillParameter parameter_CAMINHO_IMAGEM_PADRAO = null;
    private JRFillParameter parameter_descricaoEmpresa = null;
    private JRFillParameter parameter_REPORT_LOCALE = null;
    private JRFillParameter parameter_EXIBIR_TITULO_PRIMEIRA_PAGINA = null;
    private JRFillParameter parameter_SISTEMA = null;
    private JRFillParameter parameter_RUA_UNIDADE = null;
    private JRFillParameter parameter_CIDADE_UNIDADE = null;
    private JRFillParameter parameter_nomeUsuario = null;
    private JRFillParameter parameter_REPORT_FILE_RESOLVER = null;
    private JRFillParameter parameter_BAIRRO_UNIDADE = null;
    private JRFillParameter parameter_USUARIO_LOGADO = null;
    private JRFillParameter parameter_REPORT_FORMAT_FACTORY = null;
    private JRFillParameter parameter_CEP_UNIDADE = null;
    private JRFillParameter parameter_EXIBIR_HORARIO = null;
    private JRFillParameter parameter_FONE_UNIDADE = null;
    private JRFillParameter parameter_UF_UNIDADE = null;
    private JRFillParameter parameter_REPORT_CONTEXT = null;
    private JRFillParameter parameter_REPORT_CLASS_LOADER = null;
    private JRFillParameter parameter_REPORT_URL_HANDLER_FACTORY = null;
    private JRFillParameter parameter_REPORT_VIRTUALIZER = null;
    private JRFillParameter parameter_EXIBIR_RODAPE = null;
    private JRFillParameter parameter_CABECALHO_ADICIONAL_2 = null;
    private JRFillParameter parameter_CABECALHO_ADICIONAL_1 = null;
    private JRFillField field_agendaGradeAtendimentoHorarioList = null;
    private JRFillField field_usuarioCadsusCns = null;
    private JRFillField field_preparacaoExames = null;
    private JRFillField field_profissionalAgenda = null;
    private JRFillField field_descricaoPrioridade = null;
    private JRFillField field_dataAgendamentoFormatado = null;
    private JRFillField field_recomendacoes = null;
    private JRFillField field_tipoProcedimento = null;
    private JRFillField field_profissional = null;
    private JRFillField field_nomePaciente = null;
    private JRFillField field_agendaGradeAtendimentoHorario = null;
    private JRFillField field_numeroProntuario = null;
    private JRFillField field_codigoAgendamento = null;
    private JRFillField field_preparacaoExameList = null;
    private JRFillField field_enderecoDomicilio = null;
    private JRFillField field_usuarioCadsus = null;
    private JRFillField field_solicitacaoAgendamento = null;
    private JRFillField field_localAgendamento = null;
    private JRFillField field_exameList = null;
    private JRFillField field_empresa = null;
    private JRFillField field_procedimento = null;
    private JRFillField field_chaveValidacao = null;
    private JRFillField field_cid = null;
    private JRFillVariable variable_PAGE_NUMBER = null;
    private JRFillVariable variable_COLUMN_NUMBER = null;
    private JRFillVariable variable_REPORT_COUNT = null;
    private JRFillVariable variable_PAGE_COUNT = null;
    private JRFillVariable variable_COLUMN_COUNT = null;


    /**
     *
     */
    public void customizedInit(
        Map pm,
        Map fm,
        Map vm
        )
    {
        initParams(pm);
        initFields(fm);
        initVars(vm);
    }


    /**
     *
     */
    private void initParams(Map pm)
    {
        parameter_EXIBIR_CABECALHO = (JRFillParameter)pm.get("EXIBIR_CABECALHO");
        parameter_REPORT_CONNECTION = (JRFillParameter)pm.get("REPORT_CONNECTION");
        parameter_VERSAO_SISTEMA = (JRFillParameter)pm.get("VERSAO_SISTEMA");
        parameter_JASPER_REPORT = (JRFillParameter)pm.get("JASPER_REPORT");
        parameter_REPORT_TIME_ZONE = (JRFillParameter)pm.get("REPORT_TIME_ZONE");
        parameter_modelComprovanteAgendamento = (JRFillParameter)pm.get("modelComprovanteAgendamento");
        parameter_REPORT_TEMPLATES = (JRFillParameter)pm.get("REPORT_TEMPLATES");
        parameter_TITULO_REPORT = (JRFillParameter)pm.get("TITULO_REPORT");
        parameter_EXIBIR_DIRETOR_TECNICO = (JRFillParameter)pm.get("EXIBIR_DIRETOR_TECNICO");
        parameter_REPORT_MAX_COUNT = (JRFillParameter)pm.get("REPORT_MAX_COUNT");
        parameter_REPORT_SCRIPTLET = (JRFillParameter)pm.get("REPORT_SCRIPTLET");
        parameter_EXIBIR_NUMERO_PAGINAS = (JRFillParameter)pm.get("EXIBIR_NUMERO_PAGINAS");
        parameter_nomeEmpresaRelatorio = (JRFillParameter)pm.get("nomeEmpresaRelatorio");
        parameter_REPORT_PARAMETERS_MAP = (JRFillParameter)pm.get("REPORT_PARAMETERS_MAP");
        parameter_REPORT_RESOURCE_BUNDLE = (JRFillParameter)pm.get("REPORT_RESOURCE_BUNDLE");
        parameter_CABECALHO_DIRETOR_TECNICO = (JRFillParameter)pm.get("CABECALHO_DIRETOR_TECNICO");
        parameter_REPORT_DATA_SOURCE = (JRFillParameter)pm.get("REPORT_DATA_SOURCE");
        parameter_SORT_FIELDS = (JRFillParameter)pm.get("SORT_FIELDS");
        parameter_DESC_CABECALHO_PADRAO = (JRFillParameter)pm.get("DESC_CABECALHO_PADRAO");
        parameter_UNIDADE_ATENDIMENTO = (JRFillParameter)pm.get("UNIDADE_ATENDIMENTO");
        parameter_IS_IGNORE_PAGINATION = (JRFillParameter)pm.get("IS_IGNORE_PAGINATION");
        parameter_NUMERO_UNIDADE = (JRFillParameter)pm.get("NUMERO_UNIDADE");
        parameter_nomePrefeitura = (JRFillParameter)pm.get("nomePrefeitura");
        parameter_FILTER = (JRFillParameter)pm.get("FILTER");
        parameter_CAMINHO_IMAGEM_PADRAO = (JRFillParameter)pm.get("CAMINHO_IMAGEM_PADRAO");
        parameter_descricaoEmpresa = (JRFillParameter)pm.get("descricaoEmpresa");
        parameter_REPORT_LOCALE = (JRFillParameter)pm.get("REPORT_LOCALE");
        parameter_EXIBIR_TITULO_PRIMEIRA_PAGINA = (JRFillParameter)pm.get("EXIBIR_TITULO_PRIMEIRA_PAGINA");
        parameter_SISTEMA = (JRFillParameter)pm.get("SISTEMA");
        parameter_RUA_UNIDADE = (JRFillParameter)pm.get("RUA_UNIDADE");
        parameter_CIDADE_UNIDADE = (JRFillParameter)pm.get("CIDADE_UNIDADE");
        parameter_nomeUsuario = (JRFillParameter)pm.get("nomeUsuario");
        parameter_REPORT_FILE_RESOLVER = (JRFillParameter)pm.get("REPORT_FILE_RESOLVER");
        parameter_BAIRRO_UNIDADE = (JRFillParameter)pm.get("BAIRRO_UNIDADE");
        parameter_USUARIO_LOGADO = (JRFillParameter)pm.get("USUARIO_LOGADO");
        parameter_REPORT_FORMAT_FACTORY = (JRFillParameter)pm.get("REPORT_FORMAT_FACTORY");
        parameter_CEP_UNIDADE = (JRFillParameter)pm.get("CEP_UNIDADE");
        parameter_EXIBIR_HORARIO = (JRFillParameter)pm.get("EXIBIR_HORARIO");
        parameter_FONE_UNIDADE = (JRFillParameter)pm.get("FONE_UNIDADE");
        parameter_UF_UNIDADE = (JRFillParameter)pm.get("UF_UNIDADE");
        parameter_REPORT_CONTEXT = (JRFillParameter)pm.get("REPORT_CONTEXT");
        parameter_REPORT_CLASS_LOADER = (JRFillParameter)pm.get("REPORT_CLASS_LOADER");
        parameter_REPORT_URL_HANDLER_FACTORY = (JRFillParameter)pm.get("REPORT_URL_HANDLER_FACTORY");
        parameter_REPORT_VIRTUALIZER = (JRFillParameter)pm.get("REPORT_VIRTUALIZER");
        parameter_EXIBIR_RODAPE = (JRFillParameter)pm.get("EXIBIR_RODAPE");
        parameter_CABECALHO_ADICIONAL_2 = (JRFillParameter)pm.get("CABECALHO_ADICIONAL_2");
        parameter_CABECALHO_ADICIONAL_1 = (JRFillParameter)pm.get("CABECALHO_ADICIONAL_1");
    }


    /**
     *
     */
    private void initFields(Map fm)
    {
        field_agendaGradeAtendimentoHorarioList = (JRFillField)fm.get("agendaGradeAtendimentoHorarioList");
        field_usuarioCadsusCns = (JRFillField)fm.get("usuarioCadsusCns");
        field_preparacaoExames = (JRFillField)fm.get("preparacaoExames");
        field_profissionalAgenda = (JRFillField)fm.get("profissionalAgenda");
        field_descricaoPrioridade = (JRFillField)fm.get("descricaoPrioridade");
        field_dataAgendamentoFormatado = (JRFillField)fm.get("dataAgendamentoFormatado");
        field_recomendacoes = (JRFillField)fm.get("recomendacoes");
        field_tipoProcedimento = (JRFillField)fm.get("tipoProcedimento");
        field_profissional = (JRFillField)fm.get("profissional");
        field_nomePaciente = (JRFillField)fm.get("nomePaciente");
        field_agendaGradeAtendimentoHorario = (JRFillField)fm.get("agendaGradeAtendimentoHorario");
        field_numeroProntuario = (JRFillField)fm.get("numeroProntuario");
        field_codigoAgendamento = (JRFillField)fm.get("codigoAgendamento");
        field_preparacaoExameList = (JRFillField)fm.get("preparacaoExameList");
        field_enderecoDomicilio = (JRFillField)fm.get("enderecoDomicilio");
        field_usuarioCadsus = (JRFillField)fm.get("usuarioCadsus");
        field_solicitacaoAgendamento = (JRFillField)fm.get("solicitacaoAgendamento");
        field_localAgendamento = (JRFillField)fm.get("localAgendamento");
        field_exameList = (JRFillField)fm.get("exameList");
        field_empresa = (JRFillField)fm.get("empresa");
        field_procedimento = (JRFillField)fm.get("procedimento");
        field_chaveValidacao = (JRFillField)fm.get("chaveValidacao");
        field_cid = (JRFillField)fm.get("cid");
    }


    /**
     *
     */
    private void initVars(Map vm)
    {
        variable_PAGE_NUMBER = (JRFillVariable)vm.get("PAGE_NUMBER");
        variable_COLUMN_NUMBER = (JRFillVariable)vm.get("COLUMN_NUMBER");
        variable_REPORT_COUNT = (JRFillVariable)vm.get("REPORT_COUNT");
        variable_PAGE_COUNT = (JRFillVariable)vm.get("PAGE_COUNT");
        variable_COLUMN_COUNT = (JRFillVariable)vm.get("COLUMN_COUNT");
    }


    /**
     *
     */
    public Object evaluate(int id) throws Throwable
    {
        Object value = null;

        switch (id)
        {
            case 0 : 
            {
                value = null; //$JR_EXPR_ID=0$
                break;
            }
            case 1 : 
            {
                value = false; //$JR_EXPR_ID=1$
                break;
            }
            case 2 : 
            {
                value = false; //$JR_EXPR_ID=2$
                break;
            }
            case 3 : 
            {
                value = false; //$JR_EXPR_ID=3$
                break;
            }
            case 4 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=4$
                break;
            }
            case 5 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=5$
                break;
            }
            case 6 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=6$
                break;
            }
            case 7 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=7$
                break;
            }
            case 8 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=8$
                break;
            }
            case 9 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=9$
                break;
            }
            case 10 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=10$
                break;
            }
            case 11 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=11$
                break;
            }
            case 12 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=12$
                break;
            }
            case 13 : 
            {
                value = ((java.lang.String)parameter_DESC_CABECALHO_PADRAO.getValue()); //$JR_EXPR_ID=13$
                break;
            }
            case 14 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=14$
                break;
            }
            case 15 : 
            {
                value = ((java.lang.String)parameter_UNIDADE_ATENDIMENTO.getValue()); //$JR_EXPR_ID=15$
                break;
            }
            case 16 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=16$
                break;
            }
            case 17 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=17$
                break;
            }
            case 18 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_1.getValue()); //$JR_EXPR_ID=18$
                break;
            }
            case 19 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=19$
                break;
            }
            case 20 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_2.getValue()); //$JR_EXPR_ID=20$
                break;
            }
            case 21 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && (!((java.lang.Boolean)parameter_EXIBIR_TITULO_PRIMEIRA_PAGINA.getValue()) || ((java.lang.Integer)variable_PAGE_NUMBER.getValue()) == 1); //$JR_EXPR_ID=21$
                break;
            }
            case 22 : 
            {
                value = ((java.lang.String)parameter_TITULO_REPORT.getValue()); //$JR_EXPR_ID=22$
                break;
            }
            case 23 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=23$
                break;
            }
            case 24 : 
            {
                value = ((java.lang.String)parameter_CAMINHO_IMAGEM_PADRAO.getValue()); //$JR_EXPR_ID=24$
                break;
            }
            case 25 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=25$
                break;
            }
            case 26 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getValue()); //$JR_EXPR_ID=26$
                break;
            }
            case 27 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=27$
                break;
            }
            case 28 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getValue())+"    / "; //$JR_EXPR_ID=28$
                break;
            }
            case 29 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=29$
                break;
            }
            case 30 : 
            {
                value = "Emitido" //$JR_EXPR_ID=30$
    + (((java.lang.String)parameter_USUARIO_LOGADO.getValue()) != null ? " por " + ((java.lang.String)parameter_USUARIO_LOGADO.getValue()) : "") //$JR_EXPR_ID=30$
    + " em " + Data.formatarComTimezone(Data.getDataAtual()) //$JR_EXPR_ID=30$
    + " | " + ((java.lang.String)parameter_SISTEMA.getValue()) + " v" + ((java.lang.String)parameter_VERSAO_SISTEMA.getValue()) + " - CELK SISTEMAS LTDA"; //$JR_EXPR_ID=30$
                break;
            }
            case 31 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_DIRETOR_TECNICO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=31$
                break;
            }
            case 32 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()) != null //$JR_EXPR_ID=32$
? ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()).replaceAll("-", "\n") //$JR_EXPR_ID=32$
: null; //$JR_EXPR_ID=32$
                break;
            }
            case 33 : 
            {
                value = ((java.lang.String)field_chaveValidacao.getValue()) != null; //$JR_EXPR_ID=33$
                break;
            }
            case 34 : 
            {
                value = Bundle.getStringApplication("rotulo_chave_chave") + ": " + ((java.lang.String)field_chaveValidacao.getValue()); //$JR_EXPR_ID=34$
                break;
            }
            case 35 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento)field_solicitacaoAgendamento.getValue()) != null && ((br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento)field_solicitacaoAgendamento.getValue()).getCodigo() != null; //$JR_EXPR_ID=35$
                break;
            }
            case 36 : 
            {
                value = Bundle.getStringApplication("rotulo_chave_numero_solicitacao") + ": " + ((br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento)field_solicitacaoAgendamento.getValue()).getCodigo(); //$JR_EXPR_ID=36$
                break;
            }
            case 37 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=37$
                break;
            }
            case 38 : 
            {
                value = ((br.com.ksisolucoes.vo.basico.Empresa)field_localAgendamento.getValue()).getEnderecoCidadeBairroFormatado(); //$JR_EXPR_ID=38$
                break;
            }
            case 39 : 
            {
                value = Bundle.getStringApplication("rotulo_dados_agendamento"); //$JR_EXPR_ID=39$
                break;
            }
            case 40 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=40$
                break;
            }
            case 41 : 
            {
                value = Bundle.getStringApplication("rotulo_local"); //$JR_EXPR_ID=41$
                break;
            }
            case 42 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=42$
                break;
            }
            case 43 : 
            {
                value = Bundle.getStringApplication("rotulo_endereco"); //$JR_EXPR_ID=43$
                break;
            }
            case 44 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=44$
                break;
            }
            case 45 : 
            {
                value = ((br.com.ksisolucoes.vo.basico.Empresa)field_localAgendamento.getValue()).getDescricao(); //$JR_EXPR_ID=45$
                break;
            }
            case 46 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=46$
                break;
            }
            case 47 : 
            {
                value = ((br.com.ksisolucoes.vo.basico.Empresa)field_localAgendamento.getValue()).getTelefoneFormatado(); //$JR_EXPR_ID=47$
                break;
            }
            case 48 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=48$
                break;
            }
            case 49 : 
            {
                value = Bundle.getStringApplication("rotulo_telefone"); //$JR_EXPR_ID=49$
                break;
            }
            case 50 : 
            {
                value = msg("{0} - CRM: {1}/{2}", ((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getValue()).getNome(), (((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getValue()).getNumeroRegistro() == null ? "" : ((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getValue()).getNumeroRegistro()), (((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getValue()).getUnidadeFederacaoConselhoRegistro() == null ? "" : ((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getValue()).getUnidadeFederacaoConselhoRegistro())); //$JR_EXPR_ID=50$
                break;
            }
            case 51 : 
            {
                value = Bundle.getStringApplication("rotulo_profissional"); //$JR_EXPR_ID=51$
                break;
            }
            case 52 : 
            {
                value = Bundle.getStringApplication("rotulo_data"); //$JR_EXPR_ID=52$
                break;
            }
            case 53 : 
            {
                value = Bundle.getStringApplication("rotulo_procedimento"); //$JR_EXPR_ID=53$
                break;
            }
            case 54 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento)field_tipoProcedimento.getValue()).getTipoProcedimentoClassificacao().getDescricao()+"/"+((br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento)field_procedimento.getValue()).getDescricao(); //$JR_EXPR_ID=54$
                break;
            }
            case 55 : 
            {
                value = ((java.lang.String)field_nomePaciente.getValue()); //$JR_EXPR_ID=55$
                break;
            }
            case 56 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getDescricaoIdade(); //$JR_EXPR_ID=56$
                break;
            }
            case 57 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getDataNascimentoFormatado(); //$JR_EXPR_ID=57$
                break;
            }
            case 58 : 
            {
                value = Bundle.getStringApplication("rotulo_data_nascimento"); //$JR_EXPR_ID=58$
                break;
            }
            case 59 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getSexoFormatado(); //$JR_EXPR_ID=59$
                break;
            }
            case 60 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getTelefone() != null //$JR_EXPR_ID=60$
    ? //$JR_EXPR_ID=60$
        ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getTelefoneFormatado() //$JR_EXPR_ID=60$
    : //$JR_EXPR_ID=60$
        ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getCelular() != null //$JR_EXPR_ID=60$
            ? //$JR_EXPR_ID=60$
                ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getCelularFormatado() //$JR_EXPR_ID=60$
            : //$JR_EXPR_ID=60$
                null; //$JR_EXPR_ID=60$
                break;
            }
            case 61 : 
            {
                value = Bundle.getStringApplication("rotulo_dados_paciente"); //$JR_EXPR_ID=61$
                break;
            }
            case 62 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns)field_usuarioCadsusCns.getValue()).getNumeroCartaoFormatado(); //$JR_EXPR_ID=62$
                break;
            }
            case 63 : 
            {
                value = Bundle.getStringApplication("rotulo_telefone"); //$JR_EXPR_ID=63$
                break;
            }
            case 64 : 
            {
                value = Bundle.getStringApplication("rotulo_sexo"); //$JR_EXPR_ID=64$
                break;
            }
            case 65 : 
            {
                value = Bundle.getStringApplication("rotulo_mae"); //$JR_EXPR_ID=65$
                break;
            }
            case 66 : 
            {
                value = Bundle.getStringApplication("rotulo_idade"); //$JR_EXPR_ID=66$
                break;
            }
            case 67 : 
            {
                value = Bundle.getStringApplication("rotulo_nome"); //$JR_EXPR_ID=67$
                break;
            }
            case 68 : 
            {
                value = Bundle.getStringApplication("rotulo_cns"); //$JR_EXPR_ID=68$
                break;
            }
            case 69 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getNomeMae(); //$JR_EXPR_ID=69$
                break;
            }
            case 70 : 
            {
                value = ((java.util.List)field_agendaGradeAtendimentoHorarioList.getValue()).size() > 1; //$JR_EXPR_ID=70$
                break;
            }
            case 71 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_agendaGradeAtendimentoHorarioList.getValue())); //$JR_EXPR_ID=71$
                break;
            }
            case 72 : 
            {
                value = "/br/com/ksisolucoes/report/agendamento/jrxml/sub_relatorio_impressao_comprovante_agendamento_sem_solicitacao.jasper"; //$JR_EXPR_ID=72$
                break;
            }
            case 73 : 
            {
                value = Bundle.getStringApplication("rotulo_tipo_procedimento"); //$JR_EXPR_ID=73$
                break;
            }
            case 74 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento)field_tipoProcedimento.getValue()).getDescricao(); //$JR_EXPR_ID=74$
                break;
            }
            case 75 : 
            {
                value = CollectionUtils.isNotNullEmpty(((java.util.List)field_exameList.getValue())); //$JR_EXPR_ID=75$
                break;
            }
            case 76 : 
            {
                value = Bundle.getStringApplication("rotulo_exames"); //$JR_EXPR_ID=76$
                break;
            }
            case 77 : 
            {
                value = CollectionUtils.isNotNullEmpty(((java.util.List)field_exameList.getValue())); //$JR_EXPR_ID=77$
                break;
            }
            case 78 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_exameList.getValue())); //$JR_EXPR_ID=78$
                break;
            }
            case 79 : 
            {
                value = ((java.util.List)field_agendaGradeAtendimentoHorarioList.getValue()).size() == 1; //$JR_EXPR_ID=79$
                break;
            }
            case 80 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_agendaGradeAtendimentoHorarioList.getValue())); //$JR_EXPR_ID=80$
                break;
            }
            case 81 : 
            {
                value = "/br/com/ksisolucoes/report/agendamento/jrxml/sub_relatorio_impressao_comprovante_agendamento_sem_solicitacao_formatacao_grande.jasper"; //$JR_EXPR_ID=81$
                break;
            }
            case 82 : 
            {
                value = Bundle.getStringApplication("rotulo_endereco"); //$JR_EXPR_ID=82$
                break;
            }
            case 83 : 
            {
                value = Bundle.getStringApplication("rotulo_microarea"); //$JR_EXPR_ID=83$
                break;
            }
            case 84 : 
            {
                value = ((br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario)field_agendaGradeAtendimentoHorario.getValue()).getUsuarioCadsus().getEnderecoUsuarioCadsus().getEnderecoComplementoFormatadoComCidade(); //$JR_EXPR_ID=84$
                break;
            }
            case 85 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio)field_enderecoDomicilio.getValue()).getNumeroFamilia(); //$JR_EXPR_ID=85$
                break;
            }
            case 86 : 
            {
                value = Bundle.getStringApplication("rotulo_nr_familia_abv"); //$JR_EXPR_ID=86$
                break;
            }
            case 87 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio)field_enderecoDomicilio.getValue()).getEquipeMicroArea().getMicroArea(); //$JR_EXPR_ID=87$
                break;
            }
            case 88 : 
            {
                value = ((java.lang.String)field_recomendacoes.getValue()); //$JR_EXPR_ID=88$
                break;
            }
            case 89 : 
            {
                value = Bundle.getStringApplication("rotulo_usuario")+": "; //$JR_EXPR_ID=89$
                break;
            }
            case 90 : 
            {
                value = ((java.lang.String)parameter_nomeUsuario.getValue()); //$JR_EXPR_ID=90$
                break;
            }
            case 91 : 
            {
                value = Bundle.getStringApplication("rotulo_observacao"); //$JR_EXPR_ID=91$
                break;
            }
            case 92 : 
            {
                value = ((java.lang.String)field_preparacaoExames.getValue()) != null; //$JR_EXPR_ID=92$
                break;
            }
            case 93 : 
            {
                value = ((java.lang.String)field_preparacaoExames.getValue()) != null; //$JR_EXPR_ID=93$
                break;
            }
            case 94 : 
            {
                value = Bundle.getStringApplication("rotulo_preparacao"); //$JR_EXPR_ID=94$
                break;
            }
            case 95 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_preparacaoExameList.getValue())); //$JR_EXPR_ID=95$
                break;
            }
            case 96 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=96$
                break;
            }
            case 97 : 
            {
                value = ((java.lang.String)parameter_CIDADE_UNIDADE.getValue()) + " - " + ((java.lang.String)parameter_UF_UNIDADE.getValue()) //$JR_EXPR_ID=97$
+ (((java.lang.String)parameter_FONE_UNIDADE.getValue()) == null //$JR_EXPR_ID=97$
    ? "" //$JR_EXPR_ID=97$
    : " | " + ((java.lang.String)parameter_FONE_UNIDADE.getValue())); //$JR_EXPR_ID=97$
                break;
            }
            case 98 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=98$
                break;
            }
            case 99 : 
            {
                value = ((java.lang.String)parameter_RUA_UNIDADE.getValue()) //$JR_EXPR_ID=99$
    + (((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) != null ? ", " + ((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) : "") //$JR_EXPR_ID=99$
    + " - " + ((java.lang.String)parameter_BAIRRO_UNIDADE.getValue()) //$JR_EXPR_ID=99$
    + (((java.lang.String)parameter_CEP_UNIDADE.getValue()) != null ? " - CEP " + ((java.lang.String)parameter_CEP_UNIDADE.getValue()) : ""); //$JR_EXPR_ID=99$
                break;
            }
           default :
           {
           }
        }
        
        return value;
    }


    /**
     *
     */
    public Object evaluateOld(int id) throws Throwable
    {
        Object value = null;

        switch (id)
        {
            case 0 : 
            {
                value = null; //$JR_EXPR_ID=0$
                break;
            }
            case 1 : 
            {
                value = false; //$JR_EXPR_ID=1$
                break;
            }
            case 2 : 
            {
                value = false; //$JR_EXPR_ID=2$
                break;
            }
            case 3 : 
            {
                value = false; //$JR_EXPR_ID=3$
                break;
            }
            case 4 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=4$
                break;
            }
            case 5 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=5$
                break;
            }
            case 6 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=6$
                break;
            }
            case 7 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=7$
                break;
            }
            case 8 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=8$
                break;
            }
            case 9 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=9$
                break;
            }
            case 10 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=10$
                break;
            }
            case 11 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=11$
                break;
            }
            case 12 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=12$
                break;
            }
            case 13 : 
            {
                value = ((java.lang.String)parameter_DESC_CABECALHO_PADRAO.getValue()); //$JR_EXPR_ID=13$
                break;
            }
            case 14 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=14$
                break;
            }
            case 15 : 
            {
                value = ((java.lang.String)parameter_UNIDADE_ATENDIMENTO.getValue()); //$JR_EXPR_ID=15$
                break;
            }
            case 16 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=16$
                break;
            }
            case 17 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=17$
                break;
            }
            case 18 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_1.getValue()); //$JR_EXPR_ID=18$
                break;
            }
            case 19 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=19$
                break;
            }
            case 20 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_2.getValue()); //$JR_EXPR_ID=20$
                break;
            }
            case 21 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && (!((java.lang.Boolean)parameter_EXIBIR_TITULO_PRIMEIRA_PAGINA.getValue()) || ((java.lang.Integer)variable_PAGE_NUMBER.getOldValue()) == 1); //$JR_EXPR_ID=21$
                break;
            }
            case 22 : 
            {
                value = ((java.lang.String)parameter_TITULO_REPORT.getValue()); //$JR_EXPR_ID=22$
                break;
            }
            case 23 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=23$
                break;
            }
            case 24 : 
            {
                value = ((java.lang.String)parameter_CAMINHO_IMAGEM_PADRAO.getValue()); //$JR_EXPR_ID=24$
                break;
            }
            case 25 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=25$
                break;
            }
            case 26 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getOldValue()); //$JR_EXPR_ID=26$
                break;
            }
            case 27 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=27$
                break;
            }
            case 28 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getOldValue())+"    / "; //$JR_EXPR_ID=28$
                break;
            }
            case 29 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=29$
                break;
            }
            case 30 : 
            {
                value = "Emitido" //$JR_EXPR_ID=30$
    + (((java.lang.String)parameter_USUARIO_LOGADO.getValue()) != null ? " por " + ((java.lang.String)parameter_USUARIO_LOGADO.getValue()) : "") //$JR_EXPR_ID=30$
    + " em " + Data.formatarComTimezone(Data.getDataAtual()) //$JR_EXPR_ID=30$
    + " | " + ((java.lang.String)parameter_SISTEMA.getValue()) + " v" + ((java.lang.String)parameter_VERSAO_SISTEMA.getValue()) + " - CELK SISTEMAS LTDA"; //$JR_EXPR_ID=30$
                break;
            }
            case 31 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_DIRETOR_TECNICO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=31$
                break;
            }
            case 32 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()) != null //$JR_EXPR_ID=32$
? ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()).replaceAll("-", "\n") //$JR_EXPR_ID=32$
: null; //$JR_EXPR_ID=32$
                break;
            }
            case 33 : 
            {
                value = ((java.lang.String)field_chaveValidacao.getOldValue()) != null; //$JR_EXPR_ID=33$
                break;
            }
            case 34 : 
            {
                value = Bundle.getStringApplication("rotulo_chave_chave") + ": " + ((java.lang.String)field_chaveValidacao.getOldValue()); //$JR_EXPR_ID=34$
                break;
            }
            case 35 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento)field_solicitacaoAgendamento.getOldValue()) != null && ((br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento)field_solicitacaoAgendamento.getOldValue()).getCodigo() != null; //$JR_EXPR_ID=35$
                break;
            }
            case 36 : 
            {
                value = Bundle.getStringApplication("rotulo_chave_numero_solicitacao") + ": " + ((br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento)field_solicitacaoAgendamento.getOldValue()).getCodigo(); //$JR_EXPR_ID=36$
                break;
            }
            case 37 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=37$
                break;
            }
            case 38 : 
            {
                value = ((br.com.ksisolucoes.vo.basico.Empresa)field_localAgendamento.getOldValue()).getEnderecoCidadeBairroFormatado(); //$JR_EXPR_ID=38$
                break;
            }
            case 39 : 
            {
                value = Bundle.getStringApplication("rotulo_dados_agendamento"); //$JR_EXPR_ID=39$
                break;
            }
            case 40 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=40$
                break;
            }
            case 41 : 
            {
                value = Bundle.getStringApplication("rotulo_local"); //$JR_EXPR_ID=41$
                break;
            }
            case 42 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=42$
                break;
            }
            case 43 : 
            {
                value = Bundle.getStringApplication("rotulo_endereco"); //$JR_EXPR_ID=43$
                break;
            }
            case 44 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=44$
                break;
            }
            case 45 : 
            {
                value = ((br.com.ksisolucoes.vo.basico.Empresa)field_localAgendamento.getOldValue()).getDescricao(); //$JR_EXPR_ID=45$
                break;
            }
            case 46 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=46$
                break;
            }
            case 47 : 
            {
                value = ((br.com.ksisolucoes.vo.basico.Empresa)field_localAgendamento.getOldValue()).getTelefoneFormatado(); //$JR_EXPR_ID=47$
                break;
            }
            case 48 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=48$
                break;
            }
            case 49 : 
            {
                value = Bundle.getStringApplication("rotulo_telefone"); //$JR_EXPR_ID=49$
                break;
            }
            case 50 : 
            {
                value = msg("{0} - CRM: {1}/{2}", ((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getOldValue()).getNome(), (((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getOldValue()).getNumeroRegistro() == null ? "" : ((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getOldValue()).getNumeroRegistro()), (((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getOldValue()).getUnidadeFederacaoConselhoRegistro() == null ? "" : ((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getOldValue()).getUnidadeFederacaoConselhoRegistro())); //$JR_EXPR_ID=50$
                break;
            }
            case 51 : 
            {
                value = Bundle.getStringApplication("rotulo_profissional"); //$JR_EXPR_ID=51$
                break;
            }
            case 52 : 
            {
                value = Bundle.getStringApplication("rotulo_data"); //$JR_EXPR_ID=52$
                break;
            }
            case 53 : 
            {
                value = Bundle.getStringApplication("rotulo_procedimento"); //$JR_EXPR_ID=53$
                break;
            }
            case 54 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento)field_tipoProcedimento.getOldValue()).getTipoProcedimentoClassificacao().getDescricao()+"/"+((br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento)field_procedimento.getOldValue()).getDescricao(); //$JR_EXPR_ID=54$
                break;
            }
            case 55 : 
            {
                value = ((java.lang.String)field_nomePaciente.getOldValue()); //$JR_EXPR_ID=55$
                break;
            }
            case 56 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getOldValue()).getDescricaoIdade(); //$JR_EXPR_ID=56$
                break;
            }
            case 57 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getOldValue()).getDataNascimentoFormatado(); //$JR_EXPR_ID=57$
                break;
            }
            case 58 : 
            {
                value = Bundle.getStringApplication("rotulo_data_nascimento"); //$JR_EXPR_ID=58$
                break;
            }
            case 59 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getOldValue()).getSexoFormatado(); //$JR_EXPR_ID=59$
                break;
            }
            case 60 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getOldValue()).getTelefone() != null //$JR_EXPR_ID=60$
    ? //$JR_EXPR_ID=60$
        ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getOldValue()).getTelefoneFormatado() //$JR_EXPR_ID=60$
    : //$JR_EXPR_ID=60$
        ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getOldValue()).getCelular() != null //$JR_EXPR_ID=60$
            ? //$JR_EXPR_ID=60$
                ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getOldValue()).getCelularFormatado() //$JR_EXPR_ID=60$
            : //$JR_EXPR_ID=60$
                null; //$JR_EXPR_ID=60$
                break;
            }
            case 61 : 
            {
                value = Bundle.getStringApplication("rotulo_dados_paciente"); //$JR_EXPR_ID=61$
                break;
            }
            case 62 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns)field_usuarioCadsusCns.getOldValue()).getNumeroCartaoFormatado(); //$JR_EXPR_ID=62$
                break;
            }
            case 63 : 
            {
                value = Bundle.getStringApplication("rotulo_telefone"); //$JR_EXPR_ID=63$
                break;
            }
            case 64 : 
            {
                value = Bundle.getStringApplication("rotulo_sexo"); //$JR_EXPR_ID=64$
                break;
            }
            case 65 : 
            {
                value = Bundle.getStringApplication("rotulo_mae"); //$JR_EXPR_ID=65$
                break;
            }
            case 66 : 
            {
                value = Bundle.getStringApplication("rotulo_idade"); //$JR_EXPR_ID=66$
                break;
            }
            case 67 : 
            {
                value = Bundle.getStringApplication("rotulo_nome"); //$JR_EXPR_ID=67$
                break;
            }
            case 68 : 
            {
                value = Bundle.getStringApplication("rotulo_cns"); //$JR_EXPR_ID=68$
                break;
            }
            case 69 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getOldValue()).getNomeMae(); //$JR_EXPR_ID=69$
                break;
            }
            case 70 : 
            {
                value = ((java.util.List)field_agendaGradeAtendimentoHorarioList.getOldValue()).size() > 1; //$JR_EXPR_ID=70$
                break;
            }
            case 71 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_agendaGradeAtendimentoHorarioList.getOldValue())); //$JR_EXPR_ID=71$
                break;
            }
            case 72 : 
            {
                value = "/br/com/ksisolucoes/report/agendamento/jrxml/sub_relatorio_impressao_comprovante_agendamento_sem_solicitacao.jasper"; //$JR_EXPR_ID=72$
                break;
            }
            case 73 : 
            {
                value = Bundle.getStringApplication("rotulo_tipo_procedimento"); //$JR_EXPR_ID=73$
                break;
            }
            case 74 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento)field_tipoProcedimento.getOldValue()).getDescricao(); //$JR_EXPR_ID=74$
                break;
            }
            case 75 : 
            {
                value = CollectionUtils.isNotNullEmpty(((java.util.List)field_exameList.getOldValue())); //$JR_EXPR_ID=75$
                break;
            }
            case 76 : 
            {
                value = Bundle.getStringApplication("rotulo_exames"); //$JR_EXPR_ID=76$
                break;
            }
            case 77 : 
            {
                value = CollectionUtils.isNotNullEmpty(((java.util.List)field_exameList.getOldValue())); //$JR_EXPR_ID=77$
                break;
            }
            case 78 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_exameList.getOldValue())); //$JR_EXPR_ID=78$
                break;
            }
            case 79 : 
            {
                value = ((java.util.List)field_agendaGradeAtendimentoHorarioList.getOldValue()).size() == 1; //$JR_EXPR_ID=79$
                break;
            }
            case 80 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_agendaGradeAtendimentoHorarioList.getOldValue())); //$JR_EXPR_ID=80$
                break;
            }
            case 81 : 
            {
                value = "/br/com/ksisolucoes/report/agendamento/jrxml/sub_relatorio_impressao_comprovante_agendamento_sem_solicitacao_formatacao_grande.jasper"; //$JR_EXPR_ID=81$
                break;
            }
            case 82 : 
            {
                value = Bundle.getStringApplication("rotulo_endereco"); //$JR_EXPR_ID=82$
                break;
            }
            case 83 : 
            {
                value = Bundle.getStringApplication("rotulo_microarea"); //$JR_EXPR_ID=83$
                break;
            }
            case 84 : 
            {
                value = ((br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario)field_agendaGradeAtendimentoHorario.getOldValue()).getUsuarioCadsus().getEnderecoUsuarioCadsus().getEnderecoComplementoFormatadoComCidade(); //$JR_EXPR_ID=84$
                break;
            }
            case 85 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio)field_enderecoDomicilio.getOldValue()).getNumeroFamilia(); //$JR_EXPR_ID=85$
                break;
            }
            case 86 : 
            {
                value = Bundle.getStringApplication("rotulo_nr_familia_abv"); //$JR_EXPR_ID=86$
                break;
            }
            case 87 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio)field_enderecoDomicilio.getOldValue()).getEquipeMicroArea().getMicroArea(); //$JR_EXPR_ID=87$
                break;
            }
            case 88 : 
            {
                value = ((java.lang.String)field_recomendacoes.getOldValue()); //$JR_EXPR_ID=88$
                break;
            }
            case 89 : 
            {
                value = Bundle.getStringApplication("rotulo_usuario")+": "; //$JR_EXPR_ID=89$
                break;
            }
            case 90 : 
            {
                value = ((java.lang.String)parameter_nomeUsuario.getValue()); //$JR_EXPR_ID=90$
                break;
            }
            case 91 : 
            {
                value = Bundle.getStringApplication("rotulo_observacao"); //$JR_EXPR_ID=91$
                break;
            }
            case 92 : 
            {
                value = ((java.lang.String)field_preparacaoExames.getOldValue()) != null; //$JR_EXPR_ID=92$
                break;
            }
            case 93 : 
            {
                value = ((java.lang.String)field_preparacaoExames.getOldValue()) != null; //$JR_EXPR_ID=93$
                break;
            }
            case 94 : 
            {
                value = Bundle.getStringApplication("rotulo_preparacao"); //$JR_EXPR_ID=94$
                break;
            }
            case 95 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_preparacaoExameList.getOldValue())); //$JR_EXPR_ID=95$
                break;
            }
            case 96 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=96$
                break;
            }
            case 97 : 
            {
                value = ((java.lang.String)parameter_CIDADE_UNIDADE.getValue()) + " - " + ((java.lang.String)parameter_UF_UNIDADE.getValue()) //$JR_EXPR_ID=97$
+ (((java.lang.String)parameter_FONE_UNIDADE.getValue()) == null //$JR_EXPR_ID=97$
    ? "" //$JR_EXPR_ID=97$
    : " | " + ((java.lang.String)parameter_FONE_UNIDADE.getValue())); //$JR_EXPR_ID=97$
                break;
            }
            case 98 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=98$
                break;
            }
            case 99 : 
            {
                value = ((java.lang.String)parameter_RUA_UNIDADE.getValue()) //$JR_EXPR_ID=99$
    + (((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) != null ? ", " + ((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) : "") //$JR_EXPR_ID=99$
    + " - " + ((java.lang.String)parameter_BAIRRO_UNIDADE.getValue()) //$JR_EXPR_ID=99$
    + (((java.lang.String)parameter_CEP_UNIDADE.getValue()) != null ? " - CEP " + ((java.lang.String)parameter_CEP_UNIDADE.getValue()) : ""); //$JR_EXPR_ID=99$
                break;
            }
           default :
           {
           }
        }
        
        return value;
    }


    /**
     *
     */
    public Object evaluateEstimated(int id) throws Throwable
    {
        Object value = null;

        switch (id)
        {
            case 0 : 
            {
                value = null; //$JR_EXPR_ID=0$
                break;
            }
            case 1 : 
            {
                value = false; //$JR_EXPR_ID=1$
                break;
            }
            case 2 : 
            {
                value = false; //$JR_EXPR_ID=2$
                break;
            }
            case 3 : 
            {
                value = false; //$JR_EXPR_ID=3$
                break;
            }
            case 4 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=4$
                break;
            }
            case 5 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=5$
                break;
            }
            case 6 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=6$
                break;
            }
            case 7 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=7$
                break;
            }
            case 8 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=8$
                break;
            }
            case 9 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=9$
                break;
            }
            case 10 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=10$
                break;
            }
            case 11 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=11$
                break;
            }
            case 12 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=12$
                break;
            }
            case 13 : 
            {
                value = ((java.lang.String)parameter_DESC_CABECALHO_PADRAO.getValue()); //$JR_EXPR_ID=13$
                break;
            }
            case 14 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=14$
                break;
            }
            case 15 : 
            {
                value = ((java.lang.String)parameter_UNIDADE_ATENDIMENTO.getValue()); //$JR_EXPR_ID=15$
                break;
            }
            case 16 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=16$
                break;
            }
            case 17 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=17$
                break;
            }
            case 18 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_1.getValue()); //$JR_EXPR_ID=18$
                break;
            }
            case 19 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=19$
                break;
            }
            case 20 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_2.getValue()); //$JR_EXPR_ID=20$
                break;
            }
            case 21 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && (!((java.lang.Boolean)parameter_EXIBIR_TITULO_PRIMEIRA_PAGINA.getValue()) || ((java.lang.Integer)variable_PAGE_NUMBER.getEstimatedValue()) == 1); //$JR_EXPR_ID=21$
                break;
            }
            case 22 : 
            {
                value = ((java.lang.String)parameter_TITULO_REPORT.getValue()); //$JR_EXPR_ID=22$
                break;
            }
            case 23 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=23$
                break;
            }
            case 24 : 
            {
                value = ((java.lang.String)parameter_CAMINHO_IMAGEM_PADRAO.getValue()); //$JR_EXPR_ID=24$
                break;
            }
            case 25 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=25$
                break;
            }
            case 26 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getEstimatedValue()); //$JR_EXPR_ID=26$
                break;
            }
            case 27 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=27$
                break;
            }
            case 28 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getEstimatedValue())+"    / "; //$JR_EXPR_ID=28$
                break;
            }
            case 29 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=29$
                break;
            }
            case 30 : 
            {
                value = "Emitido" //$JR_EXPR_ID=30$
    + (((java.lang.String)parameter_USUARIO_LOGADO.getValue()) != null ? " por " + ((java.lang.String)parameter_USUARIO_LOGADO.getValue()) : "") //$JR_EXPR_ID=30$
    + " em " + Data.formatarComTimezone(Data.getDataAtual()) //$JR_EXPR_ID=30$
    + " | " + ((java.lang.String)parameter_SISTEMA.getValue()) + " v" + ((java.lang.String)parameter_VERSAO_SISTEMA.getValue()) + " - CELK SISTEMAS LTDA"; //$JR_EXPR_ID=30$
                break;
            }
            case 31 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_DIRETOR_TECNICO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=31$
                break;
            }
            case 32 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()) != null //$JR_EXPR_ID=32$
? ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()).replaceAll("-", "\n") //$JR_EXPR_ID=32$
: null; //$JR_EXPR_ID=32$
                break;
            }
            case 33 : 
            {
                value = ((java.lang.String)field_chaveValidacao.getValue()) != null; //$JR_EXPR_ID=33$
                break;
            }
            case 34 : 
            {
                value = Bundle.getStringApplication("rotulo_chave_chave") + ": " + ((java.lang.String)field_chaveValidacao.getValue()); //$JR_EXPR_ID=34$
                break;
            }
            case 35 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento)field_solicitacaoAgendamento.getValue()) != null && ((br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento)field_solicitacaoAgendamento.getValue()).getCodigo() != null; //$JR_EXPR_ID=35$
                break;
            }
            case 36 : 
            {
                value = Bundle.getStringApplication("rotulo_chave_numero_solicitacao") + ": " + ((br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento)field_solicitacaoAgendamento.getValue()).getCodigo(); //$JR_EXPR_ID=36$
                break;
            }
            case 37 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=37$
                break;
            }
            case 38 : 
            {
                value = ((br.com.ksisolucoes.vo.basico.Empresa)field_localAgendamento.getValue()).getEnderecoCidadeBairroFormatado(); //$JR_EXPR_ID=38$
                break;
            }
            case 39 : 
            {
                value = Bundle.getStringApplication("rotulo_dados_agendamento"); //$JR_EXPR_ID=39$
                break;
            }
            case 40 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=40$
                break;
            }
            case 41 : 
            {
                value = Bundle.getStringApplication("rotulo_local"); //$JR_EXPR_ID=41$
                break;
            }
            case 42 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=42$
                break;
            }
            case 43 : 
            {
                value = Bundle.getStringApplication("rotulo_endereco"); //$JR_EXPR_ID=43$
                break;
            }
            case 44 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=44$
                break;
            }
            case 45 : 
            {
                value = ((br.com.ksisolucoes.vo.basico.Empresa)field_localAgendamento.getValue()).getDescricao(); //$JR_EXPR_ID=45$
                break;
            }
            case 46 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=46$
                break;
            }
            case 47 : 
            {
                value = ((br.com.ksisolucoes.vo.basico.Empresa)field_localAgendamento.getValue()).getTelefoneFormatado(); //$JR_EXPR_ID=47$
                break;
            }
            case 48 : 
            {
                value = !"H".equals(((java.lang.String)parameter_modelComprovanteAgendamento.getValue())); //$JR_EXPR_ID=48$
                break;
            }
            case 49 : 
            {
                value = Bundle.getStringApplication("rotulo_telefone"); //$JR_EXPR_ID=49$
                break;
            }
            case 50 : 
            {
                value = msg("{0} - CRM: {1}/{2}", ((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getValue()).getNome(), (((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getValue()).getNumeroRegistro() == null ? "" : ((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getValue()).getNumeroRegistro()), (((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getValue()).getUnidadeFederacaoConselhoRegistro() == null ? "" : ((br.com.ksisolucoes.vo.cadsus.Profissional)field_profissionalAgenda.getValue()).getUnidadeFederacaoConselhoRegistro())); //$JR_EXPR_ID=50$
                break;
            }
            case 51 : 
            {
                value = Bundle.getStringApplication("rotulo_profissional"); //$JR_EXPR_ID=51$
                break;
            }
            case 52 : 
            {
                value = Bundle.getStringApplication("rotulo_data"); //$JR_EXPR_ID=52$
                break;
            }
            case 53 : 
            {
                value = Bundle.getStringApplication("rotulo_procedimento"); //$JR_EXPR_ID=53$
                break;
            }
            case 54 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento)field_tipoProcedimento.getValue()).getTipoProcedimentoClassificacao().getDescricao()+"/"+((br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento)field_procedimento.getValue()).getDescricao(); //$JR_EXPR_ID=54$
                break;
            }
            case 55 : 
            {
                value = ((java.lang.String)field_nomePaciente.getValue()); //$JR_EXPR_ID=55$
                break;
            }
            case 56 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getDescricaoIdade(); //$JR_EXPR_ID=56$
                break;
            }
            case 57 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getDataNascimentoFormatado(); //$JR_EXPR_ID=57$
                break;
            }
            case 58 : 
            {
                value = Bundle.getStringApplication("rotulo_data_nascimento"); //$JR_EXPR_ID=58$
                break;
            }
            case 59 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getSexoFormatado(); //$JR_EXPR_ID=59$
                break;
            }
            case 60 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getTelefone() != null //$JR_EXPR_ID=60$
    ? //$JR_EXPR_ID=60$
        ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getTelefoneFormatado() //$JR_EXPR_ID=60$
    : //$JR_EXPR_ID=60$
        ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getCelular() != null //$JR_EXPR_ID=60$
            ? //$JR_EXPR_ID=60$
                ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getCelularFormatado() //$JR_EXPR_ID=60$
            : //$JR_EXPR_ID=60$
                null; //$JR_EXPR_ID=60$
                break;
            }
            case 61 : 
            {
                value = Bundle.getStringApplication("rotulo_dados_paciente"); //$JR_EXPR_ID=61$
                break;
            }
            case 62 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns)field_usuarioCadsusCns.getValue()).getNumeroCartaoFormatado(); //$JR_EXPR_ID=62$
                break;
            }
            case 63 : 
            {
                value = Bundle.getStringApplication("rotulo_telefone"); //$JR_EXPR_ID=63$
                break;
            }
            case 64 : 
            {
                value = Bundle.getStringApplication("rotulo_sexo"); //$JR_EXPR_ID=64$
                break;
            }
            case 65 : 
            {
                value = Bundle.getStringApplication("rotulo_mae"); //$JR_EXPR_ID=65$
                break;
            }
            case 66 : 
            {
                value = Bundle.getStringApplication("rotulo_idade"); //$JR_EXPR_ID=66$
                break;
            }
            case 67 : 
            {
                value = Bundle.getStringApplication("rotulo_nome"); //$JR_EXPR_ID=67$
                break;
            }
            case 68 : 
            {
                value = Bundle.getStringApplication("rotulo_cns"); //$JR_EXPR_ID=68$
                break;
            }
            case 69 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.UsuarioCadsus)field_usuarioCadsus.getValue()).getNomeMae(); //$JR_EXPR_ID=69$
                break;
            }
            case 70 : 
            {
                value = ((java.util.List)field_agendaGradeAtendimentoHorarioList.getValue()).size() > 1; //$JR_EXPR_ID=70$
                break;
            }
            case 71 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_agendaGradeAtendimentoHorarioList.getValue())); //$JR_EXPR_ID=71$
                break;
            }
            case 72 : 
            {
                value = "/br/com/ksisolucoes/report/agendamento/jrxml/sub_relatorio_impressao_comprovante_agendamento_sem_solicitacao.jasper"; //$JR_EXPR_ID=72$
                break;
            }
            case 73 : 
            {
                value = Bundle.getStringApplication("rotulo_tipo_procedimento"); //$JR_EXPR_ID=73$
                break;
            }
            case 74 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento)field_tipoProcedimento.getValue()).getDescricao(); //$JR_EXPR_ID=74$
                break;
            }
            case 75 : 
            {
                value = CollectionUtils.isNotNullEmpty(((java.util.List)field_exameList.getValue())); //$JR_EXPR_ID=75$
                break;
            }
            case 76 : 
            {
                value = Bundle.getStringApplication("rotulo_exames"); //$JR_EXPR_ID=76$
                break;
            }
            case 77 : 
            {
                value = CollectionUtils.isNotNullEmpty(((java.util.List)field_exameList.getValue())); //$JR_EXPR_ID=77$
                break;
            }
            case 78 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_exameList.getValue())); //$JR_EXPR_ID=78$
                break;
            }
            case 79 : 
            {
                value = ((java.util.List)field_agendaGradeAtendimentoHorarioList.getValue()).size() == 1; //$JR_EXPR_ID=79$
                break;
            }
            case 80 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_agendaGradeAtendimentoHorarioList.getValue())); //$JR_EXPR_ID=80$
                break;
            }
            case 81 : 
            {
                value = "/br/com/ksisolucoes/report/agendamento/jrxml/sub_relatorio_impressao_comprovante_agendamento_sem_solicitacao_formatacao_grande.jasper"; //$JR_EXPR_ID=81$
                break;
            }
            case 82 : 
            {
                value = Bundle.getStringApplication("rotulo_endereco"); //$JR_EXPR_ID=82$
                break;
            }
            case 83 : 
            {
                value = Bundle.getStringApplication("rotulo_microarea"); //$JR_EXPR_ID=83$
                break;
            }
            case 84 : 
            {
                value = ((br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario)field_agendaGradeAtendimentoHorario.getValue()).getUsuarioCadsus().getEnderecoUsuarioCadsus().getEnderecoComplementoFormatadoComCidade(); //$JR_EXPR_ID=84$
                break;
            }
            case 85 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio)field_enderecoDomicilio.getValue()).getNumeroFamilia(); //$JR_EXPR_ID=85$
                break;
            }
            case 86 : 
            {
                value = Bundle.getStringApplication("rotulo_nr_familia_abv"); //$JR_EXPR_ID=86$
                break;
            }
            case 87 : 
            {
                value = ((br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio)field_enderecoDomicilio.getValue()).getEquipeMicroArea().getMicroArea(); //$JR_EXPR_ID=87$
                break;
            }
            case 88 : 
            {
                value = ((java.lang.String)field_recomendacoes.getValue()); //$JR_EXPR_ID=88$
                break;
            }
            case 89 : 
            {
                value = Bundle.getStringApplication("rotulo_usuario")+": "; //$JR_EXPR_ID=89$
                break;
            }
            case 90 : 
            {
                value = ((java.lang.String)parameter_nomeUsuario.getValue()); //$JR_EXPR_ID=90$
                break;
            }
            case 91 : 
            {
                value = Bundle.getStringApplication("rotulo_observacao"); //$JR_EXPR_ID=91$
                break;
            }
            case 92 : 
            {
                value = ((java.lang.String)field_preparacaoExames.getValue()) != null; //$JR_EXPR_ID=92$
                break;
            }
            case 93 : 
            {
                value = ((java.lang.String)field_preparacaoExames.getValue()) != null; //$JR_EXPR_ID=93$
                break;
            }
            case 94 : 
            {
                value = Bundle.getStringApplication("rotulo_preparacao"); //$JR_EXPR_ID=94$
                break;
            }
            case 95 : 
            {
                value = new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(((java.util.List)field_preparacaoExameList.getValue())); //$JR_EXPR_ID=95$
                break;
            }
            case 96 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=96$
                break;
            }
            case 97 : 
            {
                value = ((java.lang.String)parameter_CIDADE_UNIDADE.getValue()) + " - " + ((java.lang.String)parameter_UF_UNIDADE.getValue()) //$JR_EXPR_ID=97$
+ (((java.lang.String)parameter_FONE_UNIDADE.getValue()) == null //$JR_EXPR_ID=97$
    ? "" //$JR_EXPR_ID=97$
    : " | " + ((java.lang.String)parameter_FONE_UNIDADE.getValue())); //$JR_EXPR_ID=97$
                break;
            }
            case 98 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=98$
                break;
            }
            case 99 : 
            {
                value = ((java.lang.String)parameter_RUA_UNIDADE.getValue()) //$JR_EXPR_ID=99$
    + (((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) != null ? ", " + ((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) : "") //$JR_EXPR_ID=99$
    + " - " + ((java.lang.String)parameter_BAIRRO_UNIDADE.getValue()) //$JR_EXPR_ID=99$
    + (((java.lang.String)parameter_CEP_UNIDADE.getValue()) != null ? " - CEP " + ((java.lang.String)parameter_CEP_UNIDADE.getValue()) : ""); //$JR_EXPR_ID=99$
                break;
            }
           default :
           {
           }
        }
        
        return value;
    }


}
