/*
 * Generated by JasperReports - 15/02/23 07:42
 */
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.fill.*;

import java.util.*;
import java.math.*;
import java.text.*;
import java.io.*;
import java.net.*;

import br.com.ksisolucoes.vo.basico.*;
import net.sf.jasperreports.engine.*;
import java.util.*;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.hospital.*;
import net.sf.jasperreports.engine.data.*;


/**
 *
 */
public class relatorio_impressao_espelho_aih_1676457762763_444763 extends JREvaluator
{


    /**
     *
     */
    private JRFillParameter parameter_EXIBIR_CABECALHO = null;
    private JRFillParameter parameter_REPORT_CONNECTION = null;
    private JRFillParameter parameter_VERSAO_SISTEMA = null;
    private JRFillParameter parameter_JASPER_REPORT = null;
    private JRFillParameter parameter_REPORT_TIME_ZONE = null;
    private JRFillParameter parameter_REPORT_TEMPLATES = null;
    private JRFillParameter parameter_TITULO_REPORT = null;
    private JRFillParameter parameter_EXIBIR_DIRETOR_TECNICO = null;
    private JRFillParameter parameter_REPORT_MAX_COUNT = null;
    private JRFillParameter parameter_REPORT_SCRIPTLET = null;
    private JRFillParameter parameter_EXIBIR_NUMERO_PAGINAS = null;
    private JRFillParameter parameter_REPORT_PARAMETERS_MAP = null;
    private JRFillParameter parameter_REPORT_RESOURCE_BUNDLE = null;
    private JRFillParameter parameter_CABECALHO_DIRETOR_TECNICO = null;
    private JRFillParameter parameter_REPORT_DATA_SOURCE = null;
    private JRFillParameter parameter_SORT_FIELDS = null;
    private JRFillParameter parameter_DESC_CABECALHO_PADRAO = null;
    private JRFillParameter parameter_UNIDADE_ATENDIMENTO = null;
    private JRFillParameter parameter_IS_IGNORE_PAGINATION = null;
    private JRFillParameter parameter_NUMERO_UNIDADE = null;
    private JRFillParameter parameter_codigo = null;
    private JRFillParameter parameter_FILTER = null;
    private JRFillParameter parameter_CAMINHO_IMAGEM_PADRAO = null;
    private JRFillParameter parameter_REPORT_LOCALE = null;
    private JRFillParameter parameter_EXIBIR_TITULO_PRIMEIRA_PAGINA = null;
    private JRFillParameter parameter_SISTEMA = null;
    private JRFillParameter parameter_RUA_UNIDADE = null;
    private JRFillParameter parameter_CIDADE_UNIDADE = null;
    private JRFillParameter parameter_REPORT_FILE_RESOLVER = null;
    private JRFillParameter parameter_BAIRRO_UNIDADE = null;
    private JRFillParameter parameter_USUARIO_LOGADO = null;
    private JRFillParameter parameter_REPORT_FORMAT_FACTORY = null;
    private JRFillParameter parameter_CEP_UNIDADE = null;
    private JRFillParameter parameter_EXIBIR_HORARIO = null;
    private JRFillParameter parameter_FONE_UNIDADE = null;
    private JRFillParameter parameter_UF_UNIDADE = null;
    private JRFillParameter parameter_REPORT_CONTEXT = null;
    private JRFillParameter parameter_REPORT_CLASS_LOADER = null;
    private JRFillParameter parameter_REPORT_URL_HANDLER_FACTORY = null;
    private JRFillParameter parameter_REPORT_VIRTUALIZER = null;
    private JRFillParameter parameter_EXIBIR_RODAPE = null;
    private JRFillParameter parameter_CABECALHO_ADICIONAL_2 = null;
    private JRFillParameter parameter_CABECALHO_ADICIONAL_1 = null;
    private JRFillField field_codigo = null;
    private JRFillField field_procedimentoSolicitadoCod = null;
    private JRFillField field_principaisResultadosProvasDiagnosticas = null;
    private JRFillField field_ocorrenciaDescricao = null;
    private JRFillField field_diagnosticoInicial = null;
    private JRFillField field_condicoesJustificamInternacao = null;
    private JRFillField field_usuarioCadSusNome = null;
    private JRFillField field_caraterInternacao = null;
    private JRFillField field_dataSolicitacao = null;
    private JRFillField field_nroDocProfSol = null;
    private JRFillField field_cidPrincipal = null;
    private JRFillField field_clinica = null;
    private JRFillField field_procedimentoSolicitadoDesc = null;
    private JRFillField field_nomeProfissionalSolicitante = null;
    private JRFillField field_dataCadastro = null;
    private JRFillField field_usuarioCadastro = null;
    private JRFillField field_principaisSinaisSintomasClinicos = null;
    private JRFillField field_status = null;
    private JRFillVariable variable_PAGE_NUMBER = null;
    private JRFillVariable variable_COLUMN_NUMBER = null;
    private JRFillVariable variable_REPORT_COUNT = null;
    private JRFillVariable variable_PAGE_COUNT = null;
    private JRFillVariable variable_COLUMN_COUNT = null;


    /**
     *
     */
    public void customizedInit(
        Map pm,
        Map fm,
        Map vm
        )
    {
        initParams(pm);
        initFields(fm);
        initVars(vm);
    }


    /**
     *
     */
    private void initParams(Map pm)
    {
        parameter_EXIBIR_CABECALHO = (JRFillParameter)pm.get("EXIBIR_CABECALHO");
        parameter_REPORT_CONNECTION = (JRFillParameter)pm.get("REPORT_CONNECTION");
        parameter_VERSAO_SISTEMA = (JRFillParameter)pm.get("VERSAO_SISTEMA");
        parameter_JASPER_REPORT = (JRFillParameter)pm.get("JASPER_REPORT");
        parameter_REPORT_TIME_ZONE = (JRFillParameter)pm.get("REPORT_TIME_ZONE");
        parameter_REPORT_TEMPLATES = (JRFillParameter)pm.get("REPORT_TEMPLATES");
        parameter_TITULO_REPORT = (JRFillParameter)pm.get("TITULO_REPORT");
        parameter_EXIBIR_DIRETOR_TECNICO = (JRFillParameter)pm.get("EXIBIR_DIRETOR_TECNICO");
        parameter_REPORT_MAX_COUNT = (JRFillParameter)pm.get("REPORT_MAX_COUNT");
        parameter_REPORT_SCRIPTLET = (JRFillParameter)pm.get("REPORT_SCRIPTLET");
        parameter_EXIBIR_NUMERO_PAGINAS = (JRFillParameter)pm.get("EXIBIR_NUMERO_PAGINAS");
        parameter_REPORT_PARAMETERS_MAP = (JRFillParameter)pm.get("REPORT_PARAMETERS_MAP");
        parameter_REPORT_RESOURCE_BUNDLE = (JRFillParameter)pm.get("REPORT_RESOURCE_BUNDLE");
        parameter_CABECALHO_DIRETOR_TECNICO = (JRFillParameter)pm.get("CABECALHO_DIRETOR_TECNICO");
        parameter_REPORT_DATA_SOURCE = (JRFillParameter)pm.get("REPORT_DATA_SOURCE");
        parameter_SORT_FIELDS = (JRFillParameter)pm.get("SORT_FIELDS");
        parameter_DESC_CABECALHO_PADRAO = (JRFillParameter)pm.get("DESC_CABECALHO_PADRAO");
        parameter_UNIDADE_ATENDIMENTO = (JRFillParameter)pm.get("UNIDADE_ATENDIMENTO");
        parameter_IS_IGNORE_PAGINATION = (JRFillParameter)pm.get("IS_IGNORE_PAGINATION");
        parameter_NUMERO_UNIDADE = (JRFillParameter)pm.get("NUMERO_UNIDADE");
        parameter_codigo = (JRFillParameter)pm.get("codigo");
        parameter_FILTER = (JRFillParameter)pm.get("FILTER");
        parameter_CAMINHO_IMAGEM_PADRAO = (JRFillParameter)pm.get("CAMINHO_IMAGEM_PADRAO");
        parameter_REPORT_LOCALE = (JRFillParameter)pm.get("REPORT_LOCALE");
        parameter_EXIBIR_TITULO_PRIMEIRA_PAGINA = (JRFillParameter)pm.get("EXIBIR_TITULO_PRIMEIRA_PAGINA");
        parameter_SISTEMA = (JRFillParameter)pm.get("SISTEMA");
        parameter_RUA_UNIDADE = (JRFillParameter)pm.get("RUA_UNIDADE");
        parameter_CIDADE_UNIDADE = (JRFillParameter)pm.get("CIDADE_UNIDADE");
        parameter_REPORT_FILE_RESOLVER = (JRFillParameter)pm.get("REPORT_FILE_RESOLVER");
        parameter_BAIRRO_UNIDADE = (JRFillParameter)pm.get("BAIRRO_UNIDADE");
        parameter_USUARIO_LOGADO = (JRFillParameter)pm.get("USUARIO_LOGADO");
        parameter_REPORT_FORMAT_FACTORY = (JRFillParameter)pm.get("REPORT_FORMAT_FACTORY");
        parameter_CEP_UNIDADE = (JRFillParameter)pm.get("CEP_UNIDADE");
        parameter_EXIBIR_HORARIO = (JRFillParameter)pm.get("EXIBIR_HORARIO");
        parameter_FONE_UNIDADE = (JRFillParameter)pm.get("FONE_UNIDADE");
        parameter_UF_UNIDADE = (JRFillParameter)pm.get("UF_UNIDADE");
        parameter_REPORT_CONTEXT = (JRFillParameter)pm.get("REPORT_CONTEXT");
        parameter_REPORT_CLASS_LOADER = (JRFillParameter)pm.get("REPORT_CLASS_LOADER");
        parameter_REPORT_URL_HANDLER_FACTORY = (JRFillParameter)pm.get("REPORT_URL_HANDLER_FACTORY");
        parameter_REPORT_VIRTUALIZER = (JRFillParameter)pm.get("REPORT_VIRTUALIZER");
        parameter_EXIBIR_RODAPE = (JRFillParameter)pm.get("EXIBIR_RODAPE");
        parameter_CABECALHO_ADICIONAL_2 = (JRFillParameter)pm.get("CABECALHO_ADICIONAL_2");
        parameter_CABECALHO_ADICIONAL_1 = (JRFillParameter)pm.get("CABECALHO_ADICIONAL_1");
    }


    /**
     *
     */
    private void initFields(Map fm)
    {
        field_codigo = (JRFillField)fm.get("codigo");
        field_procedimentoSolicitadoCod = (JRFillField)fm.get("procedimentoSolicitadoCod");
        field_principaisResultadosProvasDiagnosticas = (JRFillField)fm.get("principaisResultadosProvasDiagnosticas");
        field_ocorrenciaDescricao = (JRFillField)fm.get("ocorrenciaDescricao");
        field_diagnosticoInicial = (JRFillField)fm.get("diagnosticoInicial");
        field_condicoesJustificamInternacao = (JRFillField)fm.get("condicoesJustificamInternacao");
        field_usuarioCadSusNome = (JRFillField)fm.get("usuarioCadSusNome");
        field_caraterInternacao = (JRFillField)fm.get("caraterInternacao");
        field_dataSolicitacao = (JRFillField)fm.get("dataSolicitacao");
        field_nroDocProfSol = (JRFillField)fm.get("nroDocProfSol");
        field_cidPrincipal = (JRFillField)fm.get("cidPrincipal");
        field_clinica = (JRFillField)fm.get("clinica");
        field_procedimentoSolicitadoDesc = (JRFillField)fm.get("procedimentoSolicitadoDesc");
        field_nomeProfissionalSolicitante = (JRFillField)fm.get("nomeProfissionalSolicitante");
        field_dataCadastro = (JRFillField)fm.get("dataCadastro");
        field_usuarioCadastro = (JRFillField)fm.get("usuarioCadastro");
        field_principaisSinaisSintomasClinicos = (JRFillField)fm.get("principaisSinaisSintomasClinicos");
        field_status = (JRFillField)fm.get("status");
    }


    /**
     *
     */
    private void initVars(Map vm)
    {
        variable_PAGE_NUMBER = (JRFillVariable)vm.get("PAGE_NUMBER");
        variable_COLUMN_NUMBER = (JRFillVariable)vm.get("COLUMN_NUMBER");
        variable_REPORT_COUNT = (JRFillVariable)vm.get("REPORT_COUNT");
        variable_PAGE_COUNT = (JRFillVariable)vm.get("PAGE_COUNT");
        variable_COLUMN_COUNT = (JRFillVariable)vm.get("COLUMN_COUNT");
    }


    /**
     *
     */
    public Object evaluate(int id) throws Throwable
    {
        Object value = null;

        switch (id)
        {
            case 0 : 
            {
                value = null; //$JR_EXPR_ID=0$
                break;
            }
            case 1 : 
            {
                value = false; //$JR_EXPR_ID=1$
                break;
            }
            case 2 : 
            {
                value = false; //$JR_EXPR_ID=2$
                break;
            }
            case 3 : 
            {
                value = false; //$JR_EXPR_ID=3$
                break;
            }
            case 4 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=4$
                break;
            }
            case 5 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=5$
                break;
            }
            case 6 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=6$
                break;
            }
            case 7 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=7$
                break;
            }
            case 8 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=8$
                break;
            }
            case 9 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=9$
                break;
            }
            case 10 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=10$
                break;
            }
            case 11 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=11$
                break;
            }
            case 12 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=12$
                break;
            }
            case 13 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && (!((java.lang.Boolean)parameter_EXIBIR_TITULO_PRIMEIRA_PAGINA.getValue()) || ((java.lang.Integer)variable_PAGE_NUMBER.getValue()) == 1); //$JR_EXPR_ID=13$
                break;
            }
            case 14 : 
            {
                value = ((java.lang.String)parameter_TITULO_REPORT.getValue()); //$JR_EXPR_ID=14$
                break;
            }
            case 15 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=15$
                break;
            }
            case 16 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_2.getValue()); //$JR_EXPR_ID=16$
                break;
            }
            case 17 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=17$
                break;
            }
            case 18 : 
            {
                value = ((java.lang.String)parameter_CAMINHO_IMAGEM_PADRAO.getValue()); //$JR_EXPR_ID=18$
                break;
            }
            case 19 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=19$
                break;
            }
            case 20 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_1.getValue()); //$JR_EXPR_ID=20$
                break;
            }
            case 21 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=21$
                break;
            }
            case 22 : 
            {
                value = ((java.lang.String)parameter_UNIDADE_ATENDIMENTO.getValue()); //$JR_EXPR_ID=22$
                break;
            }
            case 23 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_DIRETOR_TECNICO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=23$
                break;
            }
            case 24 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()) != null //$JR_EXPR_ID=24$
? ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()).replaceAll("-", "\n") //$JR_EXPR_ID=24$
: null; //$JR_EXPR_ID=24$
                break;
            }
            case 25 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=25$
                break;
            }
            case 26 : 
            {
                value = ((java.lang.String)parameter_DESC_CABECALHO_PADRAO.getValue()); //$JR_EXPR_ID=26$
                break;
            }
            case 27 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=27$
                break;
            }
            case 28 : 
            {
                value = "Emitido" //$JR_EXPR_ID=28$
    + (((java.lang.String)parameter_USUARIO_LOGADO.getValue()) != null ? " por " + ((java.lang.String)parameter_USUARIO_LOGADO.getValue()) : "") //$JR_EXPR_ID=28$
    + " em " + Data.formatarComTimezone(Data.getDataAtual()) //$JR_EXPR_ID=28$
    + " | " + ((java.lang.String)parameter_SISTEMA.getValue()) + " v" + ((java.lang.String)parameter_VERSAO_SISTEMA.getValue()) + " - CELK SISTEMAS LTDA"; //$JR_EXPR_ID=28$
                break;
            }
            case 29 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=29$
                break;
            }
            case 30 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getValue())+"    / "; //$JR_EXPR_ID=30$
                break;
            }
            case 31 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=31$
                break;
            }
            case 32 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getValue()); //$JR_EXPR_ID=32$
                break;
            }
            case 33 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getValue())+" de"; //$JR_EXPR_ID=33$
                break;
            }
            case 34 : 
            {
                value = " " + ((java.lang.Integer)variable_PAGE_NUMBER.getValue()); //$JR_EXPR_ID=34$
                break;
            }
            case 35 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getValue())==1; //$JR_EXPR_ID=35$
                break;
            }
            case 36 : 
            {
                value = /*Justificativa Internacao*/ //$JR_EXPR_ID=36$
Bundle.getStringApplication("rotulo_justificativa_internacao").toUpperCase(); //$JR_EXPR_ID=36$
                break;
            }
            case 37 : 
            {
                value = /*Condicoes Justificam Internacao*/ //$JR_EXPR_ID=37$
Bundle.getStringApplication("rotulo_condicoes_justificam_internacao")+"  :  "+((java.lang.String)field_condicoesJustificamInternacao.getValue()); //$JR_EXPR_ID=37$
                break;
            }
            case 38 : 
            {
                value = /*Clinica*/ //$JR_EXPR_ID=38$
Bundle.getStringApplication("rotulo_clinica"); //$JR_EXPR_ID=38$
                break;
            }
            case 39 : 
            {
                value = /*Principais Resultados*/ //$JR_EXPR_ID=39$
Bundle.getStringApplication("rotulo_principais_resultados_provas_diagnosticas")+" :  "+((java.lang.String)field_principaisResultadosProvasDiagnosticas.getValue()); //$JR_EXPR_ID=39$
                break;
            }
            case 40 : 
            {
                value = /*Sinais Sintomas*/ //$JR_EXPR_ID=40$
Bundle.getStringApplication("rotulo_principais_sinais_sintomas_clinicos")+" : "+ //$JR_EXPR_ID=40$
((java.lang.String)field_principaisSinaisSintomasClinicos.getValue()); //$JR_EXPR_ID=40$
                break;
            }
            case 41 : 
            {
                value = /*Diagnostico Inicial*/ //$JR_EXPR_ID=41$
Bundle.getStringApplication("rotulo_diagnostico_inicial")+" :  "+((java.lang.String)field_diagnosticoInicial.getValue()); //$JR_EXPR_ID=41$
                break;
            }
            case 42 : 
            {
                value = /*Carater Internacao*/ //$JR_EXPR_ID=42$
Bundle.getStringApplication("rotulo_carater_internacao"); //$JR_EXPR_ID=42$
                break;
            }
            case 43 : 
            {
                value = ((java.lang.String)field_nomeProfissionalSolicitante.getValue()); //$JR_EXPR_ID=43$
                break;
            }
            case 44 : 
            {
                value = ((java.lang.String)field_nroDocProfSol.getValue()); //$JR_EXPR_ID=44$
                break;
            }
            case 45 : 
            {
                value = /*Nome Estabelecimento Solicitante*/ //$JR_EXPR_ID=45$
Bundle.getStringApplication("rotulo_nome_estabelecimento_solicitante"); //$JR_EXPR_ID=45$
                break;
            }
            case 46 : 
            {
                value = /*Cnes*/ //$JR_EXPR_ID=46$
Bundle.getStringApplication("rotulo_cnes"); //$JR_EXPR_ID=46$
                break;
            }
            case 47 : 
            {
                value = /*Data Solicitacao*/ //$JR_EXPR_ID=47$
Bundle.getStringApplication("rotulo_data_solicitacao"); //$JR_EXPR_ID=47$
                break;
            }
            case 48 : 
            {
                value = /*Nome do Estabelecimento Executante*/ //$JR_EXPR_ID=48$
Bundle.getStringApplication("rotulo_estabelecimento_executante"); //$JR_EXPR_ID=48$
                break;
            }
            case 49 : 
            {
                value = /*Descricao Procedimento*/ //$JR_EXPR_ID=49$
Bundle.getStringApplication("rotulo_descricao_procedimento_solicitado"); //$JR_EXPR_ID=49$
                break;
            }
            case 50 : 
            {
                value = /*Nome Profissional Solicitante*/ //$JR_EXPR_ID=50$
Bundle.getStringApplication("rotulo_profissional_solicitante"); //$JR_EXPR_ID=50$
                break;
            }
            case 51 : 
            {
                value = /*N Documento Profissional Solicitante*/ //$JR_EXPR_ID=51$
Bundle.getStringApplication("rotulo_numero_doc_profissional_solicitante"); //$JR_EXPR_ID=51$
                break;
            }
            case 52 : 
            {
                value = /*Identificacao Paciente*/ //$JR_EXPR_ID=52$
Bundle.getStringApplication("rotulo_identificacao_paciente").toUpperCase(); //$JR_EXPR_ID=52$
                break;
            }
            case 53 : 
            {
                value = /*Nome Paciente*/ //$JR_EXPR_ID=53$
Bundle.getStringApplication("rotulo_nome_paciente"); //$JR_EXPR_ID=53$
                break;
            }
            case 54 : 
            {
                value = /*CNS*/ //$JR_EXPR_ID=54$
Bundle.getStringApplication("rotulo_cartao_nacional_de_saude_cns"); //$JR_EXPR_ID=54$
                break;
            }
            case 55 : 
            {
                value = /*Data Nascimento*/ //$JR_EXPR_ID=55$
Bundle.getStringApplication("rotulo_data_nascimento"); //$JR_EXPR_ID=55$
                break;
            }
            case 56 : 
            {
                value = /*sexo*/ //$JR_EXPR_ID=56$
Bundle.getStringApplication("rotulo_sexo"); //$JR_EXPR_ID=56$
                break;
            }
            case 57 : 
            {
                value = /*Etnia*/ //$JR_EXPR_ID=57$
Bundle.getStringApplication("rotulo_etnia"); //$JR_EXPR_ID=57$
                break;
            }
            case 58 : 
            {
                value = /*Telefone*/ //$JR_EXPR_ID=58$
Bundle.getStringApplication("rotulo_telefone"); //$JR_EXPR_ID=58$
                break;
            }
            case 59 : 
            {
                value = /*Raca / Cor*/ //$JR_EXPR_ID=59$
Bundle.getStringApplication("rotulo_raca_cor"); //$JR_EXPR_ID=59$
                break;
            }
            case 60 : 
            {
                value = /*Numero Prontuario*/ //$JR_EXPR_ID=60$
Bundle.getStringApplication("rotulo_numero_prontuario"); //$JR_EXPR_ID=60$
                break;
            }
            case 61 : 
            {
                value = /*Nome mae*/ //$JR_EXPR_ID=61$
Bundle.getStringApplication("rotulo_nome_mae"); //$JR_EXPR_ID=61$
                break;
            }
            case 62 : 
            {
                value = /*endereco*/ //$JR_EXPR_ID=62$
Bundle.getStringApplication("rotulo_endereco_rua_numero_bairro"); //$JR_EXPR_ID=62$
                break;
            }
            case 63 : 
            {
                value = /*Cid Principal*/ //$JR_EXPR_ID=63$
Bundle.getStringApplication("rotulo_cid_principal"); //$JR_EXPR_ID=63$
                break;
            }
            case 64 : 
            {
                value = /*cod IBGE*/ //$JR_EXPR_ID=64$
Bundle.getStringApplication("rotulo_cod_ibge_municipio"); //$JR_EXPR_ID=64$
                break;
            }
            case 65 : 
            {
                value = /*UF*/ //$JR_EXPR_ID=65$
Bundle.getStringApplication("rotulo_uf"); //$JR_EXPR_ID=65$
                break;
            }
            case 66 : 
            {
                value = /*CEP*/ //$JR_EXPR_ID=66$
Bundle.getStringApplication("rotulo_cep"); //$JR_EXPR_ID=66$
                break;
            }
            case 67 : 
            {
                value = /*Cod Procedimento*/ //$JR_EXPR_ID=67$
Bundle.getStringApplication("rotulo_codigo_procedimento"); //$JR_EXPR_ID=67$
                break;
            }
            case 68 : 
            {
                value = /*Procedimento Solicitado*/ //$JR_EXPR_ID=68$
Bundle.getStringApplication("rotulo_procedimento_solicitado").toUpperCase(); //$JR_EXPR_ID=68$
                break;
            }
            case 69 : 
            {
                value = /*Cnes*/ //$JR_EXPR_ID=69$
Bundle.getStringApplication("rotulo_cnes"); //$JR_EXPR_ID=69$
                break;
            }
            case 70 : 
            {
                value = /*N Registro*/ //$JR_EXPR_ID=70$
Bundle.getStringApplication("rotulo_numero_de_registro"); //$JR_EXPR_ID=70$
                break;
            }
            case 71 : 
            {
                value = ((java.lang.Long)field_status.getValue()); //$JR_EXPR_ID=71$
                break;
            }
            case 72 : 
            {
                value = /*Classificacao risco*/ //$JR_EXPR_ID=72$
Bundle.getStringApplication("rotulo_classificacao_risco"); //$JR_EXPR_ID=72$
                break;
            }
            case 73 : 
            {
                value = /*Situacao*/ //$JR_EXPR_ID=73$
Bundle.getStringApplication("rotulo_situacao"); //$JR_EXPR_ID=73$
                break;
            }
            case 74 : 
            {
                value = /*Leito*/ //$JR_EXPR_ID=74$
Bundle.getStringApplication("rotulo_leito"); //$JR_EXPR_ID=74$
                break;
            }
            case 75 : 
            {
                value = /*Identificacao do Estabelecimento de Saude*/ //$JR_EXPR_ID=75$
Bundle.getStringApplication("rotulo_identificacao_estabelecimento_saude").toUpperCase(); //$JR_EXPR_ID=75$
                break;
            }
            case 76 : 
            {
                value = ((java.util.Date)field_dataSolicitacao.getValue()); //$JR_EXPR_ID=76$
                break;
            }
            case 77 : 
            {
                value = ((java.lang.Long)field_caraterInternacao.getValue())==1? Bundle.getStringApplication("rotulo_eletivo"):Bundle.getStringApplication("rotulo_emergencia"); //$JR_EXPR_ID=77$
                break;
            }
            case 78 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento)field_procedimentoSolicitadoDesc.getValue()); //$JR_EXPR_ID=78$
                break;
            }
            case 79 : 
            {
                value = ((java.lang.String)field_procedimentoSolicitadoCod.getValue()); //$JR_EXPR_ID=79$
                break;
            }
            case 80 : 
            {
                value = ((java.lang.Long)field_clinica.getValue())==0?Bundle.getStringApplication("rotulo_cirurgica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==1?Bundle.getStringApplication("rotulo_obstetrica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==2? Bundle.getStringApplication("rotulo_clinica_medica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==3?Bundle.getStringApplication("rotulo_psiquiatrica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==4?Bundle.getStringApplication("rotulo_pediatrica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==5?Bundle.getStringApplication("rotulo_outros") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==null?"":""; //$JR_EXPR_ID=80$
                break;
            }
            case 81 : 
            {
                value = ((java.lang.String)field_cidPrincipal.getValue()); //$JR_EXPR_ID=81$
                break;
            }
            case 82 : 
            {
                value = ((java.lang.String)field_usuarioCadSusNome.getValue()); //$JR_EXPR_ID=82$
                break;
            }
            case 83 : 
            {
                value = ((java.lang.String)field_ocorrenciaDescricao.getValue()); //$JR_EXPR_ID=83$
                break;
            }
            case 84 : 
            {
                value = /*data*/ //$JR_EXPR_ID=84$
Bundle.getStringApplication("rotulo_data"); //$JR_EXPR_ID=84$
                break;
            }
            case 85 : 
            {
                value = /*usuario*/ //$JR_EXPR_ID=85$
Bundle.getStringApplication("rotulo_usuario"); //$JR_EXPR_ID=85$
                break;
            }
            case 86 : 
            {
                value = /*registro*/ //$JR_EXPR_ID=86$
Bundle.getStringApplication("rotulo_registro"); //$JR_EXPR_ID=86$
                break;
            }
            case 87 : 
            {
                value = /*Estabelecimento*/ //$JR_EXPR_ID=87$
Bundle.getStringApplication("rotulo_estabelecimento"); //$JR_EXPR_ID=87$
                break;
            }
            case 88 : 
            {
                value = /*Descricao*/ //$JR_EXPR_ID=88$
Bundle.getStringApplication("rotulo_descricao"); //$JR_EXPR_ID=88$
                break;
            }
            case 89 : 
            {
                value = /*dados clinicos*/ //$JR_EXPR_ID=89$
Bundle.getStringApplication("rotulo_dados_clinicos").toUpperCase(); //$JR_EXPR_ID=89$
                break;
            }
            case 90 : 
            {
                value = ((java.util.Date)field_dataCadastro.getValue()); //$JR_EXPR_ID=90$
                break;
            }
            case 91 : 
            {
                value = ((br.com.ksisolucoes.vo.controle.Usuario)field_usuarioCadastro.getValue()); //$JR_EXPR_ID=91$
                break;
            }
            case 92 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=92$
                break;
            }
            case 93 : 
            {
                value = ((java.lang.String)parameter_RUA_UNIDADE.getValue()) + (((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) != null ? ", " + ((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) : "") //$JR_EXPR_ID=93$
    + " - " + ((java.lang.String)parameter_BAIRRO_UNIDADE.getValue()) + (((java.lang.String)parameter_CEP_UNIDADE.getValue()) != null ? " - CEP " + ((java.lang.String)parameter_CEP_UNIDADE.getValue()) : ""); //$JR_EXPR_ID=93$
                break;
            }
            case 94 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=94$
                break;
            }
            case 95 : 
            {
                value = ((java.lang.String)parameter_CIDADE_UNIDADE.getValue()) + " - " + ((java.lang.String)parameter_UF_UNIDADE.getValue()) //$JR_EXPR_ID=95$
    + (((java.lang.String)parameter_FONE_UNIDADE.getValue()) == null //$JR_EXPR_ID=95$
        ? "" //$JR_EXPR_ID=95$
        : " | " + ((java.lang.String)parameter_FONE_UNIDADE.getValue())); //$JR_EXPR_ID=95$
                break;
            }
           default :
           {
           }
        }
        
        return value;
    }


    /**
     *
     */
    public Object evaluateOld(int id) throws Throwable
    {
        Object value = null;

        switch (id)
        {
            case 0 : 
            {
                value = null; //$JR_EXPR_ID=0$
                break;
            }
            case 1 : 
            {
                value = false; //$JR_EXPR_ID=1$
                break;
            }
            case 2 : 
            {
                value = false; //$JR_EXPR_ID=2$
                break;
            }
            case 3 : 
            {
                value = false; //$JR_EXPR_ID=3$
                break;
            }
            case 4 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=4$
                break;
            }
            case 5 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=5$
                break;
            }
            case 6 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=6$
                break;
            }
            case 7 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=7$
                break;
            }
            case 8 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=8$
                break;
            }
            case 9 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=9$
                break;
            }
            case 10 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=10$
                break;
            }
            case 11 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=11$
                break;
            }
            case 12 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=12$
                break;
            }
            case 13 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && (!((java.lang.Boolean)parameter_EXIBIR_TITULO_PRIMEIRA_PAGINA.getValue()) || ((java.lang.Integer)variable_PAGE_NUMBER.getOldValue()) == 1); //$JR_EXPR_ID=13$
                break;
            }
            case 14 : 
            {
                value = ((java.lang.String)parameter_TITULO_REPORT.getValue()); //$JR_EXPR_ID=14$
                break;
            }
            case 15 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=15$
                break;
            }
            case 16 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_2.getValue()); //$JR_EXPR_ID=16$
                break;
            }
            case 17 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=17$
                break;
            }
            case 18 : 
            {
                value = ((java.lang.String)parameter_CAMINHO_IMAGEM_PADRAO.getValue()); //$JR_EXPR_ID=18$
                break;
            }
            case 19 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=19$
                break;
            }
            case 20 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_1.getValue()); //$JR_EXPR_ID=20$
                break;
            }
            case 21 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=21$
                break;
            }
            case 22 : 
            {
                value = ((java.lang.String)parameter_UNIDADE_ATENDIMENTO.getValue()); //$JR_EXPR_ID=22$
                break;
            }
            case 23 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_DIRETOR_TECNICO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=23$
                break;
            }
            case 24 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()) != null //$JR_EXPR_ID=24$
? ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()).replaceAll("-", "\n") //$JR_EXPR_ID=24$
: null; //$JR_EXPR_ID=24$
                break;
            }
            case 25 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=25$
                break;
            }
            case 26 : 
            {
                value = ((java.lang.String)parameter_DESC_CABECALHO_PADRAO.getValue()); //$JR_EXPR_ID=26$
                break;
            }
            case 27 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=27$
                break;
            }
            case 28 : 
            {
                value = "Emitido" //$JR_EXPR_ID=28$
    + (((java.lang.String)parameter_USUARIO_LOGADO.getValue()) != null ? " por " + ((java.lang.String)parameter_USUARIO_LOGADO.getValue()) : "") //$JR_EXPR_ID=28$
    + " em " + Data.formatarComTimezone(Data.getDataAtual()) //$JR_EXPR_ID=28$
    + " | " + ((java.lang.String)parameter_SISTEMA.getValue()) + " v" + ((java.lang.String)parameter_VERSAO_SISTEMA.getValue()) + " - CELK SISTEMAS LTDA"; //$JR_EXPR_ID=28$
                break;
            }
            case 29 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=29$
                break;
            }
            case 30 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getOldValue())+"    / "; //$JR_EXPR_ID=30$
                break;
            }
            case 31 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=31$
                break;
            }
            case 32 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getOldValue()); //$JR_EXPR_ID=32$
                break;
            }
            case 33 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getOldValue())+" de"; //$JR_EXPR_ID=33$
                break;
            }
            case 34 : 
            {
                value = " " + ((java.lang.Integer)variable_PAGE_NUMBER.getOldValue()); //$JR_EXPR_ID=34$
                break;
            }
            case 35 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getOldValue())==1; //$JR_EXPR_ID=35$
                break;
            }
            case 36 : 
            {
                value = /*Justificativa Internacao*/ //$JR_EXPR_ID=36$
Bundle.getStringApplication("rotulo_justificativa_internacao").toUpperCase(); //$JR_EXPR_ID=36$
                break;
            }
            case 37 : 
            {
                value = /*Condicoes Justificam Internacao*/ //$JR_EXPR_ID=37$
Bundle.getStringApplication("rotulo_condicoes_justificam_internacao")+"  :  "+((java.lang.String)field_condicoesJustificamInternacao.getOldValue()); //$JR_EXPR_ID=37$
                break;
            }
            case 38 : 
            {
                value = /*Clinica*/ //$JR_EXPR_ID=38$
Bundle.getStringApplication("rotulo_clinica"); //$JR_EXPR_ID=38$
                break;
            }
            case 39 : 
            {
                value = /*Principais Resultados*/ //$JR_EXPR_ID=39$
Bundle.getStringApplication("rotulo_principais_resultados_provas_diagnosticas")+" :  "+((java.lang.String)field_principaisResultadosProvasDiagnosticas.getOldValue()); //$JR_EXPR_ID=39$
                break;
            }
            case 40 : 
            {
                value = /*Sinais Sintomas*/ //$JR_EXPR_ID=40$
Bundle.getStringApplication("rotulo_principais_sinais_sintomas_clinicos")+" : "+ //$JR_EXPR_ID=40$
((java.lang.String)field_principaisSinaisSintomasClinicos.getOldValue()); //$JR_EXPR_ID=40$
                break;
            }
            case 41 : 
            {
                value = /*Diagnostico Inicial*/ //$JR_EXPR_ID=41$
Bundle.getStringApplication("rotulo_diagnostico_inicial")+" :  "+((java.lang.String)field_diagnosticoInicial.getOldValue()); //$JR_EXPR_ID=41$
                break;
            }
            case 42 : 
            {
                value = /*Carater Internacao*/ //$JR_EXPR_ID=42$
Bundle.getStringApplication("rotulo_carater_internacao"); //$JR_EXPR_ID=42$
                break;
            }
            case 43 : 
            {
                value = ((java.lang.String)field_nomeProfissionalSolicitante.getOldValue()); //$JR_EXPR_ID=43$
                break;
            }
            case 44 : 
            {
                value = ((java.lang.String)field_nroDocProfSol.getOldValue()); //$JR_EXPR_ID=44$
                break;
            }
            case 45 : 
            {
                value = /*Nome Estabelecimento Solicitante*/ //$JR_EXPR_ID=45$
Bundle.getStringApplication("rotulo_nome_estabelecimento_solicitante"); //$JR_EXPR_ID=45$
                break;
            }
            case 46 : 
            {
                value = /*Cnes*/ //$JR_EXPR_ID=46$
Bundle.getStringApplication("rotulo_cnes"); //$JR_EXPR_ID=46$
                break;
            }
            case 47 : 
            {
                value = /*Data Solicitacao*/ //$JR_EXPR_ID=47$
Bundle.getStringApplication("rotulo_data_solicitacao"); //$JR_EXPR_ID=47$
                break;
            }
            case 48 : 
            {
                value = /*Nome do Estabelecimento Executante*/ //$JR_EXPR_ID=48$
Bundle.getStringApplication("rotulo_estabelecimento_executante"); //$JR_EXPR_ID=48$
                break;
            }
            case 49 : 
            {
                value = /*Descricao Procedimento*/ //$JR_EXPR_ID=49$
Bundle.getStringApplication("rotulo_descricao_procedimento_solicitado"); //$JR_EXPR_ID=49$
                break;
            }
            case 50 : 
            {
                value = /*Nome Profissional Solicitante*/ //$JR_EXPR_ID=50$
Bundle.getStringApplication("rotulo_profissional_solicitante"); //$JR_EXPR_ID=50$
                break;
            }
            case 51 : 
            {
                value = /*N Documento Profissional Solicitante*/ //$JR_EXPR_ID=51$
Bundle.getStringApplication("rotulo_numero_doc_profissional_solicitante"); //$JR_EXPR_ID=51$
                break;
            }
            case 52 : 
            {
                value = /*Identificacao Paciente*/ //$JR_EXPR_ID=52$
Bundle.getStringApplication("rotulo_identificacao_paciente").toUpperCase(); //$JR_EXPR_ID=52$
                break;
            }
            case 53 : 
            {
                value = /*Nome Paciente*/ //$JR_EXPR_ID=53$
Bundle.getStringApplication("rotulo_nome_paciente"); //$JR_EXPR_ID=53$
                break;
            }
            case 54 : 
            {
                value = /*CNS*/ //$JR_EXPR_ID=54$
Bundle.getStringApplication("rotulo_cartao_nacional_de_saude_cns"); //$JR_EXPR_ID=54$
                break;
            }
            case 55 : 
            {
                value = /*Data Nascimento*/ //$JR_EXPR_ID=55$
Bundle.getStringApplication("rotulo_data_nascimento"); //$JR_EXPR_ID=55$
                break;
            }
            case 56 : 
            {
                value = /*sexo*/ //$JR_EXPR_ID=56$
Bundle.getStringApplication("rotulo_sexo"); //$JR_EXPR_ID=56$
                break;
            }
            case 57 : 
            {
                value = /*Etnia*/ //$JR_EXPR_ID=57$
Bundle.getStringApplication("rotulo_etnia"); //$JR_EXPR_ID=57$
                break;
            }
            case 58 : 
            {
                value = /*Telefone*/ //$JR_EXPR_ID=58$
Bundle.getStringApplication("rotulo_telefone"); //$JR_EXPR_ID=58$
                break;
            }
            case 59 : 
            {
                value = /*Raca / Cor*/ //$JR_EXPR_ID=59$
Bundle.getStringApplication("rotulo_raca_cor"); //$JR_EXPR_ID=59$
                break;
            }
            case 60 : 
            {
                value = /*Numero Prontuario*/ //$JR_EXPR_ID=60$
Bundle.getStringApplication("rotulo_numero_prontuario"); //$JR_EXPR_ID=60$
                break;
            }
            case 61 : 
            {
                value = /*Nome mae*/ //$JR_EXPR_ID=61$
Bundle.getStringApplication("rotulo_nome_mae"); //$JR_EXPR_ID=61$
                break;
            }
            case 62 : 
            {
                value = /*endereco*/ //$JR_EXPR_ID=62$
Bundle.getStringApplication("rotulo_endereco_rua_numero_bairro"); //$JR_EXPR_ID=62$
                break;
            }
            case 63 : 
            {
                value = /*Cid Principal*/ //$JR_EXPR_ID=63$
Bundle.getStringApplication("rotulo_cid_principal"); //$JR_EXPR_ID=63$
                break;
            }
            case 64 : 
            {
                value = /*cod IBGE*/ //$JR_EXPR_ID=64$
Bundle.getStringApplication("rotulo_cod_ibge_municipio"); //$JR_EXPR_ID=64$
                break;
            }
            case 65 : 
            {
                value = /*UF*/ //$JR_EXPR_ID=65$
Bundle.getStringApplication("rotulo_uf"); //$JR_EXPR_ID=65$
                break;
            }
            case 66 : 
            {
                value = /*CEP*/ //$JR_EXPR_ID=66$
Bundle.getStringApplication("rotulo_cep"); //$JR_EXPR_ID=66$
                break;
            }
            case 67 : 
            {
                value = /*Cod Procedimento*/ //$JR_EXPR_ID=67$
Bundle.getStringApplication("rotulo_codigo_procedimento"); //$JR_EXPR_ID=67$
                break;
            }
            case 68 : 
            {
                value = /*Procedimento Solicitado*/ //$JR_EXPR_ID=68$
Bundle.getStringApplication("rotulo_procedimento_solicitado").toUpperCase(); //$JR_EXPR_ID=68$
                break;
            }
            case 69 : 
            {
                value = /*Cnes*/ //$JR_EXPR_ID=69$
Bundle.getStringApplication("rotulo_cnes"); //$JR_EXPR_ID=69$
                break;
            }
            case 70 : 
            {
                value = /*N Registro*/ //$JR_EXPR_ID=70$
Bundle.getStringApplication("rotulo_numero_de_registro"); //$JR_EXPR_ID=70$
                break;
            }
            case 71 : 
            {
                value = ((java.lang.Long)field_status.getOldValue()); //$JR_EXPR_ID=71$
                break;
            }
            case 72 : 
            {
                value = /*Classificacao risco*/ //$JR_EXPR_ID=72$
Bundle.getStringApplication("rotulo_classificacao_risco"); //$JR_EXPR_ID=72$
                break;
            }
            case 73 : 
            {
                value = /*Situacao*/ //$JR_EXPR_ID=73$
Bundle.getStringApplication("rotulo_situacao"); //$JR_EXPR_ID=73$
                break;
            }
            case 74 : 
            {
                value = /*Leito*/ //$JR_EXPR_ID=74$
Bundle.getStringApplication("rotulo_leito"); //$JR_EXPR_ID=74$
                break;
            }
            case 75 : 
            {
                value = /*Identificacao do Estabelecimento de Saude*/ //$JR_EXPR_ID=75$
Bundle.getStringApplication("rotulo_identificacao_estabelecimento_saude").toUpperCase(); //$JR_EXPR_ID=75$
                break;
            }
            case 76 : 
            {
                value = ((java.util.Date)field_dataSolicitacao.getOldValue()); //$JR_EXPR_ID=76$
                break;
            }
            case 77 : 
            {
                value = ((java.lang.Long)field_caraterInternacao.getOldValue())==1? Bundle.getStringApplication("rotulo_eletivo"):Bundle.getStringApplication("rotulo_emergencia"); //$JR_EXPR_ID=77$
                break;
            }
            case 78 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento)field_procedimentoSolicitadoDesc.getOldValue()); //$JR_EXPR_ID=78$
                break;
            }
            case 79 : 
            {
                value = ((java.lang.String)field_procedimentoSolicitadoCod.getOldValue()); //$JR_EXPR_ID=79$
                break;
            }
            case 80 : 
            {
                value = ((java.lang.Long)field_clinica.getOldValue())==0?Bundle.getStringApplication("rotulo_cirurgica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getOldValue())==1?Bundle.getStringApplication("rotulo_obstetrica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getOldValue())==2? Bundle.getStringApplication("rotulo_clinica_medica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getOldValue())==3?Bundle.getStringApplication("rotulo_psiquiatrica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getOldValue())==4?Bundle.getStringApplication("rotulo_pediatrica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getOldValue())==5?Bundle.getStringApplication("rotulo_outros") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getOldValue())==null?"":""; //$JR_EXPR_ID=80$
                break;
            }
            case 81 : 
            {
                value = ((java.lang.String)field_cidPrincipal.getOldValue()); //$JR_EXPR_ID=81$
                break;
            }
            case 82 : 
            {
                value = ((java.lang.String)field_usuarioCadSusNome.getOldValue()); //$JR_EXPR_ID=82$
                break;
            }
            case 83 : 
            {
                value = ((java.lang.String)field_ocorrenciaDescricao.getOldValue()); //$JR_EXPR_ID=83$
                break;
            }
            case 84 : 
            {
                value = /*data*/ //$JR_EXPR_ID=84$
Bundle.getStringApplication("rotulo_data"); //$JR_EXPR_ID=84$
                break;
            }
            case 85 : 
            {
                value = /*usuario*/ //$JR_EXPR_ID=85$
Bundle.getStringApplication("rotulo_usuario"); //$JR_EXPR_ID=85$
                break;
            }
            case 86 : 
            {
                value = /*registro*/ //$JR_EXPR_ID=86$
Bundle.getStringApplication("rotulo_registro"); //$JR_EXPR_ID=86$
                break;
            }
            case 87 : 
            {
                value = /*Estabelecimento*/ //$JR_EXPR_ID=87$
Bundle.getStringApplication("rotulo_estabelecimento"); //$JR_EXPR_ID=87$
                break;
            }
            case 88 : 
            {
                value = /*Descricao*/ //$JR_EXPR_ID=88$
Bundle.getStringApplication("rotulo_descricao"); //$JR_EXPR_ID=88$
                break;
            }
            case 89 : 
            {
                value = /*dados clinicos*/ //$JR_EXPR_ID=89$
Bundle.getStringApplication("rotulo_dados_clinicos").toUpperCase(); //$JR_EXPR_ID=89$
                break;
            }
            case 90 : 
            {
                value = ((java.util.Date)field_dataCadastro.getOldValue()); //$JR_EXPR_ID=90$
                break;
            }
            case 91 : 
            {
                value = ((br.com.ksisolucoes.vo.controle.Usuario)field_usuarioCadastro.getOldValue()); //$JR_EXPR_ID=91$
                break;
            }
            case 92 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=92$
                break;
            }
            case 93 : 
            {
                value = ((java.lang.String)parameter_RUA_UNIDADE.getValue()) + (((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) != null ? ", " + ((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) : "") //$JR_EXPR_ID=93$
    + " - " + ((java.lang.String)parameter_BAIRRO_UNIDADE.getValue()) + (((java.lang.String)parameter_CEP_UNIDADE.getValue()) != null ? " - CEP " + ((java.lang.String)parameter_CEP_UNIDADE.getValue()) : ""); //$JR_EXPR_ID=93$
                break;
            }
            case 94 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=94$
                break;
            }
            case 95 : 
            {
                value = ((java.lang.String)parameter_CIDADE_UNIDADE.getValue()) + " - " + ((java.lang.String)parameter_UF_UNIDADE.getValue()) //$JR_EXPR_ID=95$
    + (((java.lang.String)parameter_FONE_UNIDADE.getValue()) == null //$JR_EXPR_ID=95$
        ? "" //$JR_EXPR_ID=95$
        : " | " + ((java.lang.String)parameter_FONE_UNIDADE.getValue())); //$JR_EXPR_ID=95$
                break;
            }
           default :
           {
           }
        }
        
        return value;
    }


    /**
     *
     */
    public Object evaluateEstimated(int id) throws Throwable
    {
        Object value = null;

        switch (id)
        {
            case 0 : 
            {
                value = null; //$JR_EXPR_ID=0$
                break;
            }
            case 1 : 
            {
                value = false; //$JR_EXPR_ID=1$
                break;
            }
            case 2 : 
            {
                value = false; //$JR_EXPR_ID=2$
                break;
            }
            case 3 : 
            {
                value = false; //$JR_EXPR_ID=3$
                break;
            }
            case 4 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=4$
                break;
            }
            case 5 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=5$
                break;
            }
            case 6 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=6$
                break;
            }
            case 7 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=7$
                break;
            }
            case 8 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=8$
                break;
            }
            case 9 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=9$
                break;
            }
            case 10 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=10$
                break;
            }
            case 11 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=11$
                break;
            }
            case 12 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=12$
                break;
            }
            case 13 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && (!((java.lang.Boolean)parameter_EXIBIR_TITULO_PRIMEIRA_PAGINA.getValue()) || ((java.lang.Integer)variable_PAGE_NUMBER.getEstimatedValue()) == 1); //$JR_EXPR_ID=13$
                break;
            }
            case 14 : 
            {
                value = ((java.lang.String)parameter_TITULO_REPORT.getValue()); //$JR_EXPR_ID=14$
                break;
            }
            case 15 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=15$
                break;
            }
            case 16 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_2.getValue()); //$JR_EXPR_ID=16$
                break;
            }
            case 17 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=17$
                break;
            }
            case 18 : 
            {
                value = ((java.lang.String)parameter_CAMINHO_IMAGEM_PADRAO.getValue()); //$JR_EXPR_ID=18$
                break;
            }
            case 19 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=19$
                break;
            }
            case 20 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_1.getValue()); //$JR_EXPR_ID=20$
                break;
            }
            case 21 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=21$
                break;
            }
            case 22 : 
            {
                value = ((java.lang.String)parameter_UNIDADE_ATENDIMENTO.getValue()); //$JR_EXPR_ID=22$
                break;
            }
            case 23 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_DIRETOR_TECNICO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=23$
                break;
            }
            case 24 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()) != null //$JR_EXPR_ID=24$
? ((java.lang.String)parameter_CABECALHO_DIRETOR_TECNICO.getValue()).replaceAll("-", "\n") //$JR_EXPR_ID=24$
: null; //$JR_EXPR_ID=24$
                break;
            }
            case 25 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=25$
                break;
            }
            case 26 : 
            {
                value = ((java.lang.String)parameter_DESC_CABECALHO_PADRAO.getValue()); //$JR_EXPR_ID=26$
                break;
            }
            case 27 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()); //$JR_EXPR_ID=27$
                break;
            }
            case 28 : 
            {
                value = "Emitido" //$JR_EXPR_ID=28$
    + (((java.lang.String)parameter_USUARIO_LOGADO.getValue()) != null ? " por " + ((java.lang.String)parameter_USUARIO_LOGADO.getValue()) : "") //$JR_EXPR_ID=28$
    + " em " + Data.formatarComTimezone(Data.getDataAtual()) //$JR_EXPR_ID=28$
    + " | " + ((java.lang.String)parameter_SISTEMA.getValue()) + " v" + ((java.lang.String)parameter_VERSAO_SISTEMA.getValue()) + " - CELK SISTEMAS LTDA"; //$JR_EXPR_ID=28$
                break;
            }
            case 29 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=29$
                break;
            }
            case 30 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getEstimatedValue())+"    / "; //$JR_EXPR_ID=30$
                break;
            }
            case 31 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_CABECALHO.getValue()) && ((java.lang.Boolean)parameter_EXIBIR_NUMERO_PAGINAS.getValue()); //$JR_EXPR_ID=31$
                break;
            }
            case 32 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getEstimatedValue()); //$JR_EXPR_ID=32$
                break;
            }
            case 33 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getEstimatedValue())+" de"; //$JR_EXPR_ID=33$
                break;
            }
            case 34 : 
            {
                value = " " + ((java.lang.Integer)variable_PAGE_NUMBER.getEstimatedValue()); //$JR_EXPR_ID=34$
                break;
            }
            case 35 : 
            {
                value = ((java.lang.Integer)variable_PAGE_NUMBER.getEstimatedValue())==1; //$JR_EXPR_ID=35$
                break;
            }
            case 36 : 
            {
                value = /*Justificativa Internacao*/ //$JR_EXPR_ID=36$
Bundle.getStringApplication("rotulo_justificativa_internacao").toUpperCase(); //$JR_EXPR_ID=36$
                break;
            }
            case 37 : 
            {
                value = /*Condicoes Justificam Internacao*/ //$JR_EXPR_ID=37$
Bundle.getStringApplication("rotulo_condicoes_justificam_internacao")+"  :  "+((java.lang.String)field_condicoesJustificamInternacao.getValue()); //$JR_EXPR_ID=37$
                break;
            }
            case 38 : 
            {
                value = /*Clinica*/ //$JR_EXPR_ID=38$
Bundle.getStringApplication("rotulo_clinica"); //$JR_EXPR_ID=38$
                break;
            }
            case 39 : 
            {
                value = /*Principais Resultados*/ //$JR_EXPR_ID=39$
Bundle.getStringApplication("rotulo_principais_resultados_provas_diagnosticas")+" :  "+((java.lang.String)field_principaisResultadosProvasDiagnosticas.getValue()); //$JR_EXPR_ID=39$
                break;
            }
            case 40 : 
            {
                value = /*Sinais Sintomas*/ //$JR_EXPR_ID=40$
Bundle.getStringApplication("rotulo_principais_sinais_sintomas_clinicos")+" : "+ //$JR_EXPR_ID=40$
((java.lang.String)field_principaisSinaisSintomasClinicos.getValue()); //$JR_EXPR_ID=40$
                break;
            }
            case 41 : 
            {
                value = /*Diagnostico Inicial*/ //$JR_EXPR_ID=41$
Bundle.getStringApplication("rotulo_diagnostico_inicial")+" :  "+((java.lang.String)field_diagnosticoInicial.getValue()); //$JR_EXPR_ID=41$
                break;
            }
            case 42 : 
            {
                value = /*Carater Internacao*/ //$JR_EXPR_ID=42$
Bundle.getStringApplication("rotulo_carater_internacao"); //$JR_EXPR_ID=42$
                break;
            }
            case 43 : 
            {
                value = ((java.lang.String)field_nomeProfissionalSolicitante.getValue()); //$JR_EXPR_ID=43$
                break;
            }
            case 44 : 
            {
                value = ((java.lang.String)field_nroDocProfSol.getValue()); //$JR_EXPR_ID=44$
                break;
            }
            case 45 : 
            {
                value = /*Nome Estabelecimento Solicitante*/ //$JR_EXPR_ID=45$
Bundle.getStringApplication("rotulo_nome_estabelecimento_solicitante"); //$JR_EXPR_ID=45$
                break;
            }
            case 46 : 
            {
                value = /*Cnes*/ //$JR_EXPR_ID=46$
Bundle.getStringApplication("rotulo_cnes"); //$JR_EXPR_ID=46$
                break;
            }
            case 47 : 
            {
                value = /*Data Solicitacao*/ //$JR_EXPR_ID=47$
Bundle.getStringApplication("rotulo_data_solicitacao"); //$JR_EXPR_ID=47$
                break;
            }
            case 48 : 
            {
                value = /*Nome do Estabelecimento Executante*/ //$JR_EXPR_ID=48$
Bundle.getStringApplication("rotulo_estabelecimento_executante"); //$JR_EXPR_ID=48$
                break;
            }
            case 49 : 
            {
                value = /*Descricao Procedimento*/ //$JR_EXPR_ID=49$
Bundle.getStringApplication("rotulo_descricao_procedimento_solicitado"); //$JR_EXPR_ID=49$
                break;
            }
            case 50 : 
            {
                value = /*Nome Profissional Solicitante*/ //$JR_EXPR_ID=50$
Bundle.getStringApplication("rotulo_profissional_solicitante"); //$JR_EXPR_ID=50$
                break;
            }
            case 51 : 
            {
                value = /*N Documento Profissional Solicitante*/ //$JR_EXPR_ID=51$
Bundle.getStringApplication("rotulo_numero_doc_profissional_solicitante"); //$JR_EXPR_ID=51$
                break;
            }
            case 52 : 
            {
                value = /*Identificacao Paciente*/ //$JR_EXPR_ID=52$
Bundle.getStringApplication("rotulo_identificacao_paciente").toUpperCase(); //$JR_EXPR_ID=52$
                break;
            }
            case 53 : 
            {
                value = /*Nome Paciente*/ //$JR_EXPR_ID=53$
Bundle.getStringApplication("rotulo_nome_paciente"); //$JR_EXPR_ID=53$
                break;
            }
            case 54 : 
            {
                value = /*CNS*/ //$JR_EXPR_ID=54$
Bundle.getStringApplication("rotulo_cartao_nacional_de_saude_cns"); //$JR_EXPR_ID=54$
                break;
            }
            case 55 : 
            {
                value = /*Data Nascimento*/ //$JR_EXPR_ID=55$
Bundle.getStringApplication("rotulo_data_nascimento"); //$JR_EXPR_ID=55$
                break;
            }
            case 56 : 
            {
                value = /*sexo*/ //$JR_EXPR_ID=56$
Bundle.getStringApplication("rotulo_sexo"); //$JR_EXPR_ID=56$
                break;
            }
            case 57 : 
            {
                value = /*Etnia*/ //$JR_EXPR_ID=57$
Bundle.getStringApplication("rotulo_etnia"); //$JR_EXPR_ID=57$
                break;
            }
            case 58 : 
            {
                value = /*Telefone*/ //$JR_EXPR_ID=58$
Bundle.getStringApplication("rotulo_telefone"); //$JR_EXPR_ID=58$
                break;
            }
            case 59 : 
            {
                value = /*Raca / Cor*/ //$JR_EXPR_ID=59$
Bundle.getStringApplication("rotulo_raca_cor"); //$JR_EXPR_ID=59$
                break;
            }
            case 60 : 
            {
                value = /*Numero Prontuario*/ //$JR_EXPR_ID=60$
Bundle.getStringApplication("rotulo_numero_prontuario"); //$JR_EXPR_ID=60$
                break;
            }
            case 61 : 
            {
                value = /*Nome mae*/ //$JR_EXPR_ID=61$
Bundle.getStringApplication("rotulo_nome_mae"); //$JR_EXPR_ID=61$
                break;
            }
            case 62 : 
            {
                value = /*endereco*/ //$JR_EXPR_ID=62$
Bundle.getStringApplication("rotulo_endereco_rua_numero_bairro"); //$JR_EXPR_ID=62$
                break;
            }
            case 63 : 
            {
                value = /*Cid Principal*/ //$JR_EXPR_ID=63$
Bundle.getStringApplication("rotulo_cid_principal"); //$JR_EXPR_ID=63$
                break;
            }
            case 64 : 
            {
                value = /*cod IBGE*/ //$JR_EXPR_ID=64$
Bundle.getStringApplication("rotulo_cod_ibge_municipio"); //$JR_EXPR_ID=64$
                break;
            }
            case 65 : 
            {
                value = /*UF*/ //$JR_EXPR_ID=65$
Bundle.getStringApplication("rotulo_uf"); //$JR_EXPR_ID=65$
                break;
            }
            case 66 : 
            {
                value = /*CEP*/ //$JR_EXPR_ID=66$
Bundle.getStringApplication("rotulo_cep"); //$JR_EXPR_ID=66$
                break;
            }
            case 67 : 
            {
                value = /*Cod Procedimento*/ //$JR_EXPR_ID=67$
Bundle.getStringApplication("rotulo_codigo_procedimento"); //$JR_EXPR_ID=67$
                break;
            }
            case 68 : 
            {
                value = /*Procedimento Solicitado*/ //$JR_EXPR_ID=68$
Bundle.getStringApplication("rotulo_procedimento_solicitado").toUpperCase(); //$JR_EXPR_ID=68$
                break;
            }
            case 69 : 
            {
                value = /*Cnes*/ //$JR_EXPR_ID=69$
Bundle.getStringApplication("rotulo_cnes"); //$JR_EXPR_ID=69$
                break;
            }
            case 70 : 
            {
                value = /*N Registro*/ //$JR_EXPR_ID=70$
Bundle.getStringApplication("rotulo_numero_de_registro"); //$JR_EXPR_ID=70$
                break;
            }
            case 71 : 
            {
                value = ((java.lang.Long)field_status.getValue()); //$JR_EXPR_ID=71$
                break;
            }
            case 72 : 
            {
                value = /*Classificacao risco*/ //$JR_EXPR_ID=72$
Bundle.getStringApplication("rotulo_classificacao_risco"); //$JR_EXPR_ID=72$
                break;
            }
            case 73 : 
            {
                value = /*Situacao*/ //$JR_EXPR_ID=73$
Bundle.getStringApplication("rotulo_situacao"); //$JR_EXPR_ID=73$
                break;
            }
            case 74 : 
            {
                value = /*Leito*/ //$JR_EXPR_ID=74$
Bundle.getStringApplication("rotulo_leito"); //$JR_EXPR_ID=74$
                break;
            }
            case 75 : 
            {
                value = /*Identificacao do Estabelecimento de Saude*/ //$JR_EXPR_ID=75$
Bundle.getStringApplication("rotulo_identificacao_estabelecimento_saude").toUpperCase(); //$JR_EXPR_ID=75$
                break;
            }
            case 76 : 
            {
                value = ((java.util.Date)field_dataSolicitacao.getValue()); //$JR_EXPR_ID=76$
                break;
            }
            case 77 : 
            {
                value = ((java.lang.Long)field_caraterInternacao.getValue())==1? Bundle.getStringApplication("rotulo_eletivo"):Bundle.getStringApplication("rotulo_emergencia"); //$JR_EXPR_ID=77$
                break;
            }
            case 78 : 
            {
                value = ((br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento)field_procedimentoSolicitadoDesc.getValue()); //$JR_EXPR_ID=78$
                break;
            }
            case 79 : 
            {
                value = ((java.lang.String)field_procedimentoSolicitadoCod.getValue()); //$JR_EXPR_ID=79$
                break;
            }
            case 80 : 
            {
                value = ((java.lang.Long)field_clinica.getValue())==0?Bundle.getStringApplication("rotulo_cirurgica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==1?Bundle.getStringApplication("rotulo_obstetrica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==2? Bundle.getStringApplication("rotulo_clinica_medica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==3?Bundle.getStringApplication("rotulo_psiquiatrica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==4?Bundle.getStringApplication("rotulo_pediatrica") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==5?Bundle.getStringApplication("rotulo_outros") //$JR_EXPR_ID=80$
:((java.lang.Long)field_clinica.getValue())==null?"":""; //$JR_EXPR_ID=80$
                break;
            }
            case 81 : 
            {
                value = ((java.lang.String)field_cidPrincipal.getValue()); //$JR_EXPR_ID=81$
                break;
            }
            case 82 : 
            {
                value = ((java.lang.String)field_usuarioCadSusNome.getValue()); //$JR_EXPR_ID=82$
                break;
            }
            case 83 : 
            {
                value = ((java.lang.String)field_ocorrenciaDescricao.getValue()); //$JR_EXPR_ID=83$
                break;
            }
            case 84 : 
            {
                value = /*data*/ //$JR_EXPR_ID=84$
Bundle.getStringApplication("rotulo_data"); //$JR_EXPR_ID=84$
                break;
            }
            case 85 : 
            {
                value = /*usuario*/ //$JR_EXPR_ID=85$
Bundle.getStringApplication("rotulo_usuario"); //$JR_EXPR_ID=85$
                break;
            }
            case 86 : 
            {
                value = /*registro*/ //$JR_EXPR_ID=86$
Bundle.getStringApplication("rotulo_registro"); //$JR_EXPR_ID=86$
                break;
            }
            case 87 : 
            {
                value = /*Estabelecimento*/ //$JR_EXPR_ID=87$
Bundle.getStringApplication("rotulo_estabelecimento"); //$JR_EXPR_ID=87$
                break;
            }
            case 88 : 
            {
                value = /*Descricao*/ //$JR_EXPR_ID=88$
Bundle.getStringApplication("rotulo_descricao"); //$JR_EXPR_ID=88$
                break;
            }
            case 89 : 
            {
                value = /*dados clinicos*/ //$JR_EXPR_ID=89$
Bundle.getStringApplication("rotulo_dados_clinicos").toUpperCase(); //$JR_EXPR_ID=89$
                break;
            }
            case 90 : 
            {
                value = ((java.util.Date)field_dataCadastro.getValue()); //$JR_EXPR_ID=90$
                break;
            }
            case 91 : 
            {
                value = ((br.com.ksisolucoes.vo.controle.Usuario)field_usuarioCadastro.getValue()); //$JR_EXPR_ID=91$
                break;
            }
            case 92 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=92$
                break;
            }
            case 93 : 
            {
                value = ((java.lang.String)parameter_RUA_UNIDADE.getValue()) + (((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) != null ? ", " + ((java.lang.String)parameter_NUMERO_UNIDADE.getValue()) : "") //$JR_EXPR_ID=93$
    + " - " + ((java.lang.String)parameter_BAIRRO_UNIDADE.getValue()) + (((java.lang.String)parameter_CEP_UNIDADE.getValue()) != null ? " - CEP " + ((java.lang.String)parameter_CEP_UNIDADE.getValue()) : ""); //$JR_EXPR_ID=93$
                break;
            }
            case 94 : 
            {
                value = ((java.lang.Boolean)parameter_EXIBIR_RODAPE.getValue()); //$JR_EXPR_ID=94$
                break;
            }
            case 95 : 
            {
                value = ((java.lang.String)parameter_CIDADE_UNIDADE.getValue()) + " - " + ((java.lang.String)parameter_UF_UNIDADE.getValue()) //$JR_EXPR_ID=95$
    + (((java.lang.String)parameter_FONE_UNIDADE.getValue()) == null //$JR_EXPR_ID=95$
        ? "" //$JR_EXPR_ID=95$
        : " | " + ((java.lang.String)parameter_FONE_UNIDADE.getValue())); //$JR_EXPR_ID=95$
                break;
            }
           default :
           {
           }
        }
        
        return value;
    }


}
