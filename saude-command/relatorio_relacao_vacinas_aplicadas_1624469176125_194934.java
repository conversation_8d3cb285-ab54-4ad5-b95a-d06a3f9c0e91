/*
 * Generated by JasperReports - 23/06/21 14:26
 */
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.fill.*;

import java.util.*;
import java.math.*;
import java.text.*;
import java.io.*;
import java.net.*;

import net.sf.jasperreports.engine.*;
import java.util.*;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao;
import net.sf.jasperreports.engine.data.*;


/**
 *
 */
public class relatorio_relacao_vacinas_aplicadas_1624469176125_194934 extends JREvaluator
{


    /**
     *
     */
    private JRFillParameter parameter_REPORT_CONNECTION = null;
    private JRFillParameter parameter_TITULO_JANELA_REPORT = null;
    private JRFillParameter parameter_VERSAO_SISTEMA = null;
    private JRFillParameter parameter_JASPER_REPORT = null;
    private JRFillParameter parameter_USUARIO_REPORT = null;
    private JRFillParameter parameter_REPORT_TIME_ZONE = null;
    private JRFillParameter parameter_CABECALHO_PADRAO_RELATORIO = null;
    private JRFillParameter parameter_REPORT_TEMPLATES = null;
    private JRFillParameter parameter_TITULO_REPORT = null;
    private JRFillParameter parameter_REPORT_MAX_COUNT = null;
    private JRFillParameter parameter_REPORT_SCRIPTLET = null;
    private JRFillParameter parameter_formaApresentacao = null;
    private JRFillParameter parameter_DESCRICAO_PARAMETRO_PARM = null;
    private JRFillParameter parameter_REPORT_PARAMETERS_MAP = null;
    private JRFillParameter parameter_REPORT_RESOURCE_BUNDLE = null;
    private JRFillParameter parameter_REPORT_DATA_SOURCE = null;
    private JRFillParameter parameter_SORT_FIELDS = null;
    private JRFillParameter parameter_IS_IGNORE_PAGINATION = null;
    private JRFillParameter parameter_FILTER = null;
    private JRFillParameter parameter_CAMINHO_IMAGEM_PADRAO = null;
    private JRFillParameter parameter_REPORT_LOCALE = null;
    private JRFillParameter parameter_CLIENTE_REPORT = null;
    private JRFillParameter parameter_REPORT_FILE_RESOLVER = null;
    private JRFillParameter parameter_USUARIO_LOGADO = null;
    private JRFillParameter parameter_REPORT_FORMAT_FACTORY = null;
    private JRFillParameter parameter_NOME_RELATORIO_REPORT = null;
    private JRFillParameter parameter_REPORT_CONTEXT = null;
    private JRFillParameter parameter_REPORT_CLASS_LOADER = null;
    private JRFillParameter parameter_REPORT_URL_HANDLER_FACTORY = null;
    private JRFillParameter parameter_REPORT_VIRTUALIZER = null;
    private JRFillParameter parameter_CABECALHO_ADICIONAL_1 = null;
    private JRFillField field_enderecoFormatado = null;
    private JRFillField field_unidadeReferencia = null;
    private JRFillField field_descricaoIdade = null;
    private JRFillField field_codigoPaciente = null;
    private JRFillField field_cpfFormatado = null;
    private JRFillField field_nomePaciente = null;
    private JRFillField field_grupoVacinacaoFormatado = null;
    private JRFillField field_cnsFormatado = null;
    private JRFillVariable variable_PAGE_NUMBER = null;
    private JRFillVariable variable_COLUMN_NUMBER = null;
    private JRFillVariable variable_REPORT_COUNT = null;
    private JRFillVariable variable_PAGE_COUNT = null;
    private JRFillVariable variable_COLUMN_COUNT = null;
    private JRFillVariable variable_GERAL_COUNT = null;
    private JRFillVariable variable_FormaApresentacao_COUNT = null;
    private JRFillVariable variable_totalFA = null;
    private JRFillVariable variable_totalGeral = null;


    /**
     *
     */
    public void customizedInit(
        Map pm,
        Map fm,
        Map vm
        )
    {
        initParams(pm);
        initFields(fm);
        initVars(vm);
    }


    /**
     *
     */
    private void initParams(Map pm)
    {
        parameter_REPORT_CONNECTION = (JRFillParameter)pm.get("REPORT_CONNECTION");
        parameter_TITULO_JANELA_REPORT = (JRFillParameter)pm.get("TITULO_JANELA_REPORT");
        parameter_VERSAO_SISTEMA = (JRFillParameter)pm.get("VERSAO_SISTEMA");
        parameter_JASPER_REPORT = (JRFillParameter)pm.get("JASPER_REPORT");
        parameter_USUARIO_REPORT = (JRFillParameter)pm.get("USUARIO_REPORT");
        parameter_REPORT_TIME_ZONE = (JRFillParameter)pm.get("REPORT_TIME_ZONE");
        parameter_CABECALHO_PADRAO_RELATORIO = (JRFillParameter)pm.get("CABECALHO_PADRAO_RELATORIO");
        parameter_REPORT_TEMPLATES = (JRFillParameter)pm.get("REPORT_TEMPLATES");
        parameter_TITULO_REPORT = (JRFillParameter)pm.get("TITULO_REPORT");
        parameter_REPORT_MAX_COUNT = (JRFillParameter)pm.get("REPORT_MAX_COUNT");
        parameter_REPORT_SCRIPTLET = (JRFillParameter)pm.get("REPORT_SCRIPTLET");
        parameter_formaApresentacao = (JRFillParameter)pm.get("formaApresentacao");
        parameter_DESCRICAO_PARAMETRO_PARM = (JRFillParameter)pm.get("DESCRICAO_PARAMETRO_PARM");
        parameter_REPORT_PARAMETERS_MAP = (JRFillParameter)pm.get("REPORT_PARAMETERS_MAP");
        parameter_REPORT_RESOURCE_BUNDLE = (JRFillParameter)pm.get("REPORT_RESOURCE_BUNDLE");
        parameter_REPORT_DATA_SOURCE = (JRFillParameter)pm.get("REPORT_DATA_SOURCE");
        parameter_SORT_FIELDS = (JRFillParameter)pm.get("SORT_FIELDS");
        parameter_IS_IGNORE_PAGINATION = (JRFillParameter)pm.get("IS_IGNORE_PAGINATION");
        parameter_FILTER = (JRFillParameter)pm.get("FILTER");
        parameter_CAMINHO_IMAGEM_PADRAO = (JRFillParameter)pm.get("CAMINHO_IMAGEM_PADRAO");
        parameter_REPORT_LOCALE = (JRFillParameter)pm.get("REPORT_LOCALE");
        parameter_CLIENTE_REPORT = (JRFillParameter)pm.get("CLIENTE_REPORT");
        parameter_REPORT_FILE_RESOLVER = (JRFillParameter)pm.get("REPORT_FILE_RESOLVER");
        parameter_USUARIO_LOGADO = (JRFillParameter)pm.get("USUARIO_LOGADO");
        parameter_REPORT_FORMAT_FACTORY = (JRFillParameter)pm.get("REPORT_FORMAT_FACTORY");
        parameter_NOME_RELATORIO_REPORT = (JRFillParameter)pm.get("NOME_RELATORIO_REPORT");
        parameter_REPORT_CONTEXT = (JRFillParameter)pm.get("REPORT_CONTEXT");
        parameter_REPORT_CLASS_LOADER = (JRFillParameter)pm.get("REPORT_CLASS_LOADER");
        parameter_REPORT_URL_HANDLER_FACTORY = (JRFillParameter)pm.get("REPORT_URL_HANDLER_FACTORY");
        parameter_REPORT_VIRTUALIZER = (JRFillParameter)pm.get("REPORT_VIRTUALIZER");
        parameter_CABECALHO_ADICIONAL_1 = (JRFillParameter)pm.get("CABECALHO_ADICIONAL_1");
    }


    /**
     *
     */
    private void initFields(Map fm)
    {
        field_enderecoFormatado = (JRFillField)fm.get("enderecoFormatado");
        field_unidadeReferencia = (JRFillField)fm.get("unidadeReferencia");
        field_descricaoIdade = (JRFillField)fm.get("descricaoIdade");
        field_codigoPaciente = (JRFillField)fm.get("codigoPaciente");
        field_cpfFormatado = (JRFillField)fm.get("cpfFormatado");
        field_nomePaciente = (JRFillField)fm.get("nomePaciente");
        field_grupoVacinacaoFormatado = (JRFillField)fm.get("grupoVacinacaoFormatado");
        field_cnsFormatado = (JRFillField)fm.get("cnsFormatado");
    }


    /**
     *
     */
    private void initVars(Map vm)
    {
        variable_PAGE_NUMBER = (JRFillVariable)vm.get("PAGE_NUMBER");
        variable_COLUMN_NUMBER = (JRFillVariable)vm.get("COLUMN_NUMBER");
        variable_REPORT_COUNT = (JRFillVariable)vm.get("REPORT_COUNT");
        variable_PAGE_COUNT = (JRFillVariable)vm.get("PAGE_COUNT");
        variable_COLUMN_COUNT = (JRFillVariable)vm.get("COLUMN_COUNT");
        variable_GERAL_COUNT = (JRFillVariable)vm.get("GERAL_COUNT");
        variable_FormaApresentacao_COUNT = (JRFillVariable)vm.get("FormaApresentacao_COUNT");
        variable_totalFA = (JRFillVariable)vm.get("totalFA");
        variable_totalGeral = (JRFillVariable)vm.get("totalGeral");
    }


    /**
     *
     */
    public Object evaluate(int id) throws Throwable
    {
        Object value = null;

        switch (id)
        {
            case 0 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=0$
                break;
            }
            case 1 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=1$
                break;
            }
            case 2 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=2$
                break;
            }
            case 3 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=3$
                break;
            }
            case 4 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=4$
                break;
            }
            case 5 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=5$
                break;
            }
            case 6 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=6$
                break;
            }
            case 7 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=7$
                break;
            }
            case 8 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=8$
                break;
            }
            case 9 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=9$
                break;
            }
            case 10 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=10$
                break;
            }
            case 11 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=11$
                break;
            }
            case 12 : 
            {
                value = ((java.lang.Long)field_codigoPaciente.getValue()); //$JR_EXPR_ID=12$
                break;
            }
            case 13 : 
            {
                value = ((java.lang.Long)field_codigoPaciente.getValue()); //$JR_EXPR_ID=13$
                break;
            }
            case 14 : 
            {
                value = null; //$JR_EXPR_ID=14$
                break;
            }
            case 15 : 
            {
                value = ((java.lang.Integer)variable_totalGeral.getValue()) == null ? "0": ((java.lang.Integer)variable_totalGeral.getValue()); //$JR_EXPR_ID=15$
                break;
            }
            case 16 : 
            {
                value = /*Total Geral*/ //$JR_EXPR_ID=16$
Bundle.getStringApplication("rotulo_total_geral") + ": "; //$JR_EXPR_ID=16$
                break;
            }
            case 17 : 
            {
                value = FormaApresentacao.UNIDADE.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=17$
? //$JR_EXPR_ID=17$
    ((java.lang.String)field_unidadeReferencia.getValue())!=null //$JR_EXPR_ID=17$
            ? //$JR_EXPR_ID=17$
                ((java.lang.String)field_unidadeReferencia.getValue()) //$JR_EXPR_ID=17$
            : //$JR_EXPR_ID=17$
                Bundle.getStringApplication("rotulo_desconhecido") //$JR_EXPR_ID=17$
: //$JR_EXPR_ID=17$
    FormaApresentacao.GRUPO_ATENDIMENTO.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=17$
    ? //$JR_EXPR_ID=17$
        ((java.lang.String)field_grupoVacinacaoFormatado.getValue()) //$JR_EXPR_ID=17$
    : //$JR_EXPR_ID=17$
       null; //$JR_EXPR_ID=17$
                break;
            }
            case 18 : 
            {
                value = ((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue()) + ": " + //$JR_EXPR_ID=18$
( //$JR_EXPR_ID=18$
    FormaApresentacao.UNIDADE.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=18$
    ? //$JR_EXPR_ID=18$
        ((java.lang.String)field_unidadeReferencia.getValue())!=null //$JR_EXPR_ID=18$
            ? //$JR_EXPR_ID=18$
                ((java.lang.String)field_unidadeReferencia.getValue()) //$JR_EXPR_ID=18$
            : //$JR_EXPR_ID=18$
                Bundle.getStringApplication("rotulo_desconhecido") //$JR_EXPR_ID=18$
    : //$JR_EXPR_ID=18$
        FormaApresentacao.GRUPO_ATENDIMENTO.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=18$
        ? //$JR_EXPR_ID=18$
            ((java.lang.String)field_grupoVacinacaoFormatado.getValue()) //$JR_EXPR_ID=18$
        : //$JR_EXPR_ID=18$
            null //$JR_EXPR_ID=18$
); //$JR_EXPR_ID=18$
                break;
            }
            case 19 : 
            {
                value = /*CNS*/ //$JR_EXPR_ID=19$
Bundle.getStringApplication("rotulo_cns"); //$JR_EXPR_ID=19$
                break;
            }
            case 20 : 
            {
                value = /*Paciente*/ //$JR_EXPR_ID=20$
Bundle.getStringApplication("rotulo_paciente"); //$JR_EXPR_ID=20$
                break;
            }
            case 21 : 
            {
                value = /*Idade*/ //$JR_EXPR_ID=21$
Bundle.getStringApplication("rotulo_idade"); //$JR_EXPR_ID=21$
                break;
            }
            case 22 : 
            {
                value = /*Unidade*/ //$JR_EXPR_ID=22$
Bundle.getStringApplication("rotulo_unidade"); //$JR_EXPR_ID=22$
                break;
            }
            case 23 : 
            {
                value = /*CPF*/ //$JR_EXPR_ID=23$
Bundle.getStringApplication("rotulo_cpf"); //$JR_EXPR_ID=23$
                break;
            }
            case 24 : 
            {
                value = /*Endereço*/ //$JR_EXPR_ID=24$
Bundle.getStringApplication("rotulo_endereco"); //$JR_EXPR_ID=24$
                break;
            }
            case 25 : 
            {
                value = /*Grupo Atendimento*/ //$JR_EXPR_ID=25$
Bundle.getStringApplication("rotulo_grupo_atendimento"); //$JR_EXPR_ID=25$
                break;
            }
            case 26 : 
            {
                value = ((java.lang.Integer)variable_totalFA.getValue()) == null ? "0":((java.lang.Integer)variable_totalFA.getValue()); //$JR_EXPR_ID=26$
                break;
            }
            case 27 : 
            {
                value = /*Total*/ Bundle.getStringApplication("rotulo_total") + ": "; //$JR_EXPR_ID=27$
                break;
            }
            case 28 : 
            {
                value = ((java.lang.String)parameter_CAMINHO_IMAGEM_PADRAO.getValue()); //$JR_EXPR_ID=28$
                break;
            }
            case 29 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_PADRAO_RELATORIO.getValue()); //$JR_EXPR_ID=29$
                break;
            }
            case 30 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_1.getValue()); //$JR_EXPR_ID=30$
                break;
            }
            case 31 : 
            {
                value = br.com.ksisolucoes.util.Bundle.getStringApplication( "rotulo_pagina" ) + " " + br.com.ksisolucoes.util.Bundle.getStringApplication("format_numero_pagina_relatorio",((java.lang.Integer)variable_PAGE_NUMBER.getValue())) + " " + br.com.ksisolucoes.util.Bundle.getStringApplication( "rotulo_de" ); //$JR_EXPR_ID=31$
                break;
            }
            case 32 : 
            {
                value = br.com.ksisolucoes.util.Bundle.getStringApplication("format_numero_pagina_relatorio",((java.lang.Integer)variable_PAGE_NUMBER.getValue())); //$JR_EXPR_ID=32$
                break;
            }
            case 33 : 
            {
                value = ((java.lang.String)parameter_TITULO_REPORT.getValue()); //$JR_EXPR_ID=33$
                break;
            }
            case 34 : 
            {
                value = ((java.lang.String)parameter_TITULO_JANELA_REPORT.getValue()); //$JR_EXPR_ID=34$
                break;
            }
            case 35 : 
            {
                value = ((java.lang.String)parameter_CLIENTE_REPORT.getValue()); //$JR_EXPR_ID=35$
                break;
            }
            case 36 : 
            {
                value = ((java.lang.String)parameter_DESCRICAO_PARAMETRO_PARM.getValue()); //$JR_EXPR_ID=36$
                break;
            }
            case 37 : 
            {
                value = ((java.lang.String)field_cnsFormatado.getValue()); //$JR_EXPR_ID=37$
                break;
            }
            case 38 : 
            {
                value = ((java.lang.String)field_nomePaciente.getValue()); //$JR_EXPR_ID=38$
                break;
            }
            case 39 : 
            {
                value = ((java.lang.String)field_descricaoIdade.getValue()); //$JR_EXPR_ID=39$
                break;
            }
            case 40 : 
            {
                value = ((java.lang.String)field_cpfFormatado.getValue()); //$JR_EXPR_ID=40$
                break;
            }
            case 41 : 
            {
                value = ((java.lang.String)field_enderecoFormatado.getValue()); //$JR_EXPR_ID=41$
                break;
            }
            case 42 : 
            {
                value = ((java.lang.String)field_grupoVacinacaoFormatado.getValue()); //$JR_EXPR_ID=42$
                break;
            }
            case 43 : 
            {
                value = ((java.lang.String)field_unidadeReferencia.getValue()); //$JR_EXPR_ID=43$
                break;
            }
            case 44 : 
            {
                value = ((java.lang.String)parameter_NOME_RELATORIO_REPORT.getValue()); //$JR_EXPR_ID=44$
                break;
            }
            case 45 : 
            {
                value = br.com.ksisolucoes.util.Bundle.getStringApplication( "rodape_report",new Object[]{((java.lang.String)parameter_USUARIO_LOGADO.getValue()), br.com.ksisolucoes.util.Data.getDataAtual(), ((java.lang.String)parameter_VERSAO_SISTEMA.getValue())}); //$JR_EXPR_ID=45$
                break;
            }
           default :
           {
           }
        }
        
        return value;
    }


    /**
     *
     */
    public Object evaluateOld(int id) throws Throwable
    {
        Object value = null;

        switch (id)
        {
            case 0 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=0$
                break;
            }
            case 1 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=1$
                break;
            }
            case 2 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=2$
                break;
            }
            case 3 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=3$
                break;
            }
            case 4 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=4$
                break;
            }
            case 5 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=5$
                break;
            }
            case 6 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=6$
                break;
            }
            case 7 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=7$
                break;
            }
            case 8 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=8$
                break;
            }
            case 9 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=9$
                break;
            }
            case 10 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=10$
                break;
            }
            case 11 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=11$
                break;
            }
            case 12 : 
            {
                value = ((java.lang.Long)field_codigoPaciente.getOldValue()); //$JR_EXPR_ID=12$
                break;
            }
            case 13 : 
            {
                value = ((java.lang.Long)field_codigoPaciente.getOldValue()); //$JR_EXPR_ID=13$
                break;
            }
            case 14 : 
            {
                value = null; //$JR_EXPR_ID=14$
                break;
            }
            case 15 : 
            {
                value = ((java.lang.Integer)variable_totalGeral.getOldValue()) == null ? "0": ((java.lang.Integer)variable_totalGeral.getOldValue()); //$JR_EXPR_ID=15$
                break;
            }
            case 16 : 
            {
                value = /*Total Geral*/ //$JR_EXPR_ID=16$
Bundle.getStringApplication("rotulo_total_geral") + ": "; //$JR_EXPR_ID=16$
                break;
            }
            case 17 : 
            {
                value = FormaApresentacao.UNIDADE.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=17$
? //$JR_EXPR_ID=17$
    ((java.lang.String)field_unidadeReferencia.getOldValue())!=null //$JR_EXPR_ID=17$
            ? //$JR_EXPR_ID=17$
                ((java.lang.String)field_unidadeReferencia.getOldValue()) //$JR_EXPR_ID=17$
            : //$JR_EXPR_ID=17$
                Bundle.getStringApplication("rotulo_desconhecido") //$JR_EXPR_ID=17$
: //$JR_EXPR_ID=17$
    FormaApresentacao.GRUPO_ATENDIMENTO.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=17$
    ? //$JR_EXPR_ID=17$
        ((java.lang.String)field_grupoVacinacaoFormatado.getOldValue()) //$JR_EXPR_ID=17$
    : //$JR_EXPR_ID=17$
       null; //$JR_EXPR_ID=17$
                break;
            }
            case 18 : 
            {
                value = ((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue()) + ": " + //$JR_EXPR_ID=18$
( //$JR_EXPR_ID=18$
    FormaApresentacao.UNIDADE.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=18$
    ? //$JR_EXPR_ID=18$
        ((java.lang.String)field_unidadeReferencia.getOldValue())!=null //$JR_EXPR_ID=18$
            ? //$JR_EXPR_ID=18$
                ((java.lang.String)field_unidadeReferencia.getOldValue()) //$JR_EXPR_ID=18$
            : //$JR_EXPR_ID=18$
                Bundle.getStringApplication("rotulo_desconhecido") //$JR_EXPR_ID=18$
    : //$JR_EXPR_ID=18$
        FormaApresentacao.GRUPO_ATENDIMENTO.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=18$
        ? //$JR_EXPR_ID=18$
            ((java.lang.String)field_grupoVacinacaoFormatado.getOldValue()) //$JR_EXPR_ID=18$
        : //$JR_EXPR_ID=18$
            null //$JR_EXPR_ID=18$
); //$JR_EXPR_ID=18$
                break;
            }
            case 19 : 
            {
                value = /*CNS*/ //$JR_EXPR_ID=19$
Bundle.getStringApplication("rotulo_cns"); //$JR_EXPR_ID=19$
                break;
            }
            case 20 : 
            {
                value = /*Paciente*/ //$JR_EXPR_ID=20$
Bundle.getStringApplication("rotulo_paciente"); //$JR_EXPR_ID=20$
                break;
            }
            case 21 : 
            {
                value = /*Idade*/ //$JR_EXPR_ID=21$
Bundle.getStringApplication("rotulo_idade"); //$JR_EXPR_ID=21$
                break;
            }
            case 22 : 
            {
                value = /*Unidade*/ //$JR_EXPR_ID=22$
Bundle.getStringApplication("rotulo_unidade"); //$JR_EXPR_ID=22$
                break;
            }
            case 23 : 
            {
                value = /*CPF*/ //$JR_EXPR_ID=23$
Bundle.getStringApplication("rotulo_cpf"); //$JR_EXPR_ID=23$
                break;
            }
            case 24 : 
            {
                value = /*Endereço*/ //$JR_EXPR_ID=24$
Bundle.getStringApplication("rotulo_endereco"); //$JR_EXPR_ID=24$
                break;
            }
            case 25 : 
            {
                value = /*Grupo Atendimento*/ //$JR_EXPR_ID=25$
Bundle.getStringApplication("rotulo_grupo_atendimento"); //$JR_EXPR_ID=25$
                break;
            }
            case 26 : 
            {
                value = ((java.lang.Integer)variable_totalFA.getOldValue()) == null ? "0":((java.lang.Integer)variable_totalFA.getOldValue()); //$JR_EXPR_ID=26$
                break;
            }
            case 27 : 
            {
                value = /*Total*/ Bundle.getStringApplication("rotulo_total") + ": "; //$JR_EXPR_ID=27$
                break;
            }
            case 28 : 
            {
                value = ((java.lang.String)parameter_CAMINHO_IMAGEM_PADRAO.getValue()); //$JR_EXPR_ID=28$
                break;
            }
            case 29 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_PADRAO_RELATORIO.getValue()); //$JR_EXPR_ID=29$
                break;
            }
            case 30 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_1.getValue()); //$JR_EXPR_ID=30$
                break;
            }
            case 31 : 
            {
                value = br.com.ksisolucoes.util.Bundle.getStringApplication( "rotulo_pagina" ) + " " + br.com.ksisolucoes.util.Bundle.getStringApplication("format_numero_pagina_relatorio",((java.lang.Integer)variable_PAGE_NUMBER.getOldValue())) + " " + br.com.ksisolucoes.util.Bundle.getStringApplication( "rotulo_de" ); //$JR_EXPR_ID=31$
                break;
            }
            case 32 : 
            {
                value = br.com.ksisolucoes.util.Bundle.getStringApplication("format_numero_pagina_relatorio",((java.lang.Integer)variable_PAGE_NUMBER.getOldValue())); //$JR_EXPR_ID=32$
                break;
            }
            case 33 : 
            {
                value = ((java.lang.String)parameter_TITULO_REPORT.getValue()); //$JR_EXPR_ID=33$
                break;
            }
            case 34 : 
            {
                value = ((java.lang.String)parameter_TITULO_JANELA_REPORT.getValue()); //$JR_EXPR_ID=34$
                break;
            }
            case 35 : 
            {
                value = ((java.lang.String)parameter_CLIENTE_REPORT.getValue()); //$JR_EXPR_ID=35$
                break;
            }
            case 36 : 
            {
                value = ((java.lang.String)parameter_DESCRICAO_PARAMETRO_PARM.getValue()); //$JR_EXPR_ID=36$
                break;
            }
            case 37 : 
            {
                value = ((java.lang.String)field_cnsFormatado.getOldValue()); //$JR_EXPR_ID=37$
                break;
            }
            case 38 : 
            {
                value = ((java.lang.String)field_nomePaciente.getOldValue()); //$JR_EXPR_ID=38$
                break;
            }
            case 39 : 
            {
                value = ((java.lang.String)field_descricaoIdade.getOldValue()); //$JR_EXPR_ID=39$
                break;
            }
            case 40 : 
            {
                value = ((java.lang.String)field_cpfFormatado.getOldValue()); //$JR_EXPR_ID=40$
                break;
            }
            case 41 : 
            {
                value = ((java.lang.String)field_enderecoFormatado.getOldValue()); //$JR_EXPR_ID=41$
                break;
            }
            case 42 : 
            {
                value = ((java.lang.String)field_grupoVacinacaoFormatado.getOldValue()); //$JR_EXPR_ID=42$
                break;
            }
            case 43 : 
            {
                value = ((java.lang.String)field_unidadeReferencia.getOldValue()); //$JR_EXPR_ID=43$
                break;
            }
            case 44 : 
            {
                value = ((java.lang.String)parameter_NOME_RELATORIO_REPORT.getValue()); //$JR_EXPR_ID=44$
                break;
            }
            case 45 : 
            {
                value = br.com.ksisolucoes.util.Bundle.getStringApplication( "rodape_report",new Object[]{((java.lang.String)parameter_USUARIO_LOGADO.getValue()), br.com.ksisolucoes.util.Data.getDataAtual(), ((java.lang.String)parameter_VERSAO_SISTEMA.getValue())}); //$JR_EXPR_ID=45$
                break;
            }
           default :
           {
           }
        }
        
        return value;
    }


    /**
     *
     */
    public Object evaluateEstimated(int id) throws Throwable
    {
        Object value = null;

        switch (id)
        {
            case 0 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=0$
                break;
            }
            case 1 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=1$
                break;
            }
            case 2 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=2$
                break;
            }
            case 3 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=3$
                break;
            }
            case 4 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=4$
                break;
            }
            case 5 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=5$
                break;
            }
            case 6 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=6$
                break;
            }
            case 7 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=7$
                break;
            }
            case 8 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=8$
                break;
            }
            case 9 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=9$
                break;
            }
            case 10 : 
            {
                value = new java.lang.Integer(1); //$JR_EXPR_ID=10$
                break;
            }
            case 11 : 
            {
                value = new java.lang.Integer(0); //$JR_EXPR_ID=11$
                break;
            }
            case 12 : 
            {
                value = ((java.lang.Long)field_codigoPaciente.getValue()); //$JR_EXPR_ID=12$
                break;
            }
            case 13 : 
            {
                value = ((java.lang.Long)field_codigoPaciente.getValue()); //$JR_EXPR_ID=13$
                break;
            }
            case 14 : 
            {
                value = null; //$JR_EXPR_ID=14$
                break;
            }
            case 15 : 
            {
                value = ((java.lang.Integer)variable_totalGeral.getEstimatedValue()) == null ? "0": ((java.lang.Integer)variable_totalGeral.getEstimatedValue()); //$JR_EXPR_ID=15$
                break;
            }
            case 16 : 
            {
                value = /*Total Geral*/ //$JR_EXPR_ID=16$
Bundle.getStringApplication("rotulo_total_geral") + ": "; //$JR_EXPR_ID=16$
                break;
            }
            case 17 : 
            {
                value = FormaApresentacao.UNIDADE.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=17$
? //$JR_EXPR_ID=17$
    ((java.lang.String)field_unidadeReferencia.getValue())!=null //$JR_EXPR_ID=17$
            ? //$JR_EXPR_ID=17$
                ((java.lang.String)field_unidadeReferencia.getValue()) //$JR_EXPR_ID=17$
            : //$JR_EXPR_ID=17$
                Bundle.getStringApplication("rotulo_desconhecido") //$JR_EXPR_ID=17$
: //$JR_EXPR_ID=17$
    FormaApresentacao.GRUPO_ATENDIMENTO.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=17$
    ? //$JR_EXPR_ID=17$
        ((java.lang.String)field_grupoVacinacaoFormatado.getValue()) //$JR_EXPR_ID=17$
    : //$JR_EXPR_ID=17$
       null; //$JR_EXPR_ID=17$
                break;
            }
            case 18 : 
            {
                value = ((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue()) + ": " + //$JR_EXPR_ID=18$
( //$JR_EXPR_ID=18$
    FormaApresentacao.UNIDADE.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=18$
    ? //$JR_EXPR_ID=18$
        ((java.lang.String)field_unidadeReferencia.getValue())!=null //$JR_EXPR_ID=18$
            ? //$JR_EXPR_ID=18$
                ((java.lang.String)field_unidadeReferencia.getValue()) //$JR_EXPR_ID=18$
            : //$JR_EXPR_ID=18$
                Bundle.getStringApplication("rotulo_desconhecido") //$JR_EXPR_ID=18$
    : //$JR_EXPR_ID=18$
        FormaApresentacao.GRUPO_ATENDIMENTO.equals(((br.com.ksisolucoes.report.vacina.dto.RelatorioPacientesGrupoVacinacaoDTOParam.FormaApresentacao)parameter_formaApresentacao.getValue())) //$JR_EXPR_ID=18$
        ? //$JR_EXPR_ID=18$
            ((java.lang.String)field_grupoVacinacaoFormatado.getValue()) //$JR_EXPR_ID=18$
        : //$JR_EXPR_ID=18$
            null //$JR_EXPR_ID=18$
); //$JR_EXPR_ID=18$
                break;
            }
            case 19 : 
            {
                value = /*CNS*/ //$JR_EXPR_ID=19$
Bundle.getStringApplication("rotulo_cns"); //$JR_EXPR_ID=19$
                break;
            }
            case 20 : 
            {
                value = /*Paciente*/ //$JR_EXPR_ID=20$
Bundle.getStringApplication("rotulo_paciente"); //$JR_EXPR_ID=20$
                break;
            }
            case 21 : 
            {
                value = /*Idade*/ //$JR_EXPR_ID=21$
Bundle.getStringApplication("rotulo_idade"); //$JR_EXPR_ID=21$
                break;
            }
            case 22 : 
            {
                value = /*Unidade*/ //$JR_EXPR_ID=22$
Bundle.getStringApplication("rotulo_unidade"); //$JR_EXPR_ID=22$
                break;
            }
            case 23 : 
            {
                value = /*CPF*/ //$JR_EXPR_ID=23$
Bundle.getStringApplication("rotulo_cpf"); //$JR_EXPR_ID=23$
                break;
            }
            case 24 : 
            {
                value = /*Endereço*/ //$JR_EXPR_ID=24$
Bundle.getStringApplication("rotulo_endereco"); //$JR_EXPR_ID=24$
                break;
            }
            case 25 : 
            {
                value = /*Grupo Atendimento*/ //$JR_EXPR_ID=25$
Bundle.getStringApplication("rotulo_grupo_atendimento"); //$JR_EXPR_ID=25$
                break;
            }
            case 26 : 
            {
                value = ((java.lang.Integer)variable_totalFA.getEstimatedValue()) == null ? "0":((java.lang.Integer)variable_totalFA.getEstimatedValue()); //$JR_EXPR_ID=26$
                break;
            }
            case 27 : 
            {
                value = /*Total*/ Bundle.getStringApplication("rotulo_total") + ": "; //$JR_EXPR_ID=27$
                break;
            }
            case 28 : 
            {
                value = ((java.lang.String)parameter_CAMINHO_IMAGEM_PADRAO.getValue()); //$JR_EXPR_ID=28$
                break;
            }
            case 29 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_PADRAO_RELATORIO.getValue()); //$JR_EXPR_ID=29$
                break;
            }
            case 30 : 
            {
                value = ((java.lang.String)parameter_CABECALHO_ADICIONAL_1.getValue()); //$JR_EXPR_ID=30$
                break;
            }
            case 31 : 
            {
                value = br.com.ksisolucoes.util.Bundle.getStringApplication( "rotulo_pagina" ) + " " + br.com.ksisolucoes.util.Bundle.getStringApplication("format_numero_pagina_relatorio",((java.lang.Integer)variable_PAGE_NUMBER.getEstimatedValue())) + " " + br.com.ksisolucoes.util.Bundle.getStringApplication( "rotulo_de" ); //$JR_EXPR_ID=31$
                break;
            }
            case 32 : 
            {
                value = br.com.ksisolucoes.util.Bundle.getStringApplication("format_numero_pagina_relatorio",((java.lang.Integer)variable_PAGE_NUMBER.getEstimatedValue())); //$JR_EXPR_ID=32$
                break;
            }
            case 33 : 
            {
                value = ((java.lang.String)parameter_TITULO_REPORT.getValue()); //$JR_EXPR_ID=33$
                break;
            }
            case 34 : 
            {
                value = ((java.lang.String)parameter_TITULO_JANELA_REPORT.getValue()); //$JR_EXPR_ID=34$
                break;
            }
            case 35 : 
            {
                value = ((java.lang.String)parameter_CLIENTE_REPORT.getValue()); //$JR_EXPR_ID=35$
                break;
            }
            case 36 : 
            {
                value = ((java.lang.String)parameter_DESCRICAO_PARAMETRO_PARM.getValue()); //$JR_EXPR_ID=36$
                break;
            }
            case 37 : 
            {
                value = ((java.lang.String)field_cnsFormatado.getValue()); //$JR_EXPR_ID=37$
                break;
            }
            case 38 : 
            {
                value = ((java.lang.String)field_nomePaciente.getValue()); //$JR_EXPR_ID=38$
                break;
            }
            case 39 : 
            {
                value = ((java.lang.String)field_descricaoIdade.getValue()); //$JR_EXPR_ID=39$
                break;
            }
            case 40 : 
            {
                value = ((java.lang.String)field_cpfFormatado.getValue()); //$JR_EXPR_ID=40$
                break;
            }
            case 41 : 
            {
                value = ((java.lang.String)field_enderecoFormatado.getValue()); //$JR_EXPR_ID=41$
                break;
            }
            case 42 : 
            {
                value = ((java.lang.String)field_grupoVacinacaoFormatado.getValue()); //$JR_EXPR_ID=42$
                break;
            }
            case 43 : 
            {
                value = ((java.lang.String)field_unidadeReferencia.getValue()); //$JR_EXPR_ID=43$
                break;
            }
            case 44 : 
            {
                value = ((java.lang.String)parameter_NOME_RELATORIO_REPORT.getValue()); //$JR_EXPR_ID=44$
                break;
            }
            case 45 : 
            {
                value = br.com.ksisolucoes.util.Bundle.getStringApplication( "rodape_report",new Object[]{((java.lang.String)parameter_USUARIO_LOGADO.getValue()), br.com.ksisolucoes.util.Data.getDataAtual(), ((java.lang.String)parameter_VERSAO_SISTEMA.getValue())}); //$JR_EXPR_ID=45$
                break;
            }
           default :
           {
           }
        }
        
        return value;
    }


}
