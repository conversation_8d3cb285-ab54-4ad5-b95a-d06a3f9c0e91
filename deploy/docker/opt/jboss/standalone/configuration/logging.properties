# Note this file has been generated and will be overwritten if a
# logging subsystem has been defined in the XML configuration.


# Additional loggers to configure (the root logger is always configured)
loggers=jacorb,com.arjuna,request,org.jboss.as.config,org.apache.tomcat.util.modeler,ksisoluc<PERSON>,sun.rmi,jacorb.config,org.jboss.as.ejb3.deployment.processors.EjbJndiBindingsDeploymentUnitProcessor,org.apache.camel.impl.DefaultCamelContext

logger.level=INFO
logger.handlers=CONSOLE,FILE

logger.jacorb.level=WARN
logger.jacorb.useParentHandlers=true

logger.com.arjuna.level=WARN
logger.com.arjuna.useParentHandlers=true

logger.request.level=INFO
logger.request.useParentHandlers=true
logger.request.handlers=FILE_REQUEST

logger.org.jboss.as.config.level=INFO
logger.org.jboss.as.config.useParentHandlers=true

logger.org.apache.tomcat.util.modeler.level=WARN
logger.org.apache.tomcat.util.modeler.useParentHandlers=true

logger.ksisolucoes.level=DEBUG
logger.ksisolucoes.useParentHandlers=true

logger.sun.rmi.level=WARN
logger.sun.rmi.useParentHandlers=true

logger.jacorb.config.level=ERROR
logger.jacorb.config.useParentHandlers=true

logger.org.jboss.as.ejb3.deployment.processors.EjbJndiBindingsDeploymentUnitProcessor.level=WARN
logger.org.jboss.as.ejb3.deployment.processors.EjbJndiBindingsDeploymentUnitProcessor.useParentHandlers=true

logger.org.apache.camel.impl.DefaultCamelContext.level=DEBUG
logger.org.apache.camel.impl.DefaultCamelContext.useParentHandlers=true

handler.CONSOLE=org.jboss.logmanager.handlers.ConsoleHandler
handler.CONSOLE.level=INFO
handler.CONSOLE.formatter=COLOR-PATTERN
handler.CONSOLE.properties=autoFlush,target,enabled
handler.CONSOLE.autoFlush=true
handler.CONSOLE.target=SYSTEM_OUT
handler.CONSOLE.enabled=true

handler.FILE=org.jboss.logmanager.handlers.PeriodicRotatingFileHandler
handler.FILE.level=ALL
handler.FILE.formatter=PATTERN
handler.FILE.properties=append,autoFlush,enabled,suffix,fileName
handler.FILE.constructorProperties=fileName,append
handler.FILE.append=true
handler.FILE.autoFlush=true
handler.FILE.enabled=true
handler.FILE.suffix=.yyyy-MM-dd
handler.FILE.fileName=/opt/wildfly-9.0.2.Final/standalone/log/server.log

handler.FILE_REQUEST=org.jboss.logmanager.handlers.PeriodicRotatingFileHandler
handler.FILE_REQUEST.level=ALL
handler.FILE_REQUEST.formatter=PATTERN
handler.FILE_REQUEST.properties=append,autoFlush,enabled,suffix,fileName
handler.FILE_REQUEST.append=true
handler.FILE_REQUEST.autoFlush=true
handler.FILE_REQUEST.enabled=true
handler.FILE_REQUEST.suffix=.yyyy-MM-dd
handler.FILE_REQUEST.fileName=/opt/wildfly-9.0.2.Final/standalone/log/request.log

formatter.PATTERN=org.jboss.logmanager.formatters.PatternFormatter
formatter.PATTERN.properties=pattern
formatter.PATTERN.pattern=%d{yyyy-MM-dd HH\:mm\:ss,SSS} %-5p [%c] (%t) %s%E%n

formatter.COLOR-PATTERN=org.jboss.logmanager.formatters.PatternFormatter
formatter.COLOR-PATTERN.properties=pattern
formatter.COLOR-PATTERN.pattern=%K{level}%d{HH\:mm\:ss,SSS} %-5p [%c] (%t) %s%E%n
